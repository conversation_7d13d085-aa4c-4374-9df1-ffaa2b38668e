#!/usr/bin/env python3
"""
Enhanced De<PERSON> Script - Complete ICICI to Gainsight Migration with LLM Integration
Demonstrates the full workflow including LLM-powered data transformation and API integration
"""

import asyncio
import json
import time
import logging
from pathlib import Path
from typing import Dict, List, Any
import requests

# Setup logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class ICICIGainsightMigrationDemo:
    """
    Complete demo of ICICI to Gainsight migration with LLM enhancement
    """
    
    def __init__(self):
        # Import configuration
        from config import config
        self.config = config
        
        # Paths
        self.data_dir = Path("/Users/<USER>/Desktop/wildweasel/Browser/data")
        self.icici_path = Path("/Users/<USER>/Desktop/totango/ICICI.json")
        self.id_mapping_path = Path("/Users/<USER>/Desktop/totango/ID.json")
        self.gainsight_template_path = Path("/Users/<USER>/Desktop/totango/Gainsight_payload.json")
        
        # Output files
        self.converted_path = self.data_dir / "icici_gainsight_ready.json"
        self.with_ids_path = self.data_dir / "icici_gainsight_with_ids.json"
        self.final_migration_path = self.data_dir / "icici_final_migration.json"
        
        # Demo mode settings
        self.demo_mode = True  # Set to False when you have Gainsight API key
        
    async def run_complete_demo(self):
        """Run the complete migration demo"""
        print("🚀 ICICI TO GAINSIGHT MIGRATION DEMO")
        print("=" * 60)
        
        try:
            # Step 1: Test LLM Integration
            await self.test_llm_integration()
            
            # Step 2: Load and analyze data
            await self.load_and_analyze_data()
            
            # Step 3: Enhanced data transformation with LLM
            await self.enhance_data_transformation()
            
            # Step 4: Simulate ID generation (or real if API key available)
            await self.handle_id_generation()
            
            # Step 5: Create browser automation script
            await self.create_browser_automation_script()
            
            # Step 6: Generate final summary
            self.generate_final_summary()
            
        except Exception as e:
            logger.error(f"Demo failed: {e}")
            print(f"❌ Demo failed: {e}")
    
    async def test_llm_integration(self):
        """Test our enhanced LLM client"""
        print("\n🤖 Testing Enhanced LLM Integration...")
        
        try:
            from enhanced_llm_client import EnhancedLLMClient
            
            async with EnhancedLLMClient() as client:
                # Test with a data migration specific prompt
                prompt = """
                You are helping with a data migration from ICICI system to Gainsight.
                
                Given this sample activity data structure:
                - meetingType: numerical ID that maps to activity types
                - activityType: category like "onboarding", "support"
                - subject: activity title
                - date: timestamp
                
                Suggest the best approach to ensure data quality during migration.
                """
                
                response = await client.complete(
                    prompt,
                    task_type="data_migration",
                    max_tokens=300
                )
                
                print(f"  ✅ LLM Response successful")
                print(f"  🤖 Model used: {response.model}")
                print(f"  🏢 Provider: {response.provider}")
                print(f"  ⏱️  Response time: {response.latency:.2f}s")
                print(f"  💡 LLM Suggestion: {response.content[:200]}...")
                
                return response.success
                
        except Exception as e:
            print(f"  ❌ LLM test failed: {e}")
            return False
    
    async def load_and_analyze_data(self):
        """Load and analyze the ICICI data"""
        print("\n📊 Loading and Analyzing ICICI Data...")
        
        # Load ICICI data
        with open(self.icici_path, 'r') as f:
            icici_data = json.load(f)
        
        # Load ID mapping
        with open(self.id_mapping_path, 'r') as f:
            id_mapping = json.load(f)
        
        # Load Gainsight template
        with open(self.gainsight_template_path, 'r') as f:
            gainsight_template = json.load(f)
        
        print(f"  ✅ ICICI records loaded: {len(icici_data)}")
        print(f"  ✅ ID mappings loaded: {len(id_mapping)}")
        print(f"  ✅ Gainsight template loaded")
        
        # Analyze activity types
        activity_types = {}
        meeting_types = {}
        
        for record in icici_data[:10]:  # Sample first 10
            activity_type = record.get('activityType', 'Unknown')
            meeting_type = record.get('meetingType', 'Unknown')
            
            activity_types[activity_type] = activity_types.get(activity_type, 0) + 1
            meeting_types[meeting_type] = meeting_types.get(meeting_type, 0) + 1
        
        print(f"  📈 Activity types found: {list(activity_types.keys())}")
        print(f"  📈 Meeting types found: {list(meeting_types.keys())}")
        
        return icici_data, id_mapping, gainsight_template
    
    async def enhance_data_transformation(self):
        """Use LLM to enhance data transformation logic"""
        print("\n🔄 Enhanced Data Transformation with LLM...")
        
        try:
            from enhanced_llm_client import EnhancedLLMClient
            
            # Load existing converted data
            if self.converted_path.exists():
                with open(self.converted_path, 'r') as f:
                    converted_data = json.load(f)
                
                print(f"  ✅ Found existing converted data: {len(converted_data)} records")
                
                # Use LLM to analyze and suggest improvements
                async with EnhancedLLMClient() as client:
                    sample_record = converted_data[0] if converted_data else {}
                    
                    analysis_prompt = f"""
                    Analyze this converted Gainsight activity record and suggest improvements:
                    
                    {json.dumps(sample_record, indent=2)}
                    
                    Focus on:
                    1. Data completeness
                    2. Field mapping accuracy
                    3. Potential data quality issues
                    
                    Provide specific recommendations.
                    """
                    
                    analysis = await client.complete(
                        analysis_prompt,
                        task_type="data_migration",
                        max_tokens=400
                    )
                    
                    print(f"  🤖 LLM Analysis: {analysis.content[:300]}...")
                    
                    # Save analysis to file
                    analysis_path = self.data_dir / "llm_data_analysis.txt"
                    with open(analysis_path, 'w') as f:
                        f.write(f"LLM Analysis ({analysis.model}):\n")
                        f.write("=" * 50 + "\n")
                        f.write(analysis.content)
                    
                    print(f"  💾 Analysis saved to: {analysis_path}")
                
                return converted_data
            else:
                print("  ⚠️  No converted data found. Run conversion first.")
                return []
                
        except Exception as e:
            print(f"  ❌ Enhanced transformation failed: {e}")
            return []
    
    async def handle_id_generation(self):
        """Handle ID generation (demo mode or real API)"""
        print("\n🆔 Handling Gainsight ID Generation...")
        
        # Check if we have converted data
        if not self.converted_path.exists():
            print("  ❌ No converted data found. Cannot proceed with ID generation.")
            return False
        
        with open(self.converted_path, 'r') as f:
            activities = json.load(f)
        
        if self.demo_mode:
            print("  🎭 Running in DEMO MODE - Generating mock IDs...")
            
            # Generate demo IDs
            import uuid
            updated_activities = []
            
            for i, activity in enumerate(activities):
                # Generate a realistic-looking Gainsight ID
                demo_id = f"draft_{str(uuid.uuid4())[:8]}"
                activity["id"] = demo_id
                updated_activities.append(activity)
                
                if i < 5:  # Show first 5
                    subject = activity.get("note", {}).get("subject", "No subject")[:30]
                    print(f"    ✅ {subject}... → ID: {demo_id}")
            
            # Save activities with demo IDs
            with open(self.with_ids_path, 'w') as f:
                json.dump(updated_activities, f, indent=2)
            
            print(f"  ✅ Demo IDs generated for {len(updated_activities)} activities")
            print(f"  💾 Saved to: {self.with_ids_path}")
            
            # Create final migration payload
            final_activities = [a for a in updated_activities if a.get("id")]
            
            with open(self.final_migration_path, 'w') as f:
                json.dump(final_activities, f, indent=2)
            
            print(f"  📦 Final migration payload created: {len(final_activities)} activities")
            
            return True
        else:
            print("  🔑 Real API mode - would call Gainsight drafts API")
            print("  ⚠️  Set demo_mode = False and provide Gainsight API key for real operation")
            return False
    
    async def create_browser_automation_script(self):
        """Create a browser automation script for UI-based migration"""
        print("\n🌐 Creating Browser Automation Script...")
        
        try:
            from enhanced_llm_client import EnhancedLLMClient
            
            async with EnhancedLLMClient() as client:
                prompt = """
                Create a Python Playwright script template for automating Gainsight activity creation through the UI.
                
                The script should:
                1. Login to Gainsight
                2. Navigate to activity creation page
                3. Fill in activity fields (subject, type, date, description)
                4. Handle dropdown selections for activity types
                5. Submit the form
                6. Handle success/error responses
                
                Make it modular and reusable for multiple activities.
                """
                
                script_response = await client.complete(
                    prompt,
                    task_type="coding",
                    max_tokens=800
                )
                
                # Save the generated script
                script_path = self.data_dir / "gainsight_browser_automation.py"
                
                script_content = f"""#!/usr/bin/env python3
'''
Generated Gainsight Browser Automation Script
Created by: {script_response.model}
'''

{script_response.content}

# Additional configuration and utilities would go here
"""
                
                with open(script_path, 'w') as f:
                    f.write(script_content)
                
                print(f"  ✅ Browser automation script generated")
                print(f"  🤖 Generated by: {script_response.model}")
                print(f"  💾 Saved to: {script_path}")
                
                return True
                
        except Exception as e:
            print(f"  ❌ Script generation failed: {e}")
            return False
    
    def generate_final_summary(self):
        """Generate final migration summary"""
        print("\n📋 MIGRATION SUMMARY")
        print("=" * 60)
        
        # Check file status
        files_status = {
            "Source Data": self.icici_path.exists(),
            "Converted Data": self.converted_path.exists(),
            "With IDs": self.with_ids_path.exists(),
            "Final Migration": self.final_migration_path.exists()
        }
        
        for name, exists in files_status.items():
            print(f"  {'✅' if exists else '❌'} {name}: {'Ready' if exists else 'Missing'}")
        
        # Count records if files exist
        if self.final_migration_path.exists():
            with open(self.final_migration_path, 'r') as f:
                final_data = json.load(f)
            print(f"\n📊 Migration Statistics:")
            print(f"  • Activities ready for migration: {len(final_data)}")
            print(f"  • Activities with valid IDs: {sum(1 for a in final_data if a.get('id'))}")
        
        # Next steps
        print(f"\n🚀 Next Steps:")
        if self.demo_mode:
            print("  1. 🔑 Obtain Gainsight API key")
            print("  2. 🔧 Set demo_mode = False in script")
            print("  3. 🆔 Generate real Gainsight draft IDs")
            print("  4. 📤 Post activities via API or UI automation")
        else:
            print("  1. 📤 Post to Gainsight API:")
            print("     python3 post_to_gainsight.py")
            print("  2. 🌐 Or use browser automation:")
            print("     python3 data/gainsight_browser_automation.py")
        
        print(f"\n💡 LLM Integration Benefits:")
        print("  ✅ Intelligent data analysis and validation")
        print("  ✅ Automated script generation")
        print("  ✅ Multi-model fallback for reliability")
        print("  ✅ Task-aware model selection")
        
        print(f"\n🎯 System Status: READY FOR DEPLOYMENT")

async def main():
    """Run the complete migration demo"""
    demo = ICICIGainsightMigrationDemo()
    await demo.run_complete_demo()

if __name__ == "__main__":
    asyncio.run(main())

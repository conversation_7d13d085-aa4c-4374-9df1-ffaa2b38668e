#!/usr/bin/env python3
"""
Test specifically for Gemini API to ensure it's working correctly
"""
import asyncio
import sys
from pathlib import Path

# Add current directory to path to import modules
sys.path.append(str(Path(__file__).parent))

from enhanced_llm_client import EnhancedLL<PERSON>lient
from config import config

async def test_gemini_directly():
    """Test Gemini API directly"""
    print("🧪 Testing Gemini API Directly")
    print("=" * 50)
    
    # Check Google API key
    if not config.llm.google_api_key or config.llm.google_api_key == "your_google_api_key_here":
        print("❌ No valid Google API key found")
        return False
    
    print(f"✅ Google API key: {config.llm.google_api_key[:10]}...")
    
    async with EnhancedLLMClient() as client:
        try:
            # Override the model selection to force Gemini usage
            original_get_model = config.get_model_for_task
            config.get_model_for_task = lambda task_type: "gemini-1.5-flash"
            
            print("\n🔧 Testing Gemini-1.5-Flash...")
            response = await client.complete(
                "Please respond with exactly: 'Hello from Gemini!'",
                task_type="general",
                max_tokens=50
            )
            
            print(f"Model: {response.model}")
            print(f"Provider: {response.provider}")
            print(f"Success: {response.success}")
            print(f"Latency: {response.latency:.2f}s")
            
            if response.success:
                print(f"✅ Response: {response.content}")
                if "gemini" in response.provider.lower() or "google" in response.provider.lower():
                    print("🎉 Successfully using Gemini API!")
                    return True
                else:
                    print(f"⚠️  Using {response.provider} instead of Gemini")
            else:
                print(f"❌ Error: {response.error}")
            
            # Restore original function
            config.get_model_for_task = original_get_model
            
        except Exception as e:
            print(f"❌ Exception: {e}")
            return False
    
    return False

async def test_manual_gemini_call():
    """Test Gemini API with manual call"""
    print("\n🔧 Testing manual Gemini API call...")
    
    async with EnhancedLLMClient() as client:
        try:
            # Test the _call_google method directly
            response = await client._call_google(
                "gemini-1.5-flash",
                "Say exactly: 'Direct Gemini test successful!'",
                100,
                0.1
            )
            
            print(f"✅ Direct Gemini call successful!")
            print(f"Response: {response['content']}")
            return True
            
        except Exception as e:
            print(f"❌ Direct Gemini call failed: {e}")
            return False

async def main():
    """Main test"""
    print("Testing Gemini API Integration")
    
    # Test 1: Direct Gemini API call
    test1 = await test_manual_gemini_call()
    
    # Test 2: Through the complete method
    test2 = await test_gemini_directly()
    
    if test1:
        print("\n✅ Gemini API is working! The fix was successful.")
        print("📝 Note: You may need to configure models to use Gemini by default.")
    elif test2:
        print("\n✅ Client is working but may not be using Gemini by default.")
    else:
        print("\n❌ Tests failed. Checking error details...")
    
    return test1 or test2

if __name__ == "__main__":
    result = asyncio.run(main())
    sys.exit(0 if result else 1)

#!/usr/bin/env python3
"""
Comprehensive Memory & Loop Testing Suite for Browser Automation Agent

This script tests:
- Memory persistence across sessions
- Multi-loop task execution
- Learning and adaptation
- Task replay with intelligent modifications
- Error recovery and self-healing
- Context awareness and pattern recognition
"""

import asyncio
import json
import time
import sys
import os
from pathlib import Path
from datetime import datetime

# Add the parent directory to the path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

class MemoryLoopTester:
    def __init__(self):
        self.test_results = {}
        self.start_time = None
        
    async def initialize(self):
        """Initialize the test system"""
        try:
            from orchestrator import orchestrator, execute_task, replay_task, get_status
            from advanced_orchestrator import advanced_orchestrator, execute_advanced_task, get_advanced_status
            
            self.orchestrator = orchestrator
            self.execute_task = execute_task
            self.replay_task = replay_task
            self.get_status = get_status
            
            # Enhanced capabilities
            self.advanced_orchestrator = advanced_orchestrator
            self.execute_advanced_task = execute_advanced_task
            self.get_advanced_status = get_advanced_status
            
            print("🚀 Initializing Browser Automation Agent...")
            success = await self.orchestrator.initialize()
            if success:
                print("✅ Standard system initialized successfully")
                
                # Initialize advanced system
                advanced_success = await self.advanced_orchestrator.initialize()
                if advanced_success:
                    print("✅ Advanced system initialized successfully")
                    return True
                else:
                    print("⚠️ Advanced system failed, using standard system")
                    return True
            else:
                print("❌ System initialization failed")
                return False
                
        except ImportError as e:
            print(f"❌ Import error: {e}")
            print("Please ensure all dependencies are installed")
            return False
    
    async def test_memory_persistence(self):
        """Test 1: Memory Persistence Across Multiple Interactions"""
        print("\n" + "="*60)
        print("🧠 TEST 1: MEMORY PERSISTENCE")
        print("Testing if the agent remembers information across multiple tasks")
        print("="*60)
        
        test_start = time.time()
        
        # Phase 1: Teaching the agent personal preferences
        print("\n🔸 Phase 1: Teaching the agent about user preferences")
        result1 = await self.execute_task(
            "Remember these preferences: I like simple, clean websites. I prefer dark themes. I work in AI research. My name is Alex.",
            context={"test": "memory_persistence", "phase": 1},
            learn_from_execution=True
        )
        
        if not result1["success"]:
            print("❌ Phase 1 failed")
            return {"success": False, "error": "Teaching phase failed"}
        
        print("✅ Phase 1 completed - Agent learned user preferences")
        
        # Phase 2: Test immediate recall
        print("\n🔸 Phase 2: Testing immediate memory recall")
        result2 = await self.execute_task(
            "What do you remember about my preferences and background?",
            context={"test": "memory_persistence", "phase": 2}
        )
        
        # Phase 3: Simulate session restart by getting fresh status
        print("\n🔸 Phase 3: Testing memory after 'session restart'")
        await asyncio.sleep(2)  # Brief pause to simulate time passage
        
        result3 = await self.execute_task(
            "Based on what you know about me, recommend a good website for AI research",
            context={"test": "memory_persistence", "phase": 3}
        )
        
        # Phase 4: Test memory evolution
        print("\n🔸 Phase 4: Testing memory evolution with new information")
        result4 = await self.execute_task(
            "I also prefer websites with minimal JavaScript and fast loading times. Update your knowledge about me.",
            context={"test": "memory_persistence", "phase": 4},
            learn_from_execution=True
        )
        
        result5 = await self.execute_task(
            "Now describe my complete profile with all preferences",
            context={"test": "memory_persistence", "phase": 5}
        )
        
        test_time = time.time() - test_start
        
        # Evaluate results
        success_count = sum(1 for r in [result1, result2, result3, result4, result5] if r["success"])
        success_rate = success_count / 5
        
        test_result = {
            "success": success_rate >= 0.8,
            "success_rate": success_rate,
            "total_phases": 5,
            "successful_phases": success_count,
            "execution_time": test_time,
            "phases": {
                "teaching": result1["success"],
                "immediate_recall": result2["success"],
                "persistent_memory": result3["success"],
                "memory_evolution": result4["success"],
                "complete_recall": result5["success"]
            }
        }
        
        if test_result["success"]:
            print(f"✅ MEMORY PERSISTENCE TEST PASSED ({success_rate:.1%} success rate)")
        else:
            print(f"❌ MEMORY PERSISTENCE TEST FAILED ({success_rate:.1%} success rate)")
        
        return test_result
    
    async def test_multi_loop_execution(self):
        """Test 2: Multi-Loop Task Execution with Context Awareness"""
        print("\n" + "="*60)
        print("🔄 TEST 2: MULTI-LOOP EXECUTION")
        print("Testing complex multi-step task execution with context awareness")
        print("="*60)
        
        test_start = time.time()
        
        # Complex multi-step research task
        research_task = """
        Perform a comprehensive research task:
        1. Go to httpbin.org and explore available endpoints
        2. Test the /json endpoint and analyze the response
        3. Check the /html endpoint and examine the structure
        4. Compare both endpoints and provide insights
        5. Remember your findings for future reference
        """
        
        print("🔸 Executing complex multi-loop research task...")
        print("Task:", research_task.strip())
        
        result = await self.execute_task(
            research_task,
            context={
                "test": "multi_loop_execution",
                "complexity": "high",
                "expected_loops": 5
            },
            learn_from_execution=True,
            record_session=True
        )
        
        # Follow-up task to test context retention
        print("\n🔸 Follow-up task testing context retention...")
        followup_result = await self.execute_task(
            "Based on your recent exploration of httpbin.org, which endpoint would be best for testing JSON APIs?",
            context={"test": "multi_loop_followup"}
        )
        
        test_time = time.time() - test_start
        
        test_result = {
            "success": result["success"] and followup_result["success"],
            "execution_time": test_time,
            "steps_taken": result.get("steps_taken", 0),
            "main_task_success": result["success"],
            "followup_success": followup_result["success"],
            "recording_path": result.get("recording_path"),
            "context_retention": followup_result["success"]
        }
        
        if test_result["success"]:
            print(f"✅ MULTI-LOOP EXECUTION TEST PASSED ({test_time:.1f}s)")
        else:
            print(f"❌ MULTI-LOOP EXECUTION TEST FAILED")
        
        return test_result
    
    async def test_learning_adaptation(self):
        """Test 3: Learning and Adaptation Over Time"""
        print("\n" + "="*60)
        print("🎯 TEST 3: LEARNING & ADAPTATION")
        print("Testing the agent's ability to learn and adapt from experiences")
        print("="*60)
        
        test_start = time.time()
        
        # First attempt - establish baseline
        print("🔸 Attempt 1: Baseline task execution")
        task_description = "Go to example.com and extract the page title and main heading"
        
        result1 = await self.execute_task(
            task_description,
            context={"test": "learning_adaptation", "attempt": 1},
            learn_from_execution=True,
            record_session=True
        )
        
        # Second attempt - should be faster/better due to learning
        print("🔸 Attempt 2: Testing improvement from learning")
        result2 = await self.execute_task(
            task_description,
            context={"test": "learning_adaptation", "attempt": 2},
            learn_from_execution=True
        )
        
        # Third attempt with variation - test adaptation
        print("🔸 Attempt 3: Testing adaptation to variation")
        result3 = await self.execute_task(
            "Go to example.com and also check if there are any links on the page",
            context={"test": "learning_adaptation", "attempt": 3},
            learn_from_execution=True
        )
        
        test_time = time.time() - test_start
        
        # Analyze improvement
        times = [r.get("execution_time", 0) for r in [result1, result2, result3]]
        improvement = times[0] > times[1] if len(times) >= 2 else False
        
        test_result = {
            "success": all(r["success"] for r in [result1, result2, result3]),
            "execution_times": times,
            "showed_improvement": improvement,
            "adaptation_success": result3["success"],
            "total_test_time": test_time,
            "attempts": {
                "baseline": result1["success"],
                "learned": result2["success"], 
                "adapted": result3["success"]
            }
        }
        
        if test_result["success"]:
            print(f"✅ LEARNING & ADAPTATION TEST PASSED")
            if improvement:
                print(f"🚀 Agent showed improvement: {times[0]:.1f}s → {times[1]:.1f}s")
        else:
            print("❌ LEARNING & ADAPTATION TEST FAILED")
        
        return test_result
    
    async def test_replay_intelligence(self):
        """Test 4: Intelligent Task Replay and Modification"""
        print("\n" + "="*60)
        print("📹 TEST 4: INTELLIGENT REPLAY")
        print("Testing task replay with intelligent adaptation")
        print("="*60)
        
        test_start = time.time()
        
        # Find a recent recording to replay
        recordings_dir = Path("data/recordings")
        if not recordings_dir.exists():
            print("❌ No recordings directory found")
            return {"success": False, "error": "No recordings available"}
        
        recordings = list(recordings_dir.glob("*.json"))
        if not recordings:
            print("❌ No recordings found")
            return {"success": False, "error": "No recordings to replay"}
        
        # Use the most recent recording
        latest_recording = max(recordings, key=lambda p: p.stat().st_mtime)
        print(f"🔸 Replaying session: {latest_recording.name}")
        
        try:
            replay_result = await self.replay_task(
                str(latest_recording),
                adapt_to_changes=True,
                learn_from_replay=True
            )
            
            test_time = time.time() - test_start
            
            test_result = {
                "success": replay_result.get("success", False),
                "recording_used": str(latest_recording),
                "execution_time": test_time,
                "replay_data": replay_result
            }
            
            if test_result["success"]:
                print("✅ INTELLIGENT REPLAY TEST PASSED")
            else:
                print("❌ INTELLIGENT REPLAY TEST FAILED")
                
        except Exception as e:
            print(f"❌ Replay failed: {e}")
            test_result = {"success": False, "error": str(e)}
        
        return test_result
    
    async def test_error_recovery(self):
        """Test 5: Error Recovery and Self-Healing"""
        print("\n" + "="*60)
        print("🛠️ TEST 5: ERROR RECOVERY & SELF-HEALING")
        print("Testing the agent's ability to recover from errors")
        print("="*60)
        
        test_start = time.time()
        
        # Test with invalid URL (should gracefully handle)
        print("🔸 Testing recovery from invalid URL...")
        result1 = await self.execute_task(
            "Go to invalid-website-that-does-not-exist.com and if that fails, go to example.com instead",
            context={"test": "error_recovery", "scenario": "invalid_url"}
        )
        
        # Test with impossible task (should adapt or explain why it can't be done)
        print("🔸 Testing graceful handling of impossible task...")
        result2 = await self.execute_task(
            "Go to example.com and change the website's source code to add a new paragraph",
            context={"test": "error_recovery", "scenario": "impossible_task"}
        )
        
        test_time = time.time() - test_start
        
        # Evaluate error handling
        handled_gracefully = not any("error" in str(r).lower() and "crash" in str(r).lower() 
                                   for r in [result1, result2])
        
        test_result = {
            "success": handled_gracefully,
            "execution_time": test_time,
            "scenarios": {
                "invalid_url": result1.get("success", False),
                "impossible_task": result2.get("success", False)
            },
            "graceful_handling": handled_gracefully,
            "results": [result1, result2]
        }
        
        if test_result["success"]:
            print("✅ ERROR RECOVERY TEST PASSED")
        else:
            print("❌ ERROR RECOVERY TEST FAILED")
        
        return test_result
    
    async def test_context_awareness(self):
        """Test 6: Context Awareness and Pattern Recognition"""
        print("\n" + "="*60)
        print("🎯 TEST 6: CONTEXT AWARENESS")
        print("Testing context retention and pattern recognition")
        print("="*60)
        
        test_start = time.time()
        
        # Set up context
        print("🔸 Setting up context...")
        setup_result = await self.execute_task(
            "I'm researching web development tools. Remember this context for our conversation.",
            context={"test": "context_awareness", "phase": "setup"}
        )
        
        # Test context usage in related task
        print("🔸 Testing context application...")
        context_result = await self.execute_task(
            "Find a good website for learning about web development",
            context={"test": "context_awareness", "phase": "application"}
        )
        
        # Test pattern recognition
        print("🔸 Testing pattern recognition...")
        pattern_result = await self.execute_task(
            "Based on our previous interactions, what type of resources do I typically look for?",
            context={"test": "context_awareness", "phase": "pattern_recognition"}
        )
        
        test_time = time.time() - test_start
        
        test_result = {
            "success": all(r["success"] for r in [setup_result, context_result, pattern_result]),
            "execution_time": test_time,
            "phases": {
                "context_setup": setup_result["success"],
                "context_application": context_result["success"],
                "pattern_recognition": pattern_result["success"]
            }
        }
        
        if test_result["success"]:
            print("✅ CONTEXT AWARENESS TEST PASSED")
        else:
            print("❌ CONTEXT AWARENESS TEST FAILED")
        
        return test_result
    
    async def test_advanced_multi_loop_execution(self):
        """Test 7: Advanced Multi-Loop Execution with Context Evolution"""
        print("\n" + "="*60)
        print("🚀 TEST 7: ADVANCED MULTI-LOOP EXECUTION")
        print("Testing enhanced multi-loop execution with intelligent context evolution")
        print("="*60)
        
        test_start = time.time()
        
        # Complex research task that requires multiple loops and context evolution
        advanced_research_task = """
        Perform comprehensive AI research with multiple phases:
        1. Search for "latest AI agent frameworks 2025"
        2. Visit top 3 most relevant results
        3. Extract key information from each site
        4. Compare frameworks and identify trends  
        5. Summarize findings with recommendations
        Remember all findings between phases and build comprehensive knowledge.
        """
        
        print("🔸 Executing advanced multi-loop research task...")
        print("Task:", advanced_research_task.strip())
        
        # Use advanced orchestrator with enhanced capabilities
        try:
            from advanced_orchestrator import TaskContext, TaskPriority
            
            context = TaskContext(
                priority=TaskPriority.HIGH,
                max_retries=2,
                timeout_seconds=600,  # 10 minutes
                tags=["research", "ai", "frameworks", "comprehensive"],
                metadata={
                    "test_type": "advanced_multi_loop",
                    "expected_phases": 5,
                    "complexity": "high"
                }
            )
            
            success_criteria = {
                "sites_visited": 3,
                "information_extracted": True,
                "comparison_completed": True
            }
            
            result = await self.execute_advanced_task(
                advanced_research_task,
                context=context,
                max_loops=8,
                success_criteria=success_criteria
            )
            
            test_time = time.time() - test_start
            
            test_result = {
                "success": result.status.value == "completed",
                "execution_time": test_time,
                "loops_executed": result.steps_taken,
                "performance_metrics": result.performance_metrics,
                "context_evolution": result.context_evolution,
                "recommendations": result.recommendations,
                "advanced_features_used": True
            }
            
            if test_result["success"]:
                print(f"✅ ADVANCED MULTI-LOOP EXECUTION TEST PASSED")
                print(f"🚀 Executed {result.steps_taken} loops in {test_time:.1f}s")
                if result.performance_metrics:
                    print(f"📊 Success rate: {result.performance_metrics.get('success_rate', 0):.1%}")
                    print(f"⚡ Avg loop time: {result.performance_metrics.get('average_loop_time', 0):.1f}s")
                if result.recommendations:
                    print("💡 Recommendations:")
                    for rec in result.recommendations[:3]:
                        print(f"   • {rec}")
            else:
                print(f"❌ ADVANCED MULTI-LOOP EXECUTION TEST FAILED")
                print(f"Error: {result.error}")
        
        except ImportError:
            # Fallback to regular execution if advanced orchestrator is not available
            print("🔸 Advanced orchestrator not available, using standard execution...")
            result = await self.execute_task(
                advanced_research_task,
                context={
                    "test": "advanced_multi_loop_fallback",
                    "complexity": "high"
                },
                learn_from_execution=True
            )
            
            test_time = time.time() - test_start
            
            test_result = {
                "success": result["success"],
                "execution_time": test_time,
                "loops_executed": result.get("steps_taken", 0),
                "advanced_features_used": False,
                "fallback_used": True
            }
            
            if test_result["success"]:
                print(f"✅ ADVANCED MULTI-LOOP EXECUTION TEST PASSED (Fallback)")
            else:
                print(f"❌ ADVANCED MULTI-LOOP EXECUTION TEST FAILED")
        
        return test_result
        """Test 6: Context Awareness and Pattern Recognition"""
        print("\n" + "="*60)
        print("🎯 TEST 6: CONTEXT AWARENESS")
        print("Testing context retention and pattern recognition")
        print("="*60)
        
        test_start = time.time()
        
        # Set up context
        print("🔸 Setting up context...")
        setup_result = await self.execute_task(
            "I'm researching web development tools. Remember this context for our conversation.",
            context={"test": "context_awareness", "phase": "setup"}
        )
        
        # Test context usage in related task
        print("🔸 Testing context application...")
        context_result = await self.execute_task(
            "Find a good website for learning about web development",
            context={"test": "context_awareness", "phase": "application"}
        )
        
        # Test pattern recognition
        print("🔸 Testing pattern recognition...")
        pattern_result = await self.execute_task(
            "Based on our previous interactions, what type of resources do I typically look for?",
            context={"test": "context_awareness", "phase": "pattern_recognition"}
        )
        
        test_time = time.time() - test_start
        
        test_result = {
            "success": all(r["success"] for r in [setup_result, context_result, pattern_result]),
            "execution_time": test_time,
            "phases": {
                "context_setup": setup_result["success"],
                "context_application": context_result["success"],
                "pattern_recognition": pattern_result["success"]
            }
        }
        
        if test_result["success"]:
            print("✅ CONTEXT AWARENESS TEST PASSED")
        else:
            print("❌ CONTEXT AWARENESS TEST FAILED")
        
        return test_result
    
    async def analyze_system_state(self):
        """Analyze the current system state and memory usage"""
        print("\n" + "="*60)
        print("📊 SYSTEM STATE ANALYSIS")
        print("="*60)
        
        try:
            status = await self.get_status()
            
            print("🔧 System Components:")
            print(f"   • Orchestrator: {'✅ Ready' if status['orchestrator']['initialized'] else '❌ Not Ready'}")
            print(f"   • Memory blocks: {status['memory_system']['memory_blocks_count']}")
            print(f"   • Total tasks: {status['browser_automation']['total_tasks']}")
            print(f"   • Success rate: {status['browser_automation']['success_rate']:.1%}")
            print(f"   • Session history: {status['orchestrator']['session_history_count']} entries")
            
            if status['memory_system']['memory_blocks']:
                print("\n🧠 Active Memory Blocks:")
                for block in status['memory_system']['memory_blocks']:
                    print(f"   • {block}")
            
            print(f"\n⚙️ Configuration:")
            print(f"   • Max steps per task: {status['configuration']['max_steps_per_task']}")
            print(f"   • Learning enabled: {status['configuration']['learning_enabled']}")
            print(f"   • Browser headless: {status['configuration']['browser_headless']}")
            
            # Check recordings
            recordings_dir = Path("data/recordings")
            if recordings_dir.exists():
                recordings = list(recordings_dir.glob("*.json"))
                print(f"\n📹 Recorded Sessions: {len(recordings)}")
                if recordings:
                    for recording in recordings[-3:]:
                        print(f"   • {recording.name}")
            
            return status
            
        except Exception as e:
            print(f"❌ Error analyzing system state: {e}")
            return None
    
    async def run_comprehensive_test(self):
        """Run all memory and loop tests"""
        print("🧪 COMPREHENSIVE MEMORY & LOOP TESTING SUITE")
        print("=" * 80)
        print("Testing advanced capabilities of the Browser Automation Agent")
        print("=" * 80)
        
        self.start_time = time.time()
        
        # Initialize system
        if not await self.initialize():
            print("❌ Failed to initialize system - aborting tests")
            return
        
        # Run all tests
        tests = [
            ("Memory Persistence", self.test_memory_persistence),
            ("Multi-Loop Execution", self.test_multi_loop_execution),
            ("Learning & Adaptation", self.test_learning_adaptation),
            ("Intelligent Replay", self.test_replay_intelligence),
            ("Error Recovery", self.test_error_recovery),
            ("Context Awareness", self.test_context_awareness),
            ("Advanced Multi-Loop", self.test_advanced_multi_loop_execution)
        ]
        
        results = {}
        
        for test_name, test_func in tests:
            try:
                print(f"\n⏳ Running {test_name} test...")
                result = await test_func()
                results[test_name] = result
                
                if result["success"]:
                    print(f"✅ {test_name}: PASSED")
                else:
                    print(f"❌ {test_name}: FAILED")
                    
            except Exception as e:
                print(f"❌ {test_name}: ERROR - {e}")
                results[test_name] = {"success": False, "error": str(e)}
        
        # System state analysis
        await self.analyze_system_state()
        
        # Final summary
        total_time = time.time() - self.start_time
        passed_tests = sum(1 for r in results.values() if r.get("success", False))
        total_tests = len(results)
        success_rate = passed_tests / total_tests
        
        print("\n" + "=" * 80)
        print("📈 FINAL TEST RESULTS")
        print("=" * 80)
        
        for test_name, result in results.items():
            status = "✅ PASS" if result.get("success", False) else "❌ FAIL"
            exec_time = result.get("execution_time", 0)
            print(f"{test_name:<25} {status} ({exec_time:.1f}s)")
        
        print(f"\n📊 Summary:")
        print(f"   • Tests passed: {passed_tests}/{total_tests} ({success_rate:.1%})")
        print(f"   • Total execution time: {total_time:.1f} seconds")
        print(f"   • System status: {'✅ Operational' if success_rate >= 0.5 else '⚠️ Issues detected'}")
        
        if success_rate >= 0.8:
            print("\n🎉 EXCELLENT! System is performing at high level")
        elif success_rate >= 0.6:
            print("\n👍 GOOD! System is working well with minor issues")
        elif success_rate >= 0.4:
            print("\n⚠️ MODERATE! System has some functionality but needs attention")
        else:
            print("\n❌ POOR! System needs significant fixes")
        
        print("\n🔍 Detailed results saved for analysis")
        
        # Save detailed results
        results_file = Path("test_results_memory_loops.json")
        with open(results_file, 'w') as f:
            json.dump({
                "timestamp": datetime.now().isoformat(),
                "total_time": total_time,
                "success_rate": success_rate,
                "results": results
            }, f, indent=2, default=str)
        
        print(f"📄 Detailed results: {results_file}")
        
        # Cleanup
        try:
            await self.orchestrator.cleanup()
        except:
            pass

async def main():
    """Main entry point"""
    tester = MemoryLoopTester()
    
    try:
        await tester.run_comprehensive_test()
    except KeyboardInterrupt:
        print("\n👋 Tests interrupted by user")
    except Exception as e:
        print(f"\n❌ Test suite error: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(main())

"""
Master orchestrator with LangGraph workflow integration, Browser-Use, and Letta memory
"""
import asyncio
import logging
from typing import Dict, List, Optional, Any
from datetime import datetime
import json

# LangGraph integration
try:
    from langgraph.graph import StateGraph, END
    from langgraph.checkpoint.sqlite import SqliteSaver
    from typing_extensions import TypedDict
    LANGGRAPH_AVAILABLE = True
    print("✅ LangGraph imported successfully")
except ImportError:
    try:
        from langgraph import StateGraph, END
        from langgraph.checkpoint.sqlite import SqliteSaver
        from typing_extensions import TypedDict
        LANGGRAPH_AVAILABLE = True
        print("✅ LangGraph imported successfully (fallback path)")
    except ImportError as e:
        print(f"LangGraph not available: {e}")
        LANGGRAPH_AVAILABLE = False
        # Create dummy classes for fallback
        class StateGraph:
            def __init__(self, *args, **kwargs): pass
            def add_node(self, *args, **kwargs): pass
            def add_edge(self, *args, **kwargs): pass
            def add_conditional_edges(self, *args, **kwargs): pass
            def set_entry_point(self, *args, **kwargs): pass
            def compile(self, *args, **kwargs): return self
            async def ainvoke(self, *args, **kwargs): return {}
        class SqliteSaver:
            def __init__(self, *args, **kwargs): pass
            @classmethod
            def from_conn_string(cls, *args, **kwargs): return cls()
        TypedDict = dict  # Fallback for TypedDict
        END = "END"

# Import status tracking
try:
    from browser_automation.enhanced_agent import create_enhanced_agent, execute_task_with_optimization
    BROWSER_USE_AVAILABLE = True
    print("✅ Enhanced browser agent imported successfully")
except ImportError as e:
    print(f"Enhanced browser agent not available: {e}")
    BROWSER_USE_AVAILABLE = False
    
    # Create fallback functions
    def create_enhanced_agent(*args, **kwargs):
        return None
    
    async def execute_task_with_optimization(*args, **kwargs):
        return {"success": False, "error": "Enhanced browser agent not available"}

try:
    from memory_system.memory_manager import memory_manager
    MEMORY_AVAILABLE = True
    print("✅ Memory manager imported successfully")
except ImportError as e:
    print(f"Memory manager not available: {e}")
    MEMORY_AVAILABLE = False
    
    # Create fallback memory manager
    class FallbackMemoryManager:
        async def initialize(self): return True
        async def get_relevant_context(self, *args, **kwargs): return {}
        async def store_task_result(self, *args, **kwargs): pass
        async def consolidate_memory(self): pass
        async def export_memory(self, *args, **kwargs): pass
        async def cleanup(self): pass
    
    memory_manager = FallbackMemoryManager()
from config import config

# Set up logging
logging.basicConfig(
    level=getattr(logging, config.logging.log_level),
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler(config.logging.log_file),
        logging.StreamHandler()
    ]
)

logger = logging.getLogger(__name__)

# LangGraph state definition
class AgentState(TypedDict):
    """State schema for the browser automation workflow"""
    task: str
    context: Dict[str, Any]
    memory_context: Dict[str, Any]
    action_history: List[Dict[str, Any]]
    current_page: str
    memory_updates: List[Dict[str, Any]]
    execution_method: str
    result: Optional[Dict[str, Any]]
    error: Optional[str]
    step_count: int
    max_steps: int

class WorkflowManager:
    """
    LangGraph-based workflow manager for orchestrating browser automation tasks
    """
    
    def __init__(self):
        self.workflow = None
        self.checkpointer = None
        self.workflow_enabled = LANGGRAPH_AVAILABLE and config.workflow.enable_langgraph
        
    async def initialize(self) -> bool:
        """Initialize the LangGraph workflow"""
        if not self.workflow_enabled:
            logger.info("LangGraph workflow disabled or not available")
            return True  # Not an error, just disabled
        
        try:
            # Initialize checkpointer for workflow persistence
            if config.workflow.enable_checkpoints:
                checkpoint_path = f"{config.workflow.workflow_state_path}/checkpoints.db"
                self.checkpointer = SqliteSaver.from_conn_string(checkpoint_path)
            
            # Create the workflow graph
            self.workflow = self._create_workflow_graph()
            
            logger.info("✅ LangGraph workflow initialized")
            return True
            
        except Exception as e:
            logger.error(f"Failed to initialize LangGraph workflow: {e}")
            self.workflow_enabled = False
            return False
    
    def _create_workflow_graph(self) -> StateGraph:
        """Create the LangGraph workflow for browser automation"""
        workflow = StateGraph(AgentState)
        
        # Define workflow nodes
        workflow.add_node("memory_recall", self._memory_recall_node)
        workflow.add_node("task_planning", self._task_planning_node)
        workflow.add_node("browser_action", self._browser_action_node)
        workflow.add_node("result_evaluation", self._result_evaluation_node)
        workflow.add_node("memory_update", self._memory_update_node)
        workflow.add_node("error_recovery", self._error_recovery_node)
        
        # Define workflow edges
        workflow.set_entry_point("memory_recall")
        workflow.add_edge("memory_recall", "task_planning")
        workflow.add_edge("task_planning", "browser_action")
        workflow.add_edge("browser_action", "result_evaluation")
        
        # Conditional routing from result evaluation
        workflow.add_conditional_edges(
            "result_evaluation",
            self._should_continue_or_finish,
            {
                "continue": "browser_action",
                "error_recovery": "error_recovery", 
                "memory_update": "memory_update",
                "finish": END
            }
        )
        
        workflow.add_edge("error_recovery", "browser_action")
        workflow.add_edge("memory_update", END)
        
        # Compile with checkpointer if enabled
        if self.checkpointer:
            return workflow.compile(checkpointer=self.checkpointer)
        else:
            return workflow.compile()
    
    async def _memory_recall_node(self, state: AgentState) -> AgentState:
        """Node: Recall relevant memory context"""
        try:
            logger.debug("🧠 Memory recall node")
            
            # Get relevant context from memory
            memory_context = await memory_manager.get_relevant_context(
                state["task"], 
                max_items=config.memory.memory_search_results
            )
            
            state["memory_context"] = memory_context
            return state
            
        except Exception as e:
            logger.error(f"Memory recall failed: {e}")
            state["error"] = f"Memory recall error: {e}"
            return state
    
    async def _task_planning_node(self, state: AgentState) -> AgentState:
        """Node: Plan task execution strategy"""
        try:
            logger.debug("📋 Task planning node")
            
            # Enhance context with memory
            enhanced_context = state["context"].copy()
            enhanced_context["memory_context"] = state["memory_context"]
            
            # Determine execution method based on task and memory
            execution_method = await self._determine_execution_method(state["task"], enhanced_context)
            state["execution_method"] = execution_method
            
            # Update context
            state["context"] = enhanced_context
            
            return state
            
        except Exception as e:
            logger.error(f"Task planning failed: {e}")
            state["error"] = f"Planning error: {e}"
            return state
    
    async def _browser_action_node(self, state: AgentState) -> AgentState:
        """Node: Execute browser automation action"""
        try:
            logger.debug(f"🌐 Browser action node (method: {state.get('execution_method', 'unknown')})")
            
            # Execute using enhanced agent with Browser-Use integration
            result = await execute_task_with_optimization(
                state["task"],
                memory_manager=memory_manager,
                context=state["context"]
            )
            
            state["result"] = result
            state["step_count"] = state.get("step_count", 0) + 1
            
            # Update action history
            if "action_history" not in state:
                state["action_history"] = []
            
            state["action_history"].append({
                "step": state["step_count"],
                "method": state.get("execution_method", "unknown"),
                "success": result.get("success", False),
                "timestamp": datetime.now().isoformat()
            })
            
            return state
            
        except Exception as e:
            logger.error(f"Browser action failed: {e}")
            state["error"] = f"Browser action error: {e}"
            state["result"] = {"success": False, "error": str(e)}
            return state
    
    async def _result_evaluation_node(self, state: AgentState) -> AgentState:
        """Node: Evaluate action results and determine next steps"""
        try:
            logger.debug("📊 Result evaluation node")
            
            result = state.get("result", {})
            
            # Evaluate success
            if result.get("success"):
                state["memory_updates"] = [{
                    "type": "success",
                    "task": state["task"],
                    "result": result,
                    "timestamp": datetime.now().isoformat()
                }]
            else:
                # Check if we should retry or recover
                if state.get("step_count", 0) < state.get("max_steps", config.workflow.max_workflow_steps):
                    state["memory_updates"] = [{
                        "type": "retry_needed",
                        "error": result.get("error", "Unknown error"),
                        "step": state.get("step_count", 0)
                    }]
                else:
                    state["memory_updates"] = [{
                        "type": "failure", 
                        "task": state["task"],
                        "error": result.get("error", "Max steps exceeded"),
                        "timestamp": datetime.now().isoformat()
                    }]
            
            return state
            
        except Exception as e:
            logger.error(f"Result evaluation failed: {e}")
            state["error"] = f"Evaluation error: {e}"
            return state
    
    async def _memory_update_node(self, state: AgentState) -> AgentState:
        """Node: Update memory with results and learnings"""
        try:
            logger.debug("💾 Memory update node")
            
            result = state.get("result", {})
            
            # Store task result in memory
            await memory_manager.store_task_result(
                state["task"],
                result,
                result.get("success", False)
            )
            
            # Store any additional learning data
            for update in state.get("memory_updates", []):
                if update["type"] == "success":
                    # Could store success patterns
                    pass
                elif update["type"] == "failure":
                    # Could store error patterns
                    pass
            
            return state
            
        except Exception as e:
            logger.error(f"Memory update failed: {e}")
            state["error"] = f"Memory update error: {e}"
            return state
    
    async def _error_recovery_node(self, state: AgentState) -> AgentState:
        """Node: Attempt error recovery"""
        try:
            logger.debug("🔧 Error recovery node")
            
            # Simple recovery strategy: reset error and try different method
            state["error"] = None
            
            # Switch execution method if possible
            current_method = state.get("execution_method", "browser-use")
            if current_method == "browser-use":
                state["execution_method"] = "legacy"
            else:
                state["execution_method"] = "browser-use"
            
            logger.info(f"🔄 Switching to {state['execution_method']} method for recovery")
            
            return state
            
        except Exception as e:
            logger.error(f"Error recovery failed: {e}")
            state["error"] = f"Recovery error: {e}"
            return state
    
    def _should_continue_or_finish(self, state: AgentState) -> str:
        """Conditional routing logic"""
        result = state.get("result", {})
        step_count = state.get("step_count", 0)
        max_steps = state.get("max_steps", config.workflow.max_workflow_steps)
        
        # If there's an error and we haven't exceeded max steps, try recovery
        if state.get("error") and step_count < max_steps:
            return "error_recovery"
        
        # If successful, update memory and finish
        if result.get("success"):
            return "memory_update"
        
        # If failed but haven't exceeded max steps, continue trying
        if not result.get("success") and step_count < max_steps:
            return "continue"
        
        # Otherwise, update memory and finish (with failure)
        return "memory_update"
    
    async def _determine_execution_method(self, task: str, context: Dict[str, Any]) -> str:
        """Determine the best execution method for the task"""
        # Simple logic - can be enhanced with ML/heuristics
        if config.browser.enable_browser_use:
            return "browser-use"
        else:
            return "legacy"
    
    async def execute_workflow(self, task: str, context: Dict[str, Any]) -> Dict[str, Any]:
        """Execute the complete workflow"""
        if not self.workflow_enabled or not self.workflow:
            # Fallback to direct execution
            return await execute_task_with_optimization(task, memory_manager=memory_manager, context=context)
        
        try:
            # Initialize workflow state
            initial_state: AgentState = {
                "task": task,
                "context": context,
                "memory_context": {},
                "action_history": [],
                "current_page": "",
                "memory_updates": [],
                "execution_method": "browser-use",
                "result": None,
                "error": None,
                "step_count": 0,
                "max_steps": config.workflow.max_workflow_steps
            }
            
            # Execute workflow
            logger.info(f"🚀 Starting LangGraph workflow for task: {task}")
            
            final_state = await self.workflow.ainvoke(
                initial_state,
                config={"thread_id": f"task_{datetime.now().strftime('%Y%m%d_%H%M%S')}"}
            )
            
            # Extract result
            result = final_state.get("result", {})
            result["workflow_executed"] = True
            result["total_workflow_steps"] = final_state.get("step_count", 0)
            result["action_history"] = final_state.get("action_history", [])
            
            logger.info(f"✅ Workflow completed in {final_state.get('step_count', 0)} steps")
            
            return result
            
        except Exception as e:
            logger.error(f"Workflow execution failed: {e}")
            return {
                "success": False,
                "error": f"Workflow error: {e}",
                "workflow_executed": False
            }

class MasterOrchestrator:
    """
    Enhanced master orchestrator with LangGraph workflows, Browser-Use, and Letta memory
    """
    
    def __init__(self):
        self.initialized = False
        self.task_queue = []
        self.active_tasks = {}
        self.session_history = []
        
        # Enhanced components
        self.workflow_manager = WorkflowManager()
        self.enhanced_agent = None
        
    async def initialize(self) -> bool:
        """Initialize all components with enhanced integrations"""
        try:
            logger.info("🚀 Initializing Enhanced Master Orchestrator...")
            
            # Initialize memory system (Letta + fallback)
            memory_success = await memory_manager.initialize()
            if not memory_success:
                logger.error("Failed to initialize memory system")
                return False
            
            # Initialize workflow manager (LangGraph)
            workflow_success = await self.workflow_manager.initialize()
            if not workflow_success:
                logger.warning("Workflow manager initialization failed, continuing without LangGraph")
            
            # Initialize enhanced browser agent (Browser-Use + legacy)
            self.enhanced_agent = create_enhanced_agent(memory_manager)
            if self.enhanced_agent:
                agent_success = await self.enhanced_agent.initialize()
                if not agent_success:
                    logger.warning("Enhanced agent initialization failed")
            
            self.initialized = True
            
            # Log integration status
            integration_status = config.get_integration_status()
            logger.info("🎯 Integration Status:")
            for integration, enabled in integration_status.items():
                status = "✅" if enabled else "❌"
                logger.info(f"   {status} {integration}")
            
            logger.info("✅ Enhanced Master Orchestrator initialized successfully")
            return True
            
        except Exception as e:
            logger.error(f"Failed to initialize Enhanced Master Orchestrator: {e}")
            return False
    
    async def execute_task(self, 
                          task_description: str,
                          context: Optional[Dict[str, Any]] = None,
                          learn_from_execution: bool = True,
                          record_session: bool = True,
                          use_workflow: bool = True) -> Dict[str, Any]:
        """
        Execute a browser automation task with enhanced workflow orchestration
        """
        if not self.initialized:
            raise RuntimeError("Orchestrator not initialized. Call initialize() first.")
        
        logger.info(f"🚀 Executing enhanced task: {task_description}")
        
        try:
            # Prepare full context
            full_context = {
                "user_context": context or {},
                "task_metadata": {
                    "timestamp": datetime.now().isoformat(),
                    "session_id": f"session_{datetime.now().strftime('%Y%m%d_%H%M%S')}",
                    "learn_from_execution": learn_from_execution,
                    "use_workflow": use_workflow
                }
            }
            
            # Choose execution method
            if use_workflow and self.workflow_manager.workflow_enabled:
                # Use LangGraph workflow orchestration
                logger.info("🔄 Using LangGraph workflow orchestration")
                result = await self.workflow_manager.execute_workflow(task_description, full_context)
            else:
                # Direct execution with enhanced agent
                logger.info("⚡ Using direct enhanced execution")
                result = await execute_task_with_optimization(
                    task_description,
                    memory_manager=memory_manager,
                    context=full_context
                )
            
            # Post-process results
            if learn_from_execution:
                await self._learn_from_execution(task_description, result, full_context)
            
            # Add to session history
            self.session_history.append({
                "task": task_description,
                "result": result,
                "timestamp": datetime.now().isoformat(),
                "execution_method": result.get("execution_method", "unknown"),
                "workflow_used": use_workflow and self.workflow_manager.workflow_enabled
            })
            
            # Trigger memory consolidation if needed
            if len(self.session_history) % config.memory.memory_consolidation_interval == 0:
                await self._trigger_memory_consolidation()
            
            success = result.get("success", False)
            method = result.get("execution_method", "unknown")
            logger.info(f"{'✅' if success else '❌'} Task completed using {method}")
            
            return result
            
        except Exception as e:
            logger.error(f"Enhanced task execution failed: {e}")
            
            # Create failure result
            failure_result = {
                "success": False,
                "error": str(e),
                "task_description": task_description,
                "timestamp": datetime.now().isoformat(),
                "execution_method": "failed"
            }
            
            # Learn from failure if enabled
            if learn_from_execution:
                await self._learn_from_failure(task_description, failure_result, str(e))
            
            return failure_result
    
    async def replay_recorded_task(self, 
                                  recording_path: str,
                                  adapt_to_changes: bool = True,
                                  learn_from_replay: bool = True) -> Dict[str, Any]:
        """
        Replay a previously recorded task with intelligent adaptation
        """
        if not self.initialized:
            raise RuntimeError("Orchestrator not initialized. Call initialize() first.")
        
        logger.info(f"Replaying recorded task from: {recording_path}")
        
        try:
            # For now, return a placeholder result since browser_agent is not available
            # This would need to be implemented with the enhanced agent
            replay_result = {
                "success": False,
                "error": "Replay functionality not yet implemented with enhanced agent",
                "recording_path": recording_path
            }
            
            # Learn from replay if enabled
            if learn_from_replay:
                await self._learn_from_replay(replay_result)
            
            logger.info(f"Task replay completed")
            return replay_result
            
        except Exception as e:
            logger.error(f"Task replay failed: {e}")
            return {"success": False, "error": str(e)}
    
    async def get_task_suggestions(self, partial_description: str, max_suggestions: int = 5) -> List[Dict[str, Any]]:
        """
        Get task suggestions based on memory and past successful tasks
        """
        try:
            # Get memory context
            memory_context = await memory_manager.get_relevant_context(partial_description)
            
            suggestions = []
            
            # Extract suggestions from similar tasks
            similar_tasks = memory_context.get("similar_tasks", [])
            for task in similar_tasks[:max_suggestions]:
                suggestions.append({
                    "suggestion": task["task"],
                    "confidence": 0.8,  # Could be calculated based on similarity
                    "success_rate": 1.0 if task["success"] else 0.0,
                    "last_executed": task.get("timestamp", "unknown")
                })
            
            # Extract suggestions from success strategies
            strategies = memory_context.get("success_strategies", [])
            for strategy in strategies[:max_suggestions - len(suggestions)]:
                suggestions.append({
                    "suggestion": f"Use strategy: {strategy}",
                    "confidence": 0.6,
                    "type": "strategy"
                })
            
            return suggestions[:max_suggestions]
            
        except Exception as e:
            logger.error(f"Failed to get task suggestions: {e}")
            return []
    
    async def generate_playwright_script(self, session_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Generate Playwright script from a successful session
        """
        if not self.initialized:
            raise RuntimeError("Orchestrator not initialized. Call initialize() first.")
        
        # Placeholder implementation - would integrate with enhanced agent
        return {
            "success": False,
            "error": "Playwright script generation not yet implemented with enhanced agent",
            "session_data": session_data
        }
    
    async def optimize_task_patterns(self, sessions: List[Dict[str, Any]]) -> Dict[str, Any]:
        """
        Analyze multiple sessions to discover and optimize reusable patterns
        """
        if not self.initialized:
            raise RuntimeError("Orchestrator not initialized. Call initialize() first.")
        
        # Placeholder implementation - would integrate with enhanced agent
        return {
            "success": False,
            "error": "Pattern optimization not yet implemented with enhanced agent",
            "sessions_analyzed": len(sessions)
        }
    
    def get_playwright_performance_stats(self) -> Dict[str, Any]:
        """
        Get Playwright integration performance statistics
        """
        if not self.initialized:
            return {"error": "Orchestrator not initialized"}
        
        # Placeholder implementation - would get stats from enhanced agent
        return {
            "playwright_executions": 0,
            "token_savings": 0,
            "success_rate": 0.0,
            "note": "Enhanced agent integration in progress"
        }

    async def get_system_status(self) -> Dict[str, Any]:
        """Get comprehensive system status with all integrations"""
        try:
            # Get integration status
            integration_status = config.get_integration_status()
            
            # Get enhanced agent statistics
            enhanced_stats = {}
            if self.enhanced_agent:
                try:
                    enhanced_stats = await self.enhanced_agent.get_optimization_statistics() if hasattr(self.enhanced_agent, 'get_optimization_statistics') else {}
                except:
                    enhanced_stats = {"error": "Could not retrieve enhanced agent stats"}
            
            # Get memory status
            memory_status = {"initialized": bool(memory_manager)}
            
            # Get workflow status  
            workflow_status = {
                "enabled": self.workflow_manager.workflow_enabled,
                "initialized": bool(self.workflow_manager.workflow),
                "checkpoints_enabled": config.workflow.enable_checkpoints
            }
            
            status = {
                "orchestrator": {
                    "initialized": self.initialized,
                    "task_queue_size": len(self.task_queue),
                    "active_tasks": len(self.active_tasks),
                    "session_history_count": len(self.session_history)
                },
                "integrations": integration_status,
                "browser_automation": {
                    "enhanced_agent_available": bool(self.enhanced_agent),
                    "browser_use_config": config.get_browser_use_config(),
                    "enhanced_stats": enhanced_stats
                },
                "workflow_orchestration": workflow_status,
                "memory_system": {
                    "status": memory_status,
                    "letta_config": config.get_letta_config(),
                    "type": "letta" if config.memory.letta_enabled else "fallback"
                },
                "configuration": {
                    "max_workflow_steps": config.workflow.max_workflow_steps,
                    "memory_consolidation_interval": config.memory.memory_consolidation_interval,
                    "browser_headless": config.browser.headless,
                    "learning_enabled": config.agent.enable_memory
                },
                "timestamp": datetime.now().isoformat()
            }
            
            return status
            
        except Exception as e:
            logger.error(f"Failed to get enhanced system status: {e}")
            return {"error": str(e), "timestamp": datetime.now().isoformat()}
    
    async def export_system_data(self, export_path: str) -> bool:
        """Export all system data for backup or analysis"""
        try:
            # Get system status
            status = await self.get_system_status()
            
            # Export data
            export_data = {
                "export_timestamp": datetime.now().isoformat(),
                "system_status": status,
                "session_history": self.session_history,
                "configuration": {
                    "llm": config.llm.__dict__,
                    "memory": config.memory.__dict__,
                    "browser": config.browser.__dict__,
                    "agent": config.agent.__dict__
                }
            }
            
            # Save to file
            with open(export_path, 'w') as f:
                json.dump(export_data, f, indent=2, default=str)
            
            # Also export memory separately
            memory_export_path = export_path.replace('.json', '_memory.json')
            await memory_manager.export_memory(memory_export_path)
            
            logger.info(f"System data exported to: {export_path}")
            return True
            
        except Exception as e:
            logger.error(f"Failed to export system data: {e}")
            return False
    
    async def _learn_from_execution(self, task_description: str, result: Dict[str, Any], context: Dict[str, Any]):
        """Learn from task execution results"""
        try:
            # Store the task result in memory (already done by browser_agent)
            
            # Extract additional learning opportunities
            if result.get("success"):
                # Analyze successful strategies
                strategies = result.get("strategies", [])
                if strategies:
                    logger.debug(f"Learning successful strategies: {strategies}")
                
                # Store website knowledge if applicable
                final_url = result.get("final_url")
                if final_url:
                    # Additional website knowledge could be stored here
                    pass
            
            logger.debug(f"Learning completed for task: {task_description}")
            
        except Exception as e:
            logger.error(f"Failed to learn from execution: {e}")
    
    async def _learn_from_failure(self, task_description: str, result: Dict[str, Any], error: str):
        """Learn from task execution failures"""
        try:
            # Store error pattern (already done by browser_agent)
            
            # Additional failure analysis could be done here
            logger.debug(f"Learning from failure: {task_description} - {error}")
            
        except Exception as e:
            logger.error(f"Failed to learn from failure: {e}")
    
    async def _learn_from_replay(self, replay_result: Dict[str, Any]):
        """Learn from task replay results"""
        try:
            success_rate = replay_result.get("success_rate", 0)
            
            if success_rate < 0.8:  # If replay success rate is low
                # This indicates that the original recording may not adapt well
                # Could trigger additional learning or strategy refinement
                logger.info(f"Low replay success rate ({success_rate:.2%}) - triggering adaptation learning")
            
            logger.debug("Learning from replay completed")
            
        except Exception as e:
            logger.error(f"Failed to learn from replay: {e}")
    
    async def _trigger_memory_consolidation(self):
        """Trigger memory consolidation process"""
        try:
            logger.info("Triggering memory consolidation...")
            await memory_manager.consolidate_memory()
            logger.info("Memory consolidation completed")
            
        except Exception as e:
            logger.error(f"Memory consolidation failed: {e}")
    
    async def cleanup(self):
        """Enhanced cleanup with all integrations"""
        try:
            logger.info("🧹 Cleaning up Enhanced Master Orchestrator...")
            
            # Cleanup enhanced agent
            if self.enhanced_agent:
                try:
                    if hasattr(self.enhanced_agent, 'cleanup'):
                        await self.enhanced_agent.cleanup()
                except Exception as e:
                    logger.error(f"Enhanced agent cleanup error: {e}")
            
            # Cleanup memory manager  
            await memory_manager.cleanup()
            
            # Cleanup workflow manager
            if self.workflow_manager:
                try:
                    if hasattr(self.workflow_manager, 'cleanup'):
                        await self.workflow_manager.cleanup()
                except Exception as e:
                    logger.error(f"Workflow manager cleanup error: {e}")
            
            # Clear local state
            self.task_queue.clear()
            self.active_tasks.clear()
            self.session_history.clear()
            
            self.initialized = False
            logger.info("✅ Enhanced Master Orchestrator cleanup completed")
            
        except Exception as e:
            logger.error(f"Error during enhanced cleanup: {e}")

# Global enhanced orchestrator instance
orchestrator = MasterOrchestrator()

# Enhanced convenience functions
async def execute_task(task_description: str, **kwargs) -> Dict[str, Any]:
    """Enhanced convenience function to execute a task"""
    if not orchestrator.initialized:
        await orchestrator.initialize()
    return await orchestrator.execute_task(task_description, **kwargs)

async def execute_intelligent_task(task_description: str, **kwargs) -> Dict[str, Any]:
    """Execute task with intelligent method selection"""
    if not orchestrator.initialized:
        await orchestrator.initialize()
    
    # Use workflow orchestration by default
    kwargs.setdefault('use_workflow', True)
    return await orchestrator.execute_task(task_description, **kwargs)

async def replay_task(recording_path: str, **kwargs) -> Dict[str, Any]:
    """Enhanced convenience function to replay a task"""
    if not orchestrator.initialized:
        await orchestrator.initialize()
    return await orchestrator.replay_recorded_task(recording_path, **kwargs)

async def get_status() -> Dict[str, Any]:
    """Enhanced convenience function to get system status"""
    if not orchestrator.initialized:
        await orchestrator.initialize()
    return await orchestrator.get_system_status()

async def get_integration_status() -> Dict[str, Any]:
    """Get detailed integration status"""
    return {
        "config": config.get_integration_status(),
        "runtime": {
            "browser_use_available": BROWSER_USE_AVAILABLE,
            "langgraph_available": LANGGRAPH_AVAILABLE,
            "memory_available": MEMORY_AVAILABLE,
            "orchestrator_initialized": orchestrator.initialized,
            "workflow_enabled": orchestrator.workflow_manager.workflow_enabled if orchestrator.workflow_manager else False
        },
        "components": {
            "enhanced_agent": bool(orchestrator.enhanced_agent),
            "workflow_manager": bool(orchestrator.workflow_manager),
            "memory_manager": bool(memory_manager)
        }
    }

# Advanced workflow functions
async def execute_with_custom_workflow(task_description: str, workflow_config: Dict[str, Any], **kwargs) -> Dict[str, Any]:
    """Execute task with custom workflow configuration"""
    if not orchestrator.initialized:
        await orchestrator.initialize()
    
    # This could be enhanced to support custom workflow definitions
    # For now, use the standard workflow with custom parameters
    context = kwargs.get('context', {})
    context['workflow_config'] = workflow_config
    
    return await orchestrator.execute_task(task_description, context=context, **kwargs)

async def batch_execute_tasks(tasks: List[Dict[str, Any]], max_concurrent: int = 3) -> List[Dict[str, Any]]:
    """Execute multiple tasks with optional concurrency control"""
    if not orchestrator.initialized:
        await orchestrator.initialize()
    
    async def execute_single_task(task_config):
        task_desc = task_config.get('task', '')
        context = task_config.get('context', {})
        options = {k: v for k, v in task_config.items() if k not in ['task', 'context']}
        
        try:
            result = await orchestrator.execute_task(task_desc, context=context, **options)
            return {"task_config": task_config, "result": result}
        except Exception as e:
            return {"task_config": task_config, "result": {"success": False, "error": str(e)}}
    
    # Execute tasks with concurrency control
    results = []
    semaphore = asyncio.Semaphore(max_concurrent)
    
    async def controlled_execute(task_config):
        async with semaphore:
            return await execute_single_task(task_config)
    
    if tasks:
        logger.info(f"🚀 Executing {len(tasks)} tasks with max concurrency {max_concurrent}")
        results = await asyncio.gather(*[controlled_execute(task) for task in tasks])
        
        # Log summary
        successful = sum(1 for r in results if r["result"].get("success"))
        logger.info(f"✅ Batch execution completed: {successful}/{len(tasks)} successful")
    
    return results

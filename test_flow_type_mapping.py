#!/usr/bin/env python3
"""
Test Flow Type Mapping
Check if flow types are being properly mapped from the JSON data
"""

import json
from pathlib import Path
from collections import Counter

def check_flow_type_mapping():
    """Check flow type mapping and data."""
    print("🔍 TESTING FLOW TYPE MAPPING")
    print("=" * 40)
    
    # Check if flowtype.json exists and load it
    flow_type_file = "/Users/<USER>/Desktop/totango/flowtype.json"
    
    print(f"📄 Checking flow type mapping file...")
    if Path(flow_type_file).exists():
        with open(flow_type_file, 'r') as f:
            flow_type_data = json.load(f)
        
        print(f"✅ Found flowtype.json with {len(flow_type_data)} entries")
        
        # Show sample flow types
        print(f"\n📋 Sample Flow Types from mapping file:")
        for i, item in enumerate(flow_type_data[:5]):
            flow_id = item.get('id', 'no_id')
            display_name = item.get('display_name', item.get('name', 'no_name'))
            print(f"   {i+1}. ID: {flow_id}")
            print(f"      Name: {display_name}")
        
        # Create mapping
        flow_mapping = {}
        for item in flow_type_data:
            flow_id = item.get('id', '')
            display_name = item.get('display_name', item.get('name', ''))
            if flow_id and display_name:
                flow_mapping[flow_id] = display_name
        
        print(f"\n📊 Created mapping for {len(flow_mapping)} flow types")
        
    else:
        print(f"❌ Flow type file not found: {flow_type_file}")
        flow_mapping = {}
    
    return flow_mapping

def check_activities_for_flow_types():
    """Check activities for flow type fields."""
    print(f"\n🔍 CHECKING ACTIVITIES FOR FLOW TYPES")
    print("=" * 40)
    
    # Check the mapped JSON file
    json_file = "/Users/<USER>/Desktop/totango/ICICI_processed_gainsight_mapped.json"
    
    if not Path(json_file).exists():
        print(f"❌ JSON file not found: {json_file}")
        return
    
    with open(json_file, 'r') as f:
        activities = json.load(f)
    
    print(f"📊 Checking {len(activities)} activities for flow type fields...")
    
    # Look for flow type related fields
    flow_type_fields = Counter()
    flow_type_values = Counter()
    activities_with_flow_type = 0
    sample_flow_types = []
    
    for i, activity in enumerate(activities):
        properties = activity.get('properties', {})
        
        # Check for various flow type field names
        flow_type_found = False
        for field_name in ['flow_type', 'flow_type_id', 'flow_type_name', 'Ant__Flow_Type__c']:
            if field_name in properties:
                flow_type_fields[field_name] += 1
                flow_type_value = properties[field_name]
                flow_type_values[str(flow_type_value)] += 1
                flow_type_found = True
                
                # Collect samples
                if len(sample_flow_types) < 10:
                    sample_flow_types.append({
                        'activity_index': i,
                        'field_name': field_name,
                        'value': flow_type_value,
                        'activity_id': activity.get('id', 'no_id')
                    })
        
        if flow_type_found:
            activities_with_flow_type += 1
    
    print(f"📊 Activities with flow type fields: {activities_with_flow_type}")
    
    if flow_type_fields:
        print(f"\n📋 Flow Type Fields Found:")
        for field_name, count in flow_type_fields.most_common():
            print(f"   • {field_name}: {count} activities")
        
        print(f"\n📋 Flow Type Values Found:")
        for value, count in flow_type_values.most_common(10):
            print(f"   • '{value}': {count} activities")
        
        print(f"\n🔍 Sample Flow Type Data:")
        for sample in sample_flow_types[:5]:
            print(f"   Activity {sample['activity_index']+1}:")
            print(f"     Field: {sample['field_name']}")
            print(f"     Value: {sample['value']}")
            print(f"     ID: {sample['activity_id']}")
    else:
        print("❌ No flow type fields found in activities")

def test_flow_type_cleaning():
    """Test the flow type name cleaning function."""
    print(f"\n🧪 TESTING FLOW TYPE CLEANING")
    print("=" * 40)
    
    # Test cases for flow type cleaning
    test_cases = [
        "risk_1560979263618",
        "onboarding_1234567890",
        "support_case_1",
        "customer_feedback_2",
        "standard_flow",
        "Risk Management",
        "Customer_Onboarding_Process"
    ]
    
    import re
    
    def clean_flow_type_name(flow_type_name: str) -> str:
        """Clean flow type name by removing timestamps and numbers."""
        if not flow_type_name:
            return ""
        
        # Remove timestamp patterns like _1560979263618
        cleaned = re.sub(r'_\d{10,}', '', flow_type_name)
        
        # Remove trailing numbers like _1, _2, etc.
        cleaned = re.sub(r'_\d+$', '', cleaned)
        
        # Replace underscores with spaces and title case
        cleaned = cleaned.replace('_', ' ').title()
        
        return cleaned
    
    print("📋 Flow Type Cleaning Test Results:")
    for test_case in test_cases:
        cleaned = clean_flow_type_name(test_case)
        print(f"   • '{test_case}' → '{cleaned}'")

def run_enhanced_csv_test():
    """Show how to run the enhanced CSV exporter."""
    print(f"\n🚀 HOW TO RUN THE ENHANCED CSV EXPORTER")
    print("=" * 50)
    
    print("📋 To test the enhanced CSV exporter:")
    print("   1. For Demo data (Ram Prasad):")
    print("      echo '1' | python enhanced_csv_exporter.py")
    print("")
    print("   2. For Real data (ICICI users):")
    print("      echo '2' | python enhanced_csv_exporter.py")
    print("")
    print("   3. Interactive mode:")
    print("      python enhanced_csv_exporter.py")
    print("")
    
    print("📊 Generated files will be:")
    print("   • ICICI_processed_gainsight_mapped_enhanced_demo.csv")
    print("   • ICICI_processed_gainsight_mapped_enhanced_real.csv")
    print("")
    
    print("🔍 To verify the results:")
    print("   python verify_enhanced_csv.py")

def main():
    """Main test function."""
    print("🧪 FLOW TYPE MAPPING TEST")
    print("=" * 30)
    
    # Test 1: Check flow type mapping file
    flow_mapping = check_flow_type_mapping()
    
    # Test 2: Check activities for flow type fields
    check_activities_for_flow_types()
    
    # Test 3: Test flow type cleaning
    test_flow_type_cleaning()
    
    # Test 4: Show how to run the enhanced CSV exporter
    run_enhanced_csv_test()
    
    print(f"\n🎯 SUMMARY:")
    if flow_mapping:
        print(f"✅ Flow type mapping file loaded with {len(flow_mapping)} entries")
    else:
        print(f"❌ Flow type mapping file not found or empty")
    
    print(f"\n💡 RECOMMENDATIONS:")
    print(f"   1. Check if activities have flow_type fields")
    print(f"   2. Verify flowtype.json file exists and has correct structure")
    print(f"   3. Run enhanced CSV exporter to see flow type mapping results")
    print(f"   4. Use verification script to check final CSV output")

if __name__ == "__main__":
    main()

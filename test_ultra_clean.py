#!/usr/bin/env python3
"""
🧪 Quick test for ultra-clean automation script generation
"""

import asyncio
from pathlib import Path
import sys
import os

# Add current directory to path
sys.path.append(str(Path(__file__).parent))

async def test_script_generation():
    """Test that the script generation works with correct path"""
    
    print("🧪 Testing script generation...")
    
    # Import the automation class
    from ultra_clean_automation import UltraCleanAutomation
    
    automation = UltraCleanAutomation()
    
    # Test data directory creation
    data_dir = Path("data")
    data_dir.mkdir(exist_ok=True)
    print(f"✅ Data directory: {data_dir.absolute()}")
    
    # Test script generation with dummy actions
    dummy_actions = [
        {"type": "click", "selector": "button", "text": "Login", "timestamp": 1},
        {"type": "input", "selector": "input[name='username']", "value": "test", "timestamp": 2}
    ]
    
    try:
        script_path = await automation.generate_hybrid_script(dummy_actions)
        
        if script_path and script_path.exists():
            print(f"✅ Script generated: {script_path}")
            print(f"📁 Script size: {script_path.stat().st_size} bytes")
            
            # Verify it's the correct path
            expected_path = "data/hybrid_gainsight_automation.py"
            if str(script_path).endswith(expected_path):
                print(f"✅ Correct path: {expected_path}")
            else:
                print(f"❌ Wrong path: Expected {expected_path}, got {script_path}")
            
            # Show first few lines
            print("\n📝 Script preview:")
            with open(script_path, 'r') as f:
                lines = f.readlines()[:10]
                for i, line in enumerate(lines, 1):
                    print(f"  {i:2d}: {line.rstrip()}")
            
            return True
        else:
            print("❌ Script generation failed")
            return False
            
    except Exception as e:
        print(f"❌ Script generation error: {e}")
        return False

async def test_llm_brain_initialization():
    """Test LLM brain initialization separately"""
    
    print("\n🧠 Testing LLM brain initialization...")
    
    try:
        from enhanced_llm_client import EnhancedLLMClient
        
        async with EnhancedLLMClient() as client:
            result = await client.complete(
                "Test message", 
                task_type="general", 
                max_tokens=10
            )
            
            if result.success:
                print(f"✅ LLM brain working: {result.model} ({result.provider})")
                return True
            else:
                print(f"❌ LLM brain failed: {result.error}")
                return False
                
    except Exception as e:
        print(f"❌ LLM brain initialization error: {e}")
        return False

async def main():
    """Run all tests"""
    
    print("🎯 ULTRA-CLEAN AUTOMATION TESTS")
    print("=" * 40)
    
    # Test 1: Script generation
    test1 = await test_script_generation()
    
    # Test 2: LLM brain
    test2 = await test_llm_brain_initialization()
    
    print(f"\n📊 TEST RESULTS:")
    print(f"✅ Script generation: {'PASS' if test1 else 'FAIL'}")
    print(f"✅ LLM brain: {'PASS' if test2 else 'FAIL'}")
    
    if test1 and test2:
        print("\n🎉 All tests PASSED! Ultra-clean automation ready!")
    else:
        print("\n⚠️  Some tests failed, but basic functionality should work")
    
    return test1 and test2

if __name__ == "__main__":
    asyncio.run(main())

#!/usr/bin/env python3
"""
Debug Meeting Types Reading
Check exactly what's happening with meeting_type reading from ICICI.json
"""

import json
from collections import Counter

def debug_meeting_types():
    """Debug meeting type reading from ICICI.json."""
    print("🔍 DEBUGGING MEETING TYPE READING FROM ICICI.JSON")
    print("=" * 60)
    
    # Load ICICI data
    icici_file = "/Users/<USER>/Desktop/totango/ICICI_processed.json"
    with open(icici_file, 'r') as f:
        activities = json.load(f)
    
    print(f"📊 Total activities loaded: {len(activities)}")
    
    # Check meeting_type field
    activities_with_meeting_type = 0
    meeting_type_values = Counter()
    
    print(f"\n📋 CHECKING MEETING_TYPE FIELD:")
    print("-" * 40)
    
    for i, activity in enumerate(activities):
        properties = activity.get('properties', {})
        
        if 'meeting_type' in properties:
            activities_with_meeting_type += 1
            meeting_type_value = properties['meeting_type']
            meeting_type_values[meeting_type_value] += 1
            
            # Show first few examples
            if i < 10:
                print(f"   Activity {i+1}: meeting_type = '{meeting_type_value}'")
    
    print(f"\n📊 SUMMARY:")
    print(f"   • Activities with meeting_type field: {activities_with_meeting_type}")
    print(f"   • Activities without meeting_type field: {len(activities) - activities_with_meeting_type}")
    
    print(f"\n📋 MEETING_TYPE VALUES FOUND:")
    for value, count in meeting_type_values.most_common():
        print(f"   • '{value}': {count} activities")
    
    # Load ID.json to see mappings
    print(f"\n📋 ID.JSON MAPPINGS:")
    print("-" * 20)
    
    id_file = "/Users/<USER>/Desktop/totango/ID.json"
    try:
        with open(id_file, 'r') as f:
            id_data = json.load(f)
        
        id_mapping = {item['id']: item['display_name'] for item in id_data}
        
        print(f"   Loaded {len(id_mapping)} ID mappings:")
        for id_val, display_name in id_mapping.items():
            print(f"   • {id_val} → '{display_name}'")
        
        # Check if meeting_type values match ID mappings
        print(f"\n🔄 MAPPING CHECK:")
        print("-" * 15)
        
        for meeting_type_id, count in meeting_type_values.most_common():
            if meeting_type_id in id_mapping:
                mapped_name = id_mapping[meeting_type_id]
                print(f"   ✅ {meeting_type_id} → '{mapped_name}' ({count} activities)")
            else:
                print(f"   ❌ {meeting_type_id} → NOT FOUND in ID.json ({count} activities)")
                
    except Exception as e:
        print(f"   ❌ Error loading ID.json: {e}")
    
    # Show sample activity structure
    print(f"\n📋 SAMPLE ACTIVITY WITH MEETING_TYPE:")
    print("-" * 40)
    
    for activity in activities:
        properties = activity.get('properties', {})
        if 'meeting_type' in properties:
            print(f"   Activity ID: {activity.get('id', 'N/A')}")
            print(f"   Activity Type: {activity.get('type', 'N/A')}")
            print(f"   Meeting Type: {properties['meeting_type']}")
            print(f"   Properties keys: {list(properties.keys())[:10]}...")
            break

if __name__ == "__main__":
    debug_meeting_types()

#!/usr/bin/env python3
"""
🚀 UPDATED MAIN.PY - Latest LLM Integration
Fixed: Uses Enhanced LLM Client with latest models (Meta Llama 4, DeepSeek R1, Qwen)
Fixed: Proper async handling, no browser cleanup errors
"""
import asyncio
import argparse
import sys
import json
from typing import Dict, Any, Optional
from pathlib import Path

# UPDATED: Latest LLM integration first
try:
    from enhanced_llm_client import EnhancedLLMClient
    LLM_AVAILABLE = True
    print("✅ Latest LLM client loaded (Meta Llama 4, DeepSeek R1, Qwen)")
except ImportError:
    LLM_AVAILABLE = False
    print("⚠️  Enhanced LLM client not available")

# Original components (fallback)
try:
    from orchestrator import orchestrator, execute_task, replay_task, get_status
    from config import config
    ORCHESTRATOR_AVAILABLE = True
except ImportError:
    ORCHESTRATOR_AVAILABLE = False
    print("⚠️  Original orchestrator not available")

try:
    from gainsight_ui_automator import GainsightUIAutomator
    GAINSIGHT_AVAILABLE = True
except ImportError:
    GAINSIGHT_AVAILABLE = False

class EnhancedMainAgent:
    """Enhanced main agent with latest LLM integration"""
    
    def __init__(self):
        self.llm_client = None
        self.session_data = {}
    
    async def __aenter__(self):
        if LLM_AVAILABLE:
            self.llm_client = EnhancedLLMClient()
            await self.llm_client.__aenter__()
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        if self.llm_client:
            await self.llm_client.__aexit__(exc_type, exc_val, exc_tb)

def print_banner():
    """Print enhanced banner"""
    banner = """
╔══════════════════════════════════════════════════════════════════════════════╗
║                🚀 ENHANCED BROWSER AUTOMATION WITH LATEST LLM 🧠             ║
║                                                                              ║
║  🤖 Latest Models: Meta Llama 4 Maverick, DeepSeek R1, Qwen                 ║
║  🌐 Browser Automation: Playwright + browser-use                            ║
║  📊 ICICI Migration: Ready (37 activities)                                  ║
║  🎭 Manual → Record → Playwright workflow                                    ║
║  💰 Cost: $0 (Free models only)                                             ║
╚══════════════════════════════════════════════════════════════════════════════╝
    """
    print(banner)

async def enhanced_interactive_mode():
    """Enhanced interactive mode with latest LLM"""
    print("\n🤖 Enhanced Browser Agent (Latest LLM)")
    print("Commands: help, status, icici, gainsight, llm, quit")
    
    async with EnhancedMainAgent() as agent:
        while True:
            try:
                user_input = input("\n🚀 Agent> ").strip()
                
                if not user_input:
                    continue
                
                if user_input.lower() in ['quit', 'exit', 'q']:
                    print("👋 Goodbye!")
                    break
                
                if user_input.lower() == 'help':
                    print_enhanced_help()
                    continue
                
                if user_input.lower() == 'status':
                    await show_enhanced_status(agent)
                    continue
                
                if user_input.lower().startswith('icici'):
                    await handle_icici_migration()
                    continue
                
                if user_input.lower().startswith('gainsight'):
                    await handle_gainsight_automation()
                    continue
                
                if user_input.lower().startswith('llm'):
                    await test_llm_integration(agent)
                    continue
                
                # Default: Use latest LLM for task
                if agent.llm_client:
                    print(f"🧠 Processing with latest LLM: {user_input}")
                    result = await agent.llm_client.complete(
                        user_input,
                        task_type="general",
                        max_tokens=500
                    )
                    
                    print(f"🤖 Model: {result.model}")
                    print(f"⏱️  Time: {result.latency:.2f}s")
                    print(f"💡 Response: {result.content}")
                else:
                    print("❌ LLM not available")
                
            except KeyboardInterrupt:
                print("\n👋 Goodbye!")
                break
            except Exception as e:
                print(f"❌ Error: {e}")

async def show_enhanced_status(agent):
    """Show enhanced system status"""
    print("\n📊 ENHANCED SYSTEM STATUS")
    print("=" * 40)
    
    # LLM Status
    if agent.llm_client:
        print("🤖 LLM Integration: ✅ Active")
        print("   • Meta Llama 4 Maverick (coding)")
        print("   • DeepSeek R1 (reasoning)")
        print("   • Qwen QwQ 32B (general)")
        print("   • Auto-fallback enabled")
        print("   • Cost: $0 (free models)")
    else:
        print("🤖 LLM Integration: ❌ Not available")
    
    # Data Status
    print("\n📊 Migration Data:")
    data_files = {
        "ICICI converted": Path("data/icici_gainsight_ready.json"),
        "With IDs": Path("data/icici_gainsight_with_ids.json"),
        "Final payload": Path("data/icici_final_migration.json")
    }
    
    for name, path in data_files.items():
        if path.exists():
            try:
                with open(path, 'r') as f:
                    data = json.load(f)
                    count = len(data) if isinstance(data, list) else 1
                    print(f"   ✅ {name}: {count} activities")
            except:
                print(f"   ⚠️  {name}: Error reading")
        else:
            print(f"   ❌ {name}: Not found")
    
    # System Components
    print("\n🛠️  System Components:")
    components = [
        ("Enhanced LLM Client", LLM_AVAILABLE),
        ("Gainsight UI Automator", GAINSIGHT_AVAILABLE),
        ("Original Orchestrator", ORCHESTRATOR_AVAILABLE)
    ]
    
    for name, available in components:
        status = "✅" if available else "❌"
        print(f"   {status} {name}")

async def test_llm_integration(agent):
    """Test latest LLM integration"""
    print("\n🤖 TESTING LATEST LLM INTEGRATION")
    print("=" * 40)
    
    if not agent.llm_client:
        print("❌ LLM client not available")
        return
    
    tests = [
        ("coding", "Write a Python function to validate email"),
        ("reasoning", "Explain browser automation benefits"),
        ("data_migration", "Best practices for ICICI to Gainsight migration")
    ]
    
    for task_type, prompt in tests:
        print(f"\n🧠 Testing {task_type} model...")
        
        try:
            result = await agent.llm_client.complete(
                prompt,
                task_type=task_type,
                max_tokens=200
            )
            
            print(f"   ✅ Model: {result.model}")
            print(f"   ⏱️  Time: {result.latency:.2f}s")
            print(f"   🎯 Success: {result.success}")
            if result.success:
                print(f"   💡 Response: {result.content[:100]}...")
        
        except Exception as e:
            print(f"   ❌ Error: {e}")

async def handle_icici_migration():
    """Handle ICICI migration with latest LLM"""
    print("\n📊 ICICI MIGRATION (Latest LLM Enhanced)")
    print("=" * 50)
    
    print("1. Run UI automation (1 activity)")
    print("2. Run UI automation (3 activities)")
    print("3. Use fixed launcher")
    
    choice = input("Choice (1-3): ").strip()
    
    if choice in ["1", "2"]:
        max_activities = 1 if choice == "1" else 3
        
        if GAINSIGHT_AVAILABLE:
            try:
                automator = GainsightUIAutomator()
                success = await automator.run_migration(
                    max_activities=max_activities,
                    headless=False
                )
                
                if success:
                    print("✅ Migration completed!")
                else:
                    print("⚠️  Migration had issues")
            except Exception as e:
                print(f"❌ Migration error: {e}")
        else:
            print("❌ Gainsight automator not available")
    
    elif choice == "3":
        print("🔧 Use: python3 fixed_launcher.py")

async def handle_gainsight_automation():
    """Handle Gainsight automation options"""
    print("\n🌐 GAINSIGHT AUTOMATION")
    print("=" * 30)
    
    print("1. Fixed UI automation")
    print("2. Record workflow")
    print("3. Test generated script")
    
    choice = input("Choice (1-3): ").strip()
    
    if choice == "1":
        print("🔧 Use: python3 fixed_launcher.py")
    elif choice == "2":
        print("🔧 Use: python3 fixed_launcher.py (option 3)")
    elif choice == "3":
        script_path = Path("data/generated_automation.py")
        if script_path.exists():
            print(f"🔧 Run: python3 {script_path}")
        else:
            print("❌ No generated script found")

def print_enhanced_help():
    """Print enhanced help"""
    help_text = """
🚀 ENHANCED BROWSER AUTOMATION COMMANDS
════════════════════════════════════════════════════════════════

🎯 QUICK COMMANDS:
  help                 - Show this help
  status               - Enhanced system status
  quit/exit/q          - Exit application

🤖 LLM INTEGRATION (Latest Models):
  llm test             - Test latest LLM integration
  llm coding <task>    - Use coding model
  llm reasoning <task> - Use reasoning model

📊 MIGRATION:
  icici                - ICICI to Gainsight migration
  gainsight            - Gainsight automation options

💡 EXAMPLES:
  "Write Python code for web scraping"
  "Explain machine learning concepts"
  "Help with browser automation strategy"
  "Analyze data migration best practices"

🎭 WORKFLOW:
  The system uses the latest free LLM models:
  • Meta Llama 4 Maverick (400B params) - Coding
  • DeepSeek R1 - Reasoning and planning
  • Qwen QwQ 32B - General tasks
  • Auto-fallback between providers
    """
    print(help_text)

async def run_single_task_enhanced(task_description: str, options: Dict[str, Any]) -> Dict[str, Any]:
    """Run single task with latest LLM"""
    print(f"🚀 Executing enhanced task: {task_description}")
    
    async with EnhancedMainAgent() as agent:
        if agent.llm_client:
            # Use latest LLM for task analysis
            result = await agent.llm_client.complete(
                task_description,
                task_type=options.get('task_type', 'general'),
                max_tokens=options.get('max_tokens', 1000)
            )
            
            return {
                "success": result.success,
                "model": result.model,
                "provider": result.provider,
                "content": result.content,
                "latency": result.latency,
                "enhanced": True
            }
        else:
            # Fallback to original orchestrator if available
            if ORCHESTRATOR_AVAILABLE:
                return await execute_task(task_description)
            else:
                return {
                    "success": False,
                    "error": "No execution engine available"
                }

def print_enhanced_result(result: Dict[str, Any]):
    """Print enhanced result"""
    if result.get("success"):
        print("✅ Enhanced task completed!")
        if result.get("enhanced"):
            print(f"   🤖 Model: {result.get('model', 'Unknown')}")
            print(f"   🏢 Provider: {result.get('provider', 'Unknown')}")
            print(f"   ⏱️  Latency: {result.get('latency', 0):.2f}s")
        print(f"   💡 Result: {result.get('content', 'No content')[:200]}...")
    else:
        print("❌ Enhanced task failed!")
        print(f"   💥 Error: {result.get('error', 'Unknown error')}")

async def setup_enhanced_environment():
    """Setup enhanced environment"""
    print("🔧 Setting up enhanced environment...")
    
    if LLM_AVAILABLE:
        print("✅ Latest LLM client available")
    else:
        print("⚠️  LLM client not available")
    
    if GAINSIGHT_AVAILABLE:
        print("✅ Gainsight automation available")
    else:
        print("⚠️  Gainsight automation not available")
    
    # Create data directories
    Path("data").mkdir(exist_ok=True)
    Path("logs").mkdir(exist_ok=True)
    
    print("✅ Enhanced environment ready")
    return True

def main():
    """Enhanced main entry point"""
    parser = argparse.ArgumentParser(
        description="Enhanced Browser Automation with Latest LLM Integration",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
🚀 ENHANCED EXAMPLES:
  %(prog)s                                    # Enhanced interactive mode
  %(prog)s -t "Code a web scraper"            # Execute with latest LLM
  %(prog)s --icici-migration 3               # Run ICICI migration
  %(prog)s --llm-test                        # Test latest LLM
  %(prog)s --status                          # Show enhanced status
        """
    )
    
    parser.add_argument('-t', '--task', help='Execute task with latest LLM')
    parser.add_argument('--icici-migration', type=int, help='Run ICICI migration')
    parser.add_argument('--llm-test', action='store_true', help='Test latest LLM')
    parser.add_argument('--status', action='store_true', help='Show enhanced status')
    parser.add_argument('--task-type', default='general', help='Task type for LLM')
    parser.add_argument('--max-tokens', type=int, default=1000, help='Max tokens')
    
    args = parser.parse_args()
    
    # Print enhanced banner
    print_banner()
    
    async def run_enhanced_main():
        # Setup enhanced environment
        if not await setup_enhanced_environment():
            sys.exit(1)
        
        try:
            if args.status:
                async with EnhancedMainAgent() as agent:
                    await show_enhanced_status(agent)
            
            elif args.icici_migration:
                if GAINSIGHT_AVAILABLE:
                    automator = GainsightUIAutomator()
                    await automator.run_migration(max_activities=args.icici_migration)
                else:
                    print("❌ Gainsight automator not available")
            
            elif args.llm_test:
                async with EnhancedMainAgent() as agent:
                    await test_llm_integration(agent)
            
            elif args.task:
                options = {
                    'task_type': args.task_type,
                    'max_tokens': args.max_tokens
                }
                result = await run_single_task_enhanced(args.task, options)
                print_enhanced_result(result)
            
            else:
                # Enhanced interactive mode
                await enhanced_interactive_mode()
        
        except Exception as e:
            print(f"❌ Enhanced error: {e}")
    
    try:
        asyncio.run(run_enhanced_main())
    except KeyboardInterrupt:
        print("\n👋 Enhanced automation ready anytime!")
    except Exception as e:
        print(f"❌ Fatal error: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()

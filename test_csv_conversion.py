#!/usr/bin/env python3
"""
Test CSV Conversion Functionality
Tests the JSON to CSV conversion for Gainsight import
"""

import json
import pandas as pd
from pathlib import Path
from json_to_csv_converter import JSONToCSVConverter

def create_sample_json():
    """Create sample enhanced JSON data for testing"""
    sample_data = [
        {
            "id": "activity_001",
            "timestamp": 1640995200000,  # Jan 1, 2022
            "type": "campaign_touch",
            "properties": {
                "meeting_type": "1I00ABC123",
                "meeting_type_id": "1I00ABC123",
                "meeting_type_name": "Email",
                "gainsight_activity_type": "Email",
                "touchpoint_tags_ids": ["1I00XYZ789"],
                "touchpoint_tags_names": ["Customer Onboarding"],
                "name": "Welcome Email Campaign",
                "description": "Welcome email sent to new ICICI customers"
            }
        },
        {
            "id": "activity_002", 
            "timestamp": 1641081600000,  # Jan 2, 2022
            "type": "webhook",
            "properties": {
                "meeting_type": "1I00DEF456",
                "meeting_type_id": "1I00DEF456",
                "meeting_type_name": "Web Meeting",
                "gainsight_activity_type": "Meeting",
                "touchpoint_tags_ids": ["1I00ABC123"],
                "touchpoint_tags_names": ["Product Demo"],
                "name": "Product Demo Session",
                "description": "Online product demonstration for ICICI team"
            }
        },
        {
            "id": "activity_003",
            "timestamp": *************,  # Jan 3, 2022
            "type": "automated_attribute_change",
            "properties": {
                "meeting_type_id": None,
                "meeting_type_name": "No Meeting Type",
                "gainsight_activity_type": "Update",
                "touchpoint_tags_ids": [],
                "touchpoint_tags_names": ["Internal Note"],
                "display_name": "Account Status",
                "new_value": "Active",
                "prev_value": "Pending"
            }
        },
        {
            "id": "activity_004",
            "timestamp": *************,  # Jan 4, 2022
            "type": "account_alert",
            "properties": {
                "meeting_type_id": None,
                "meeting_type_name": "No Meeting Type", 
                "gainsight_activity_type": "Update",
                "touchpoint_tags_ids": [],
                "touchpoint_tags_names": ["Internal Note"],
                "title": "High Value Customer Alert",
                "description": "Customer has been flagged as high value based on recent activity"
            }
        }
    ]
    
    return sample_data

def test_csv_conversion():
    """Test the CSV conversion functionality"""
    print("🧪 TESTING CSV CONVERSION")
    print("=" * 40)
    
    # Create sample data
    sample_data = create_sample_json()
    
    # Save sample JSON
    sample_json_file = "test_sample_data.json"
    with open(sample_json_file, 'w') as f:
        json.dump(sample_data, f, indent=2)
    
    print(f"📄 Created sample JSON with {len(sample_data)} activities")
    
    # Test conversion
    converter = JSONToCSVConverter()
    
    try:
        csv_file = converter.convert_json_to_csv(sample_json_file, "test_output.csv")
        
        print(f"✅ Conversion successful!")
        print(f"📄 CSV file created: {csv_file}")
        
        # Read and display sample CSV content
        df = pd.read_csv(csv_file)
        
        print(f"\n📊 CSV Structure:")
        print(f"   • Rows: {len(df)}")
        print(f"   • Columns: {len(df.columns)}")
        
        print(f"\n📋 Column Names:")
        for i, col in enumerate(df.columns):
            print(f"   {i+1:2d}. {col}")
        
        print(f"\n🔍 Sample Data (first 2 rows):")
        print("=" * 60)
        
        for i in range(min(2, len(df))):
            print(f"\nRow {i+1}:")
            row = df.iloc[i]
            key_fields = [
                "Activity ID", "Subject", "Activity Type", 
                "Activity Date", "Author Name", "Company"
            ]
            
            for field in key_fields:
                if field in row:
                    value = str(row[field])[:50] + "..." if len(str(row[field])) > 50 else str(row[field])
                    print(f"   {field}: {value}")
        
        print(f"\n🎯 Activity Type Distribution:")
        activity_type_counts = df['Activity Type'].value_counts()
        for activity_type, count in activity_type_counts.items():
            print(f"   • {activity_type}: {count}")
        
        # Clean up test files
        Path(sample_json_file).unlink()
        Path(csv_file).unlink()
        
        return True
        
    except Exception as e:
        print(f"❌ Conversion failed: {e}")
        return False

def show_csv_structure():
    """Show the expected CSV structure for Gainsight"""
    print("\n📋 EXPECTED CSV STRUCTURE FOR GAINSIGHT")
    print("=" * 50)
    
    csv_fields = [
        ("Activity ID", "Unique identifier for each activity"),
        ("Subject", "Meaningful title for the activity"),
        ("Activity Date", "Formatted timestamp (YYYY-MM-DD HH:MM:SS)"),
        ("Activity Type", "Gainsight activity type (Email, Call, Meeting, Update, etc.)"),
        ("Content (HTML)", "Detailed HTML description"),
        ("Plain Text", "Plain text version of content"),
        ("Author Name", "Ram Prasad"),
        ("Author Email", "<EMAIL>"),
        ("Flow Type", "Gainsight flow type ID"),
        ("Touchpoint Reason", "Gainsight touchpoint reason ID"),
        ("Internal Attendees", "Ram Prasad <<EMAIL>>"),
        ("External Attendees", "Empty"),
        ("Company", "ICICI"),
        ("Original Activity Type", "Original ICICI activity type"),
        ("Meeting Type Name", "Totango meeting type name"),
        ("Touchpoint Tags", "Mapped touchpoint tag names"),
        ("Source", "ICICI_MIGRATION"),
        ("Timestamp", "Original timestamp"),
        ("Has Meeting Type", "Yes/No indicator")
    ]
    
    print("📊 CSV will contain the following fields:")
    for i, (field_name, description) in enumerate(csv_fields):
        print(f"   {i+1:2d}. {field_name}")
        print(f"       {description}")
    
    print(f"\n🎯 Key Fields for Gainsight Import:")
    key_fields = [
        "Activity Type",
        "Subject", 
        "Activity Date",
        "Content (HTML)",
        "Author Name",
        "Company"
    ]
    
    for field in key_fields:
        print(f"   • {field}")

def main():
    """Main test function"""
    print("🧪 CSV CONVERSION TEST")
    print("=" * 30)
    
    # Test 1: Show expected structure
    show_csv_structure()
    
    # Test 2: Test conversion functionality
    conversion_success = test_csv_conversion()
    
    # Summary
    print(f"\n🎯 TEST SUMMARY:")
    print("=" * 20)
    
    if conversion_success:
        print("✅ CSV conversion: WORKING")
        print("✅ All required fields: PRESENT")
        print("✅ Data formatting: CORRECT")
    else:
        print("❌ CSV conversion: FAILED")
    
    print(f"\n🚀 NEXT STEPS:")
    if conversion_success:
        print("   1. Run complete migration: python complete_icici_migration.py")
        print("   2. Check output CSV for Gainsight import")
        print("   3. Verify all 322 activities are processed")
    else:
        print("   1. Fix conversion issues")
        print("   2. Re-run this test")
    
    print("\n🎉 Test completed!")

if __name__ == "__main__":
    main()

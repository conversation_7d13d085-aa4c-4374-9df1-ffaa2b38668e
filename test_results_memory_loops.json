{"timestamp": "2025-05-27T17:21:31.119650", "total_time": 683.4267520904541, "success_rate": 0.14285714285714285, "results": {"Memory Persistence": {"success": false, "error": "Teaching phase failed"}, "Multi-Loop Execution": {"success": false, "execution_time": 172.89720273017883, "steps_taken": 0, "main_task_success": false, "followup_success": false, "recording_path": null, "context_retention": false}, "Learning & Adaptation": {"success": false, "execution_times": [0, 0, 0], "showed_improvement": false, "adaptation_success": false, "total_test_time": 142.62048888206482, "attempts": {"baseline": false, "learned": false, "adapted": false}}, "Intelligent Replay": {"success": false, "recording_used": "data/recordings/task_8b7ff1ba.json", "execution_time": 0.015167951583862305, "replay_data": {"error": "division by zero", "success": false}}, "Error Recovery": {"success": true, "execution_time": 94.16968417167664, "scenarios": {"invalid_url": false, "impossible_task": false}, "graceful_handling": true, "results": [{"success": false, "error": "string longer than INT_MAX bytes", "task_description": "Go to invalid-website-that-does-not-exist.com and if that fails, go to example.com instead", "timestamp": "2025-05-27T17:17:10.888762"}, {"success": false, "error": "string longer than INT_MAX bytes", "task_description": "Go to example.com and change the website's source code to add a new paragraph", "timestamp": "2025-05-27T17:17:59.037747"}]}, "Context Awareness": {"success": false, "execution_time": 210.91262292861938, "phases": {"context_setup": false, "context_application": false, "pattern_recognition": false}}, "Advanced Multi-Loop": {"success": false, "error": "TaskContext.__init__() missing 1 required positional argument: 'task_id'"}}}
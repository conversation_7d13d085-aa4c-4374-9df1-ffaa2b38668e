#!/usr/bin/env python3
"""
Generate All Files in Totango Directory
=======================================

This script generates both JSON and CSV files and saves them ALL in the totango directory.

What it creates:
1. ICICI_processed_gainsight_mapped.json (Mapped activities)
2. ICICI_processed_gainsight_mapped_enhanced_demo.csv (Demo CSV)
3. ICICI_processed_gainsight_mapped_enhanced_real.csv (Real CSV)
4. ICICI_processed_gainsight_import.csv (Basic CSV backup)

Author: Assistant
Date: January 29, 2025
"""

import subprocess
import sys
from pathlib import Path

def print_banner():
    """Print the application banner."""
    banner = """
╔══════════════════════════════════════════════════════════════════════════════╗
║            🚀 GENERATE ALL FILES IN TOTANGO DIRECTORY                       ║
║                                                                              ║
║  This script creates both JSON and CSV files in the totango folder:         ║
║  • JSON: Mapped activities with Gainsight activity types                    ║
║  • CSV Demo: Testing version with <PERSON> Prasad                                ║
║  • CSV Real: Production version with real ICICI users                       ║
║  • All files saved to: /Users/<USER>/Desktop/totango/            ║
╚══════════════════════════════════════════════════════════════════════════════╝
    """
    print(banner)

def check_prerequisites():
    """Check if source files exist."""
    print("🔍 CHECKING SOURCE FILES")
    print("=" * 40)
    
    source_files = [
        "/Users/<USER>/Desktop/totango/ICICI_processed.json",
        "/Users/<USER>/Desktop/totango/flowtype.json",
        "/Users/<USER>/Desktop/totango/Touchpoint_reason.JSON"
    ]
    
    all_exist = True
    for file_path in source_files:
        if Path(file_path).exists():
            size = Path(file_path).stat().st_size
            print(f"✅ {Path(file_path).name}: {size:,} bytes")
        else:
            print(f"❌ Missing: {file_path}")
            all_exist = False
    
    return all_exist

def run_command(description, command, timeout=120):
    """Run a command and show results."""
    print(f"\n🔄 {description}")
    print("=" * 50)
    print(f"🚀 Running: {command}")
    
    try:
        if "echo" in command:
            # Handle commands with echo input
            parts = command.split(" | ")
            echo_part = parts[0].replace("echo '", "").replace("'", "")
            python_part = parts[1]
            
            result = subprocess.run(
                python_part.split(),
                input=echo_part + "\n",
                text=True,
                capture_output=True,
                timeout=timeout
            )
        else:
            result = subprocess.run(
                command.split(),
                capture_output=True,
                text=True,
                timeout=timeout
            )
        
        if result.returncode == 0:
            print(f"✅ {description} completed successfully!")
            
            # Show last few lines of output
            if result.stdout:
                output_lines = result.stdout.strip().split('\n')
                if len(output_lines) > 3:
                    print("📄 Output (last 3 lines):")
                    for line in output_lines[-3:]:
                        if line.strip():
                            print(f"   {line}")
                else:
                    print("📄 Output:")
                    for line in output_lines:
                        if line.strip():
                            print(f"   {line}")
            
            return True
        else:
            print(f"❌ {description} failed!")
            if result.stderr:
                print(f"Error: {result.stderr}")
            return False
            
    except subprocess.TimeoutExpired:
        print(f"⏰ {description} timed out!")
        return False
    except Exception as e:
        print(f"💥 {description} error: {e}")
        return False

def show_generated_files():
    """Show all generated files in totango directory."""
    print(f"\n📁 GENERATED FILES IN TOTANGO DIRECTORY")
    print("=" * 50)
    
    totango_dir = "/Users/<USER>/Desktop/totango"
    expected_files = [
        "ICICI_processed_gainsight_mapped.json",
        "ICICI_processed_gainsight_mapped_enhanced_demo.csv",
        "ICICI_processed_gainsight_mapped_enhanced_real.csv",
        "ICICI_processed_gainsight_import.csv"
    ]
    
    print(f"📊 Files in: {totango_dir}")
    print()
    
    all_files_exist = True
    
    for file_name in expected_files:
        file_path = Path(totango_dir) / file_name
        
        if file_path.exists():
            size = file_path.stat().st_size
            print(f"✅ {file_name}")
            print(f"   📊 Size: {size:,} bytes")
            print(f"   📄 Path: {file_path}")
        else:
            print(f"❌ {file_name}: NOT FOUND")
            all_files_exist = False
        print()
    
    return all_files_exist

def show_usage_instructions():
    """Show how to use the generated files."""
    print("🚀 HOW TO USE YOUR FILES")
    print("=" * 40)
    
    print("📋 All files are now in: /Users/<USER>/Desktop/totango/")
    print()
    
    print("📊 JSON File (For Development):")
    print("   • ICICI_processed_gainsight_mapped.json")
    print("     - Contains all 322 activities with Gainsight activity types")
    print("     - Use for further processing or analysis")
    print()
    
    print("📊 CSV Files (For Gainsight Import):")
    print("   • ICICI_processed_gainsight_mapped_enhanced_demo.csv")
    print("     - For TESTING Gainsight import")
    print("     - Uses Ram Prasad as author for all activities")
    print("     - Perfect for testing the import process")
    print()
    print("   • ICICI_processed_gainsight_mapped_enhanced_real.csv")
    print("     - For PRODUCTION migration")
    print("     - Uses real ICICI users as authors")
    print("     - Ready for actual migration")
    print()
    print("   • ICICI_processed_gainsight_import.csv")
    print("     - Basic CSV backup (without enhancements)")
    print()
    
    print("🎯 Gainsight Import Process:")
    print("   1. Choose Demo CSV for testing OR Real CSV for production")
    print("   2. Import into Gainsight Timeline")
    print("   3. Map columns to Gainsight fields")
    print("   4. Verify all 322 activities import successfully")

def main():
    """Main function to generate all files."""
    print_banner()
    
    # Step 1: Check prerequisites
    if not check_prerequisites():
        print("\n❌ Missing source files. Please ensure all files are in the totango directory.")
        return 1
    
    print(f"\n🎯 GENERATION PROCESS")
    print("=" * 30)
    print("This will create 4 files in the totango directory:")
    print("1. Mapped JSON (activities with Gainsight types)")
    print("2. Demo CSV (Ram Prasad as author)")
    print("3. Real CSV (ICICI users as authors)")
    print("4. Basic CSV (backup)")
    
    try:
        response = input("\nProceed with file generation? (y/n): ").strip().lower()
        
        if response not in ['y', 'yes']:
            print("\n👋 Generation cancelled by user.")
            return 0
        
        # Step 1: Generate JSON with Gainsight activity types
        success1 = run_command(
            "STEP 1: Generate Gainsight-Mapped JSON",
            "python3 complete_gainsight_mapping.py"
        )
        
        if not success1:
            print("\n❌ Failed to generate JSON file")
            return 1
        
        # Step 2: Generate Demo CSV
        success2 = run_command(
            "STEP 2: Generate Demo CSV (Ram Prasad)",
            "echo '1' | python3 enhanced_csv_exporter.py"
        )
        
        if not success2:
            print("\n❌ Failed to generate Demo CSV")
            return 1
        
        # Step 3: Generate Real CSV
        success3 = run_command(
            "STEP 3: Generate Real CSV (ICICI Users)",
            "echo '2' | python3 enhanced_csv_exporter.py"
        )
        
        if not success3:
            print("\n❌ Failed to generate Real CSV")
            return 1
        
        # Step 4: Show results
        all_files_created = show_generated_files()
        
        if all_files_created:
            show_usage_instructions()
            
            print(f"\n🎉 ALL FILES GENERATED SUCCESSFULLY!")
            print("=" * 50)
            print("✅ JSON file: Contains mapped activity data")
            print("✅ Demo CSV: Ready for testing Gainsight import")
            print("✅ Real CSV: Ready for production migration")
            print("✅ Basic CSV: Backup option available")
            print("✅ All files saved in totango directory")
            
            print(f"\n📁 Location: /Users/<USER>/Desktop/totango/")
            print(f"🚀 Ready for Gainsight Timeline import!")
            
        else:
            print(f"\n⚠️  Some files may not have been created properly")
            print("Please check the error messages above")
        
        return 0
        
    except KeyboardInterrupt:
        print("\n👋 Generation cancelled by user.")
        return 0

if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)

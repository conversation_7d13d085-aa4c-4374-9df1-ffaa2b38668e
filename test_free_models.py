#!/usr/bin/env python3
"""
Quick test to verify free OpenRouter models are working
"""

import asyncio
from enhanced_llm_client import EnhancedLLMClient

async def test_free_models():
    print("🧪 Testing Free OpenRouter Models")
    print("=" * 50)
    
    async with EnhancedLL<PERSON>lient() as client:
        
        # Test 1: DeepSeek (primary model)
        print("\n🤖 Testing DeepSeek Chat V3 (Free)...")
        try:
            response = await client.complete(
                "Say 'Hello from DeepSeek!' in exactly 5 words",
                task_type="general",
                max_tokens=20
            )
            print(f"✅ Model: {response.model}")
            print(f"✅ Provider: {response.provider}")
            print(f"✅ Success: {response.success}")
            print(f"✅ Response: {response.content}")
            print(f"✅ Latency: {response.latency:.2f}s")
        except Exception as e:
            print(f"❌ DeepSeek failed: {e}")
        
        # Test 2: Llama (reasoning model)
        print("\n🦙 Testing Llama 3.3 70B (Free)...")
        try:
            response = await client.complete(
                "What is 2+2? Answer in 3 words maximum.",
                task_type="reasoning",
                max_tokens=20
            )
            print(f"✅ Model: {response.model}")
            print(f"✅ Provider: {response.provider}")
            print(f"✅ Success: {response.success}")
            print(f"✅ Response: {response.content}")
            print(f"✅ Latency: {response.latency:.2f}s")
        except Exception as e:
            print(f"❌ Llama failed: {e}")
        
        # Test 3: Browser automation task
        print("\n🌐 Testing Browser Automation Intelligence...")
        try:
            response = await client.complete(
                "Browser automation: Reached Gainsight login page. Brief next action?",
                task_type="browser_automation",
                max_tokens=30
            )
            print(f"✅ Model: {response.model}")
            print(f"✅ Provider: {response.provider}")
            print(f"✅ Success: {response.success}")
            print(f"✅ Response: {response.content}")
            print(f"✅ Latency: {response.latency:.2f}s")
        except Exception as e:
            print(f"❌ Browser automation failed: {e}")

if __name__ == "__main__":
    asyncio.run(test_free_models())

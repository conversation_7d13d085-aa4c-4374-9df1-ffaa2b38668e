#!/usr/bin/env python3
"""
🎭 HYBRID GAINSIGHT AUTOMATION SCRIPT  
Generated from 0 recorded actions
Combines LLM brain + precise automation
"""

import asyncio
from playwright.async_api import async_playwright

async def hybrid_gainsight_automation():
    """Hybrid automation with LLM brain"""
    
    async with async_playwright() as playwright:
        browser = await playwright.chromium.launch(
            headless=False,
            args=['--no-sandbox', '--disable-dev-shm-usage']
        )
        
        try:
            page = await browser.new_page()
            page.set_default_timeout(30000)
            
            print("🎯 HYBRID AUTOMATION STARTING...")
            
            # Step 1: Navigate
            await page.goto("https://auth.gainsightcloud.com/login?lc=en")
            await page.wait_for_load_state('networkidle')
            await asyncio.sleep(2)
            
            # Step 2: Subdomain FIRST 
            print("🏢 Subdomain: demo-emea1")
            subdomain_selectors = [
                'input[placeholder*="subdomain" i]',
                'input[type="text"]:visible:first'
            ]
            
            for selector in subdomain_selectors:
                if await page.locator(selector).count() > 0:
                    await page.fill(selector, "demo-emea1")
                    await page.click('button:has-text("Continue"), button[type="submit"]')
                    await asyncio.sleep(2)
                    break
            
            # Step 3: Username
            print("👤 Username: <EMAIL>")
            await page.fill('input[name="username"], input[type="email"]', "<EMAIL>")
            
            # Step 4: Password  
            print("🔐 Password: ********")
            await page.fill('input[type="password"]', "@Ramprasad826ie")
            
            # Step 5: Submit
            print("🚀 Submit login")
            await page.click('button[type="submit"]')
            await asyncio.sleep(5)
            
            # Step 6: Timeline
            print("📋 Navigate to timeline")
            timeline_url = "https://demo-emea1.gainsightcloud.com/v1/ui/customersuccess360?cid=1P02IPMAEL4M3CQGY4K5FHU22D2HHT11KCKU#/8fd79692-8e98-4543-b2ec-bc0dba3776ca"
            await page.goto(timeline_url)
            await asyncio.sleep(3)
            
            await page.click('a:has-text("Timeline")')
            await asyncio.sleep(2)
            
            # Step 7: Create activities
            for i in range(3):
                print(f"📝 Creating activity {i+1}/3...")
                
                await page.click('button:has-text("Create")')
                await asyncio.sleep(1)
                
                await page.click('a:has-text("Activity")')
                await asyncio.sleep(2)
                
                subject = f"Hybrid Activity #{i+1} - Auto"
                await page.fill('input[name="subject"]', subject)
                
                await page.click('button:has-text("Log Activity")')
                await asyncio.sleep(2)
                
                print(f"✅ Activity {i+1} created!")
            
            print("🎉 HYBRID AUTOMATION COMPLETED!")
            
        except Exception as e:
            print(f"❌ Error: {e}")
            
        finally:
            await page.close()
            await browser.close()

# Recorded actions for reference:
# []

if __name__ == "__main__":
    asyncio.run(hybrid_gainsight_automation())

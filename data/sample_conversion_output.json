[{"lastModifiedByUser": {"gsId": "1P01E316G9DAPFOLE6SOOUG71XRMN5F3PLER", "name": "<PERSON>", "eid": null, "esys": null, "pp": ""}, "note": {"customFields": {"internalAttendees": [{"id": "1P01E316G9DAPFOLE6SOOUG71XRMN5F3PLER", "obj": "User", "name": "<PERSON>", "email": "<EMAIL>", "eid": null, "eobj": "User", "epp": null, "esys": "SALESFORCE", "sys": "GAINSIGHT", "pp": ""}], "externalAttendees": [], "ant__Status1552512571338": null, "Ant__Touchpoint_Reason__c": "1I00J1INQCQ6GQ3T2MWULITYE9ASDFPQ2KD3", "Ant__Flow_Type__c": "1I00EBMOY66ROGCBY63I51PAXG5OJRPK37AQ"}, "type": "EMAIL", "subject": "Onboarding Complete: Welcome to [Company Name]!", "activityDate": *************, "content": "<p>A welcome email (\"Customer Onboarding\") was sent as part of the onboarding_101 campaign. This email provided initial information and resources to help the customer get started with our product or service.  The email aimed to facilitate a smooth onboarding experience.</p>", "plainText": "A welcome email (\"Customer Onboarding\") was sent as part of the onboarding_101 campaign. This email provided initial information and resources to help the customer get started with our product or service.  The email aimed to facilitate a smooth onboarding experience.", "trackers": null}, "mentions": [], "relatedRecords": {}, "meta": {"activityTypeId": "c81eeccf-2aa1-45bc-be71-5377f148e1e9", "ctaId": null, "source": "ICICI_MIGRATION", "hasTask": false, "emailSent": false, "systemType": "GAINSIGHT", "notesTemplateId": null}, "author": {"id": "1P01E316G9DAPFOLE6SOOUG71XRMN5F3PLER", "obj": "User", "name": "<PERSON>", "email": "<EMAIL>", "eid": null, "eobj": "User", "epp": null, "esys": "SALESFORCE", "sys": "GAINSIGHT", "pp": ""}, "syncedToSFDC": false, "id": "ICICI_act_001", "tasks": [], "attachments": [], "contexts": [{"id": "1P02IPMAEL4M3CQGY4K5FHU22D2HHT11KCKU", "obj": "Company", "eobj": "Account", "eid": null, "esys": "SALESFORCE", "lbl": "ICICI", "dsp": true, "base": true}]}, {"lastModifiedByUser": {"gsId": "1P01E316G9DAPFOLE6SOOUG71XRMN5F3PLER", "name": "<PERSON>", "eid": null, "esys": null, "pp": ""}, "note": {"customFields": {"internalAttendees": [{"id": "1P01E316G9DAPFOLE6SOOUG71XRMN5F3PLER", "obj": "User", "name": "<PERSON>", "email": "<EMAIL>", "eid": null, "eobj": "User", "epp": null, "esys": "SALESFORCE", "sys": "GAINSIGHT", "pp": ""}], "externalAttendees": [], "ant__Status1552512571338": null, "Ant__Touchpoint_Reason__c": "1I00J1INQCQ6GQ3T2MWULITYE9ASDFPQ2KD3", "Ant__Flow_Type__c": "1I00EBMOY66ROGCBY63I51PAXG5OJRPK37AQ"}, "type": "EMAIL", "subject": "Product Adoption: Feature Usage Detected", "activityDate": *************, "content": "<p>The customer has engaged with the product, demonstrating adoption of a key feature. This webhook event signals successful usage of the feature, indicating positive product engagement and potential for increased value realization.  Further analysis of usage patterns is recommended.</p>", "plainText": "The customer has engaged with the product, demonstrating adoption of a key feature. This webhook event signals successful usage of the feature, indicating positive product engagement and potential for increased value realization.  Further analysis of usage patterns is recommended.", "trackers": null}, "mentions": [], "relatedRecords": {}, "meta": {"activityTypeId": "68c0d13a-40f1-47e4-9bb4-3d1fb6a16515", "ctaId": null, "source": "ICICI_MIGRATION", "hasTask": false, "emailSent": false, "systemType": "GAINSIGHT", "notesTemplateId": null}, "author": {"id": "1P01E316G9DAPFOLE6SOOUG71XRMN5F3PLER", "obj": "User", "name": "<PERSON>", "email": "<EMAIL>", "eid": null, "eobj": "User", "epp": null, "esys": "SALESFORCE", "sys": "GAINSIGHT", "pp": ""}, "syncedToSFDC": false, "id": "ICICI_act_002", "tasks": [], "attachments": [], "contexts": [{"id": "1P02IPMAEL4M3CQGY4K5FHU22D2HHT11KCKU", "obj": "Company", "eobj": "Account", "eid": null, "esys": "SALESFORCE", "lbl": "ICICI", "dsp": true, "base": true}]}]
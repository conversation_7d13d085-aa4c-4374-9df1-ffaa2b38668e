{"gainsightcloud.com": {"input_text": [{"selector": "[aria-label*='Subdomain']", "alternatives": ["text=Subdomain", "//input[@name='subdomain']", "input[name='subdomain']"], "quality_score": 0.7999999999999999, "context": {"page_title": "Gainsight Login", "page_url": "https://auth.gainsightcloud.com/login", "element_text": "Subdomain", "element_attributes": {}}, "discovered_at": "2025-05-28T20:01:26.970229", "action_type": "input_text", "usage_count": 1, "success_rate": 1.0}, {"selector": "[aria-label*='Email']", "alternatives": ["text=Email", "//input[@type='email']", "input[type='email']"], "quality_score": 0.7999999999999999, "context": {"page_title": "Gainsight Login", "page_url": "https://auth.gainsightcloud.com/login", "element_text": "Email", "element_attributes": {}}, "discovered_at": "2025-05-28T20:01:26.970506", "action_type": "input_text", "usage_count": 1, "success_rate": 1.0}, {"selector": "[aria-label*='Password']", "alternatives": ["text=Password", "//input[@type='password']", "input[type='password']"], "quality_score": 0.7999999999999999, "context": {"page_title": "Gainsight Login", "page_url": "https://auth.gainsightcloud.com/login", "element_text": "Password", "element_attributes": {}}, "discovered_at": "2025-05-28T20:01:26.970667", "action_type": "input_text", "usage_count": 1, "success_rate": 1.0}, {"selector": "[aria-label*='Subject']", "alternatives": ["text=Subject", "//input[@name='subject']", "input[name='subject'], #activity-subject"], "quality_score": 0.7999999999999999, "context": {"page_title": "Add Activity", "page_url": "https://demo-emea1.gainsightcloud.com/v1/ui/customersuccess360?cid=1P02IPMAEL4M3CQGY4K5FHU22D2HHT11KCKU#/8fd79692-8e98-4543-b2ec-bc0dba3776ca", "element_text": "Subject", "element_attributes": {}}, "discovered_at": "2025-05-28T20:01:26.971699", "action_type": "input_text", "usage_count": 1, "success_rate": 1.0}, {"selector": "[aria-label*='Content']", "alternatives": ["text=Content", "//textarea[@name='content']", "textarea[name='content'], #activity-content"], "quality_score": 0.7999999999999999, "context": {"page_title": "Add Activity", "page_url": "https://demo-emea1.gainsightcloud.com/v1/ui/customersuccess360?cid=1P02IPMAEL4M3CQGY4K5FHU22D2HHT11KCKU#/8fd79692-8e98-4543-b2ec-bc0dba3776ca", "element_text": "Content", "element_attributes": {}}, "discovered_at": "2025-05-28T20:01:26.971956", "action_type": "input_text", "usage_count": 1, "success_rate": 1.0}], "click_element_by_index": [{"selector": "[aria-label*='Sign In']", "alternatives": ["text=Sign In", "//button[@type='submit']", "button[type='submit']"], "quality_score": 0.7999999999999999, "context": {"page_title": "Gainsight Login", "page_url": "https://auth.gainsightcloud.com/login", "element_text": "Sign In", "element_attributes": {}}, "discovered_at": "2025-05-28T20:01:26.970810", "action_type": "click_element_by_index", "usage_count": 1, "success_rate": 1.0}, {"selector": ".timeline-tab, [data-tab='timeline']", "alternatives": ["[aria-label*='Timeline']", "text=Timeline", "//div[contains(@class, 'timeline')]//button"], "quality_score": 0.9, "context": {"page_title": "Customer Success 360", "page_url": "https://demo-emea1.gainsightcloud.com/v1/ui/customersuccess360?cid=1P02IPMAEL4M3CQGY4K5FHU22D2HHT11KCKU#/8fd79692-8e98-4543-b2ec-bc0dba3776ca", "element_text": "Timeline", "element_attributes": {}}, "discovered_at": "2025-05-28T20:01:26.970965", "action_type": "click_element_by_index", "usage_count": 1, "success_rate": 1.0}, {"selector": "button.add-activity, [data-action='add-activity']", "alternatives": ["[aria-label*='Add Activity']", "text=Add Activity", "//button[contains(text(), 'Add Activity')]"], "quality_score": 0.9, "context": {"page_title": "Customer Success 360", "page_url": "https://demo-emea1.gainsightcloud.com/v1/ui/customersuccess360?cid=1P02IPMAEL4M3CQGY4K5FHU22D2HHT11KCKU#/8fd79692-8e98-4543-b2ec-bc0dba3776ca", "element_text": "Add Activity", "element_attributes": {}}, "discovered_at": "2025-05-28T20:01:26.971201", "action_type": "click_element_by_index", "usage_count": 1, "success_rate": 1.0}, {"selector": "button.submit-activity, [data-action='submit']", "alternatives": ["[aria-label*='Log Activity']", "text=Log Activity", "//button[contains(text(), 'Log Activity')]"], "quality_score": 0.9, "context": {"page_title": "Add Activity", "page_url": "https://demo-emea1.gainsightcloud.com/v1/ui/customersuccess360?cid=1P02IPMAEL4M3CQGY4K5FHU22D2HHT11KCKU#/8fd79692-8e98-4543-b2ec-bc0dba3776ca", "element_text": "Log Activity", "element_attributes": {}}, "discovered_at": "2025-05-28T20:01:26.972215", "action_type": "click_element_by_index", "usage_count": 1, "success_rate": 1.0}]}}
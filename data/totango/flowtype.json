[{"activity_type_id": "renewal", "display_name": "Renewal", "metric_name": "Renewal", "num_of_activities_assign_to": 0, "icon_class": "icon-dollar", "system_type": false, "default_type": false, "disabled": false, "service_id": "45606", "calculate_metric": false}, {"activity_type_id": "support", "display_name": "Support", "metric_name": "Support", "num_of_activities_assign_to": 0, "icon_class": "icon-lifebouy", "system_type": false, "default_type": false, "disabled": false, "service_id": "45606", "calculate_metric": false}, {"activity_type_id": "upsell", "display_name": "Upsell", "metric_name": "Upsell", "num_of_activities_assign_to": 0, "icon_class": "icon-Upsell", "system_type": false, "default_type": false, "disabled": false, "service_id": "45606", "calculate_metric": false}, {"activity_type_id": "escalation", "display_name": "Escalation", "metric_name": "Escalation", "num_of_activities_assign_to": 0, "icon_class": "icon-Escalation", "system_type": false, "default_type": false, "disabled": false, "service_id": "45606", "calculate_metric": false}, {"activity_type_id": "onboarding_101", "display_name": "Onboarding", "metric_name": "Onboarding", "num_of_activities_assign_to": 0, "icon_class": "icon-rocket", "system_type": false, "default_type": false, "disabled": false, "service_id": "45606", "calculate_metric": false}, {"activity_type_id": "adoption", "display_name": "Adoption", "metric_name": "Adoption", "num_of_activities_assign_to": 0, "icon_class": "icon-leaf", "system_type": false, "default_type": true, "disabled": false, "service_id": "45606", "calculate_metric": false}, {"service_id": "45606", "activity_type_id": "risk_1560979263618", "display_name": "Risk", "icon_class": "icon-flag", "system_type": false, "default_type": false, "disabled": false, "calculate_metric": false, "metric_name": "Risk"}, {"service_id": "45606", "activity_type_id": "product_transition_1630456786595", "display_name": "Product Transition", "icon_class": "icon-gavel", "system_type": false, "default_type": false, "disabled": false, "calculate_metric": false, "metric_name": "Product Transition"}, {"service_id": "45606", "activity_type_id": "intelligence_1561140678082", "display_name": "Intelligence", "icon_class": "icon-lightbulb-o", "system_type": false, "default_type": false, "disabled": false, "calculate_metric": false, "metric_name": "Intelligence"}, {"service_id": "45606", "activity_type_id": "services_1631204797082", "display_name": "Services", "icon_class": "icon-bolt", "system_type": false, "default_type": false, "disabled": false, "calculate_metric": true, "metric_name": "Services"}, {"service_id": "45606", "activity_type_id": "inbound_1631240727463", "display_name": "Inbound", "icon_class": "icon-magic", "system_type": false, "default_type": false, "disabled": false, "calculate_metric": false, "metric_name": "Inbound"}, {"service_id": "45606", "activity_type_id": "design_partner_1635451768576", "display_name": "Design Partner", "icon_class": "icon-puzzle-piece", "system_type": false, "default_type": false, "disabled": false, "calculate_metric": false, "metric_name": "Design Partner"}, {"service_id": "45606", "activity_type_id": "nps_1654203738879", "display_name": "NPS", "icon_class": "icon-pie-chart", "system_type": false, "default_type": false, "disabled": false, "calculate_metric": false, "metric_name": "NPS"}, {"service_id": "45606", "activity_type_id": "business_review_1628207371592", "display_name": "Business Review", "icon_class": "icon-Relationship", "system_type": false, "default_type": false, "disabled": false, "calculate_metric": false, "metric_name": "Business Review"}, {"service_id": "45606", "activity_type_id": "journey_1713548309375", "display_name": "Journey", "icon_class": "icon-heart", "system_type": false, "default_type": false, "disabled": false, "calculate_metric": false, "metric_name": "Journey"}, {"service_id": "45606", "activity_type_id": "lifecycle_1714094340032", "display_name": "Lifecycle", "icon_class": "icon-map-marker", "system_type": false, "default_type": false, "disabled": false, "calculate_metric": false, "metric_name": "Lifecycle"}]
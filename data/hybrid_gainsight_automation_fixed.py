#!/usr/bin/env python3
"""
🎭 HYBRID GAINSIGHT AUTOMATION SCRIPT - FIXED VERSION
✅ Proper CSS selectors, error handling, robust automation
🧠 Combines LLM brain + precise automation
"""

import asyncio
from playwright.async_api import async_playwright

async def hybrid_gainsight_automation():
    """Fixed hybrid automation with proper selectors"""
    
    async with async_playwright() as playwright:
        browser = await playwright.chromium.launch(
            headless=False,
            args=['--no-sandbox', '--disable-dev-shm-usage']
        )
        
        try:
            page = await browser.new_page()
            page.set_default_timeout(30000)
            
            print("🎯 HYBRID AUTOMATION STARTING...")
            
            # Step 1: Navigate
            print("🌐 Navigating to Gainsight...")
            await page.goto("https://auth.gainsightcloud.com/login?lc=en")
            await page.wait_for_load_state('domcontentloaded')
            await asyncio.sleep(2)
            
            # Step 2: Subdomain FIRST 
            print("🏢 Subdomain: demo-emea1")
            subdomain_selectors = [
                'input[placeholder*="subdomain" i]',
                'input[placeholder*="organization" i]',
                'input[placeholder*="company" i]',
                'input[name*="subdomain" i]',
                'input[id*="subdomain" i]',
                'input[type="text"]'
            ]
            
            subdomain_entered = False
            for selector in subdomain_selectors:
                element = page.locator(selector).first
                if await element.count() > 0 and await element.is_visible():
                    await element.fill("demo-emea1")
                    subdomain_entered = True
                    print(f"   ✅ Subdomain entered via: {selector}")
                    
                    # Look for continue button
                    continue_buttons = [
                        'button:has-text("Continue")',
                        'button:has-text("Next")',
                        'button[type="submit"]',
                        'input[type="submit"]'
                    ]
                    
                    for btn_selector in continue_buttons:
                        btn = page.locator(btn_selector).first
                        if await btn.count() > 0 and await btn.is_visible():
                            await btn.click()
                            await asyncio.sleep(2)
                            print(f"   🚀 Continue clicked via: {btn_selector}")
                            break
                    break
            
            if not subdomain_entered:
                print("   ℹ️  No subdomain field found (direct login)")
            
            # Step 3: Username
            print("👤 Username: <EMAIL>")
            username_selectors = [
                'input[name="username"]',
                'input[name="email"]',
                'input[type="email"]',
                'input[placeholder*="email" i]',
                'input[placeholder*="username" i]',
                'input[autocomplete="username"]'
            ]
            
            username_entered = False
            for selector in username_selectors:
                element = page.locator(selector).first
                if await element.count() > 0 and await element.is_visible():
                    await element.fill("<EMAIL>")
                    username_entered = True
                    print(f"   ✅ Username entered via: {selector}")
                    break
            
            if not username_entered:
                print("   ❌ Username field not found")
            
            # Step 4: Password  
            print("🔐 Password: ********")
            password_selectors = [
                'input[name="password"]',
                'input[type="password"]',
                'input[autocomplete="current-password"]'
            ]
            
            password_entered = False
            for selector in password_selectors:
                element = page.locator(selector).first
                if await element.count() > 0 and await element.is_visible():
                    await element.fill("@Ramprasad826ie")
                    password_entered = True
                    print(f"   ✅ Password entered via: {selector}")
                    break
            
            if not password_entered:
                print("   ❌ Password field not found")
            
            # Step 5: Submit
            print("🚀 Submit login")
            submit_selectors = [
                'button[type="submit"]',
                'input[type="submit"]',
                'button:has-text("Sign in")',
                'button:has-text("Sign In")',
                'button:has-text("Login")',
                'button:has-text("Log in")'
            ]
            
            login_submitted = False
            for selector in submit_selectors:
                element = page.locator(selector).first
                if await element.count() > 0 and await element.is_visible():
                    await element.click()
                    login_submitted = True
                    print(f"   ✅ Login submitted via: {selector}")
                    break
            
            if not login_submitted:
                print("   ❌ Submit button not found")
            
            await asyncio.sleep(5)
            
            # Step 6: Timeline
            print("📋 Navigate to timeline")
            timeline_url = "https://demo-emea1.gainsightcloud.com/v1/ui/customersuccess360?cid=1P02IPMAEL4M3CQGY4K5FHU22D2HHT11KCKU#/8fd79692-8e98-4543-b2ec-bc0dba3776ca"
            await page.goto(timeline_url)
            await asyncio.sleep(3)
            
            # Click Timeline tab
            timeline_tab = page.locator('a:has-text("Timeline")').first
            if await timeline_tab.count() > 0:
                await timeline_tab.click()
                await asyncio.sleep(2)
                print("   ✅ Timeline tab clicked")
            else:
                print("   ⚠️  Timeline tab not found")
            
            # Step 7: Create activities
            for i in range(3):
                print(f"📝 Creating activity {i+1}/3...")
                
                # Create button
                create_btn = page.locator('button:has-text("Create")').first
                if await create_btn.count() > 0 and await create_btn.is_visible():
                    await create_btn.click()
                    await asyncio.sleep(1)
                    print(f"   ✅ Create button clicked")
                else:
                    print(f"   ❌ Create button not found")
                    continue
                
                # Activity option
                activity_btn = page.locator('a:has-text("Activity")').first
                if await activity_btn.count() > 0 and await activity_btn.is_visible():
                    await activity_btn.click()
                    await asyncio.sleep(2)
                    print(f"   ✅ Activity option clicked")
                else:
                    print(f"   ❌ Activity option not found")
                    continue
                
                # Fill subject
                subject = f"Hybrid Activity #{i+1} - Auto Generated"
                subject_selectors = [
                    'input[name="subject"]',
                    'input[placeholder*="subject" i]',
                    'input[aria-label*="subject" i]'
                ]
                
                subject_filled = False
                for selector in subject_selectors:
                    element = page.locator(selector).first
                    if await element.count() > 0 and await element.is_visible():
                        await element.fill(subject)
                        subject_filled = True
                        print(f"   ✅ Subject filled: {subject}")
                        break
                
                if not subject_filled:
                    print(f"   ❌ Subject field not found")
                    continue
                
                # Submit activity
                submit_selectors = [
                    'button:has-text("Log Activity")',
                    'button:has-text("Save")',
                    'button[type="submit"]'
                ]
                
                activity_submitted = False
                for selector in submit_selectors:
                    element = page.locator(selector).first
                    if await element.count() > 0 and await element.is_visible():
                        await element.click()
                        activity_submitted = True
                        print(f"   ✅ Activity {i+1} submitted")
                        break
                
                if not activity_submitted:
                    print(f"   ❌ Submit button not found for activity {i+1}")
                
                await asyncio.sleep(2)
            
            print("🎉 HYBRID AUTOMATION COMPLETED!")
            print("✅ All activities processed with proper error handling")
            
        except Exception as e:
            print(f"❌ Error: {e}")
            
        finally:
            await page.close()
            await browser.close()

if __name__ == "__main__":
    asyncio.run(hybrid_gainsight_automation())
#!/usr/bin/env python3
"""
Quick test script to verify the Browser Automation Agent setup
"""
import sys
import os
import asyncio
from pathlib import Path

# Add the parent directory to Python path
sys.path.insert(0, str(Path(__file__).parent.parent))

def test_imports():
    """Test that all required modules can be imported"""
    print("🧪 Testing imports...")
    
    try:
        # Test basic imports
        from config import config
        print("✅ Config imported successfully")
        
        from memory_system.memory_manager import MemoryManager
        print("✅ Memory system imported successfully")
        
        from browser_automation.automation_agent import BrowserAutomationAgent
        print("✅ Browser automation imported successfully")
        
        from orchestrator import orchestrator
        print("✅ Orchestrator imported successfully")
        
        # Test LLM imports
        try:
            from langchain_google_genai import ChatGoogleGenerativeAI
            print("✅ Google Gemini support available")
        except ImportError:
            print("⚠️  Google Gemini support not available (langchain-google-genai not installed)")
        
        try:
            from langchain_openai import ChatOpenAI
            print("✅ OpenAI support available")
        except ImportError:
            print("⚠️  OpenAI support not available")
        
        try:
            from langchain_anthropic import ChatAnthropic
            print("✅ Anthropic support available")
        except ImportError:
            print("⚠️  Anthropic support not available")
        
        return True
    except ImportError as e:
        print(f"❌ Import error: {e}")
        return False
    except Exception as e:
        print(f"❌ Unexpected error: {e}")
        return False

def test_config():
    """Test configuration"""
    print("\n🧪 Testing configuration...")
    
    try:
        from config import config
        
        # Check API keys
        if config.llm.google_api_key:
            print("✅ Google API key configured")
        if config.llm.openai_api_key and config.llm.openai_api_key != "your_openai_api_key_here":
            print("✅ OpenAI API key configured")
        if config.llm.anthropic_api_key and config.llm.anthropic_api_key != "your_anthropic_api_key_here":
            print("✅ Anthropic API key configured")
        
        # Test validation
        try:
            config.validate()
            print("✅ Configuration validation passed")
            return True
        except ValueError as e:
            print(f"⚠️  Configuration validation warning: {e}")
            return True  # Warnings are OK for now
            
    except Exception as e:
        print(f"❌ Configuration test failed: {e}")
        return False

def test_directories():
    """Test that required directories exist"""
    print("\n🧪 Testing directories...")
    
    required_dirs = [
        "data",
        "data/screenshots", 
        "data/recordings",
        "data/exports",
        "data/vector_store",
        "logs",
        "logs/conversations"
    ]
    
    all_good = True
    for dir_path in required_dirs:
        if Path(dir_path).exists():
            print(f"✅ Directory exists: {dir_path}")
        else:
            print(f"⚠️  Directory missing: {dir_path} (will be created automatically)")
    
    return all_good

async def test_system_initialization():
    """Test system initialization"""
    print("\n🧪 Testing system initialization...")
    
    try:
        from orchestrator import orchestrator
        
        # Test initialization
        success = await orchestrator.initialize()
        
        if success:
            print("✅ System initialization successful")
            
            # Get status
            status = await orchestrator.get_system_status()
            print(f"✅ System status retrieved")
            print(f"   • Orchestrator initialized: {status['orchestrator']['initialized']}")
            print(f"   • Memory blocks: {status['memory_system']['memory_blocks_count']}")
            print(f"   • Browser automation ready: {status['browser_automation']['total_tasks'] >= 0}")
            
            return True
        else:
            print("❌ System initialization failed")
            return False
            
    except Exception as e:
        print(f"❌ System initialization error: {e}")
        import traceback
        traceback.print_exc()
        return False
    finally:
        # Cleanup
        try:
            await orchestrator.cleanup()
            print("✅ System cleanup completed")
        except:
            pass

async def test_browser_dependencies():
    """Test browser dependencies"""
    print("\n🧪 Testing browser dependencies...")
    
    try:
        import playwright
        print("✅ Playwright installed")
        
        # Test if Chromium is installed using async API
        try:
            from playwright.async_api import async_playwright
            async with async_playwright() as p:
                browser = await p.chromium.launch(headless=True)
                await browser.close()
            print("✅ Chromium browser available")
            return True
        except Exception as e:
            print(f"⚠️  Chromium browser issue: {e}")
            print("   Run: playwright install chromium --with-deps")
            return False
            
    except ImportError:
        print("❌ Playwright not installed")
        return False

async def run_all_tests():
    """Run all tests"""
    print("🚀 Browser Automation Agent - System Test")
    print("=" * 50)
    
    test_results = []
    
    # Run tests
    test_results.append(("Imports", test_imports()))
    test_results.append(("Configuration", test_config()))
    test_results.append(("Directories", test_directories()))
    test_results.append(("Browser Dependencies", await test_browser_dependencies()))
    test_results.append(("System Initialization", await test_system_initialization()))
    
    # Summary
    print("\n" + "=" * 50)
    print("📊 Test Summary:")
    
    passed = 0
    for test_name, result in test_results:
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"   {test_name:<25} {status}")
        if result:
            passed += 1
    
    print(f"\nResults: {passed}/{len(test_results)} tests passed")
    
    if passed == len(test_results):
        print("🎉 All tests passed! The system is ready to use.")
        print("\nNext steps:")
        print("1. Run: python main.py")
        print("2. Try: python examples/basic_usage.py") 
        return True
    else:
        print("⚠️  Some tests failed. Please check the errors above.")
        print("\nCommon fixes:")
        print("1. Install missing dependencies: pip install -r requirements.txt")
        print("2. Install browser: playwright install chromium --with-deps")
        print("3. Check API keys in .env file")
        return False

if __name__ == "__main__":
    success = asyncio.run(run_all_tests())
    sys.exit(0 if success else 1)

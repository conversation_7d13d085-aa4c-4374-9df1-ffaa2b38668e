#!/usr/bin/env python3
"""
Complete Gainsight Mapping Workflow
===================================

This script:
1. Finds your generated JSON file
2. Analyzes current meeting_type_name values
3. Maps them to Gainsight activity types
4. Creates CSV export for Gainsight import

Author: Assistant
Date: January 29, 2025
"""

import json
import pandas as pd
import os
from pathlib import Path
from collections import Counter
from datetime import datetime

class CompleteGainsightMapper:
    def __init__(self):
        """Initialize the complete mapper."""
        
        # Totango to Gainsight mapping rules
        self.mapping_rules = {
            # Default Gainsight Types
            "Email": "Email",
            "Telephone Call": "Call",
            "Web Meeting": "Meeting",
            "Internal Note": "Update",
            
            # Custom Gainsight Types
            "In-Person Meeting": "In-Person Meeting",
            "Gong Call": "Gong Call",
            "Feedback": "Feedback", 
            "Inbound": "Inbound",
            "Slack": "Slack",
            
            # Common variations
            "email": "Email",
            "Email Campaign": "Email",
            "telephone call": "Call",
            "Phone Call": "Call",
            "web meeting": "Meeting",
            "Video Meeting": "Meeting",
            "internal note": "Update",
            "Note": "Update"
        }
        
        self.stats = {
            'total_activities': 0,
            'mapped_activities': 0,
            'mapping_results': Counter()
        }

    def find_json_file(self) -> str:
        """Find the generated JSON file."""
        possible_paths = [
            "/Users/<USER>/Desktop/wildweasel/Browser/ICICI_processed.json",
            "/Users/<USER>/Desktop/totango/ICICI_processed.json",
            "ICICI_processed.json",
            "/Users/<USER>/Desktop/wildweasel/Browser/ICICI_gainsight_ready.json",
            "/Users/<USER>/Desktop/totango/ICICI_gainsight_ready.json"
        ]
        
        for path in possible_paths:
            if Path(path).exists():
                return path
        
        return None

    def map_meeting_type_to_gainsight(self, meeting_type: str) -> str:
        """Map meeting type to Gainsight activity type."""
        if not meeting_type or meeting_type.strip() == "":
            return "Update"
        
        clean_type = meeting_type.strip()
        
        # Direct mapping
        if clean_type in self.mapping_rules:
            return self.mapping_rules[clean_type]
        
        # Case-insensitive mapping
        for key, value in self.mapping_rules.items():
            if clean_type.lower() == key.lower():
                return value
        
        # Pattern matching
        clean_lower = clean_type.lower()
        if "email" in clean_lower or "mail" in clean_lower:
            return "Email"
        elif "call" in clean_lower or "phone" in clean_lower or "telephone" in clean_lower:
            return "Call"
        elif "meeting" in clean_lower or "conference" in clean_lower or "video" in clean_lower:
            return "Meeting"
        elif "slack" in clean_lower or "chat" in clean_lower:
            return "Slack"
        elif "feedback" in clean_lower or "survey" in clean_lower:
            return "Feedback"
        elif "inbound" in clean_lower or "support" in clean_lower:
            return "Inbound"
        elif "gong" in clean_lower:
            return "Gong Call"
        elif "person" in clean_lower and "meeting" in clean_lower:
            return "In-Person Meeting"
        
        # Default fallback
        return "Update"

    def analyze_and_map_json(self, input_file: str) -> dict:
        """Analyze and map the JSON file."""
        print(f"🔄 Processing: {input_file}")
        
        with open(input_file, 'r', encoding='utf-8') as f:
            activities = json.load(f)
        
        self.stats['total_activities'] = len(activities)
        print(f"📊 Found {len(activities)} activities")
        
        # Analyze current meeting types
        current_meeting_types = Counter()
        mapped_activities = []
        
        for activity in activities:
            properties = activity.get('properties', {})
            
            if 'meeting_type_name' in properties:
                original_type = properties['meeting_type_name']
                current_meeting_types[original_type] += 1
                
                # Map to Gainsight type
                gainsight_type = self.map_meeting_type_to_gainsight(original_type)
                
                # Update the activity
                updated_activity = activity.copy()
                updated_properties = properties.copy()
                updated_properties['original_meeting_type_name'] = original_type
                updated_properties['meeting_type_name'] = gainsight_type
                updated_properties['gainsight_activity_type'] = gainsight_type
                updated_activity['properties'] = updated_properties
                
                self.stats['mapping_results'][f"{original_type} → {gainsight_type}"] += 1
                self.stats['mapped_activities'] += 1
                
            else:
                # No meeting type - assign Update
                updated_activity = activity.copy()
                if 'properties' not in updated_activity:
                    updated_activity['properties'] = {}
                
                updated_activity['properties']['original_meeting_type_name'] = "No Meeting Type"
                updated_activity['properties']['meeting_type_name'] = "Update"
                updated_activity['properties']['gainsight_activity_type'] = "Update"
                
                self.stats['mapping_results']["No Meeting Type → Update"] += 1
            
            mapped_activities.append(updated_activity)
        
        return {
            'activities': mapped_activities,
            'current_meeting_types': current_meeting_types
        }

    def create_csv_export(self, activities: list, output_file: str):
        """Create CSV export for Gainsight."""
        print(f"📄 Creating CSV export: {output_file}")
        
        csv_rows = []
        
        for activity in activities:
            properties = activity.get('properties', {})
            
            # Extract data
            activity_id = activity.get('id', 'unknown')
            timestamp = activity.get('timestamp', 0)
            activity_type = activity.get('type', 'unknown')
            
            # Gainsight fields
            gainsight_activity_type = properties.get('gainsight_activity_type', 'Update')
            original_meeting_type = properties.get('original_meeting_type_name', 'Unknown')
            
            # Generate subject and content
            subject = self.generate_subject(activity)
            content_html = self.generate_content(activity)
            content_plain = self.strip_html(content_html)
            
            # Format timestamp
            activity_date = self.format_timestamp(timestamp)
            
            csv_row = {
                "Activity ID": activity_id,
                "Subject": subject,
                "Activity Date": activity_date,
                "Activity Type": gainsight_activity_type,
                "Content (HTML)": content_html,
                "Plain Text": content_plain,
                "Author Name": "Ram Prasad",
                "Author Email": "<EMAIL>",
                "Flow Type": "1I00EBMOY66ROGCBY63I51PAXG5OJRPK37AQ",
                "Touchpoint Reason": "1I00J1INQCQ6GQ3T2MWULITYE9ASDFPQ2KD3",
                "Internal Attendees": "Ram Prasad <<EMAIL>>",
                "External Attendees": "",
                "Company": "ICICI",
                "Original Activity Type": activity_type,
                "Original Meeting Type": original_meeting_type,
                "Source": "ICICI_MIGRATION"
            }
            
            csv_rows.append(csv_row)
        
        # Create DataFrame and save
        df = pd.DataFrame(csv_rows)
        df.to_csv(output_file, index=False, encoding='utf-8')
        
        print(f"✅ CSV created with {len(csv_rows)} rows")
        return output_file

    def generate_subject(self, activity: dict) -> str:
        """Generate subject for activity."""
        properties = activity.get('properties', {})
        activity_type = activity.get('type', 'Activity')
        
        name = properties.get('name', '')
        display_name = properties.get('display_name', '')
        title = properties.get('title', '')
        
        if name:
            return f"ICICI: {name}"
        elif display_name:
            return f"ICICI: {display_name}"
        elif title:
            return f"ICICI: {title}"
        else:
            return f"ICICI {activity_type.replace('_', ' ').title()}"

    def generate_content(self, activity: dict) -> str:
        """Generate HTML content for activity."""
        properties = activity.get('properties', {})
        activity_type = activity.get('type', 'activity')
        
        description = properties.get('description', '')
        if description:
            return f"<p>{description}</p>"
        
        # Generate based on activity type
        if activity_type == 'automated_attribute_change':
            display_name = properties.get('display_name', 'attribute')
            new_value = properties.get('new_value', 'updated')
            return f"<p>Automated update: {display_name} changed to {new_value}</p>"
        elif activity_type == 'campaign_touch':
            name = properties.get('name', 'campaign')
            return f"<p>Campaign: {name} executed for ICICI Bank</p>"
        else:
            return f"<p>ICICI Bank activity: {activity_type.replace('_', ' ')}</p>"

    def format_timestamp(self, timestamp: int) -> str:
        """Format timestamp for CSV."""
        try:
            if timestamp > 10**10:  # milliseconds
                timestamp = timestamp / 1000
            dt = datetime.fromtimestamp(timestamp)
            return dt.strftime('%Y-%m-%d %H:%M:%S')
        except:
            return ""

    def strip_html(self, html: str) -> str:
        """Strip HTML tags."""
        import re
        return re.sub(r'<[^>]+>', '', html) if html else ""

    def generate_report(self, current_meeting_types: Counter) -> str:
        """Generate mapping report."""
        report = []
        report.append("=" * 70)
        report.append("GAINSIGHT ACTIVITY TYPE MAPPING REPORT")
        report.append("=" * 70)
        report.append(f"📊 Total Activities: {self.stats['total_activities']:,}")
        report.append(f"🎯 Mapped Activities: {self.stats['mapped_activities']:,}")
        report.append("")
        
        if current_meeting_types:
            report.append("📋 ORIGINAL MEETING TYPES FOUND:")
            for meeting_type, count in current_meeting_types.most_common():
                report.append(f"   • '{meeting_type}': {count} activities")
            report.append("")
        
        if self.stats['mapping_results']:
            report.append("🔄 APPLIED MAPPINGS:")
            for mapping, count in self.stats['mapping_results'].most_common():
                report.append(f"   • {mapping}: {count} activities")
            report.append("")
        
        report.append("✅ All activities now have Gainsight-compatible activity types")
        report.append("=" * 70)
        
        return "\n".join(report)

    def run_complete_mapping(self):
        """Run the complete mapping workflow."""
        print("🚀 COMPLETE GAINSIGHT MAPPING WORKFLOW")
        print("=" * 50)
        
        # Step 1: Find JSON file
        input_file = self.find_json_file()
        if not input_file:
            print("❌ No JSON file found!")
            print("Please ensure you have run your converter first.")
            return
        
        print(f"✅ Found JSON file: {input_file}")
        
        # Step 2: Analyze and map
        result = self.analyze_and_map_json(input_file)
        
        # Step 3: Save mapped JSON
        output_json = input_file.replace('.json', '_gainsight_mapped.json')
        with open(output_json, 'w', encoding='utf-8') as f:
            json.dump(result['activities'], f, indent=2, ensure_ascii=False)
        print(f"💾 Saved mapped JSON: {output_json}")
        
        # Step 4: Create CSV export
        output_csv = input_file.replace('.json', '_gainsight_import.csv')
        self.create_csv_export(result['activities'], output_csv)
        
        # Step 5: Generate report
        report = self.generate_report(result['current_meeting_types'])
        print(f"\n{report}")
        
        print(f"\n🎉 MAPPING COMPLETED!")
        print(f"📄 Mapped JSON: {output_json}")
        print(f"📊 CSV for import: {output_csv}")
        print(f"🚀 Ready for Gainsight Timeline import!")

def main():
    """Main function."""
    mapper = CompleteGainsightMapper()
    mapper.run_complete_mapping()

if __name__ == "__main__":
    main()

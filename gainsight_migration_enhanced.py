#!/usr/bin/env python3
"""
Enhanced ICICI to Gainsight Migration System
Addresses the missing drafts API call and provides complete migration solution
"""

import json
import asyncio
import aiohttp
from pathlib import Path
from datetime import datetime
from collections import Counter
import sys

def load_converted_activities():
    """Load the converted activities from the extraction script"""
    activities_path = Path("/Users/<USER>/Desktop/wildweasel/Browser/data/icici_gainsight_ready.json")
    
    if not activities_path.exists():
        print("❌ Converted activities file not found. Please run extract_icici_touchpoints.py first.")
        return None
    
    with open(activities_path, "r") as f:
        activities = json.load(f)
    
    print(f"📊 Loaded {len(activities)} converted activities")
    return activities

async def get_draft_id(session, api_key, activity_payload):
    """Get unique draft ID from Gainsight drafts API"""
    drafts_url = "https://demo-emea1.gainsightcloud.com/v1/ant/v2/activity/drafts"
    headers = {
        "Authorization": f"Bearer {api_key}",
        "Content-Type": "application/json"
    }
    
    try:
        async with session.post(drafts_url, json=activity_payload, headers=headers) as response:
            if response.status == 200:
                draft_data = await response.json()
                # Extract the ID from the draft response
                draft_id = draft_data.get("id")
                return draft_id
            else:
                print(f"❌ Draft API failed with status {response.status}")
                return None
    except Exception as e:
        print(f"❌ Draft API error: {e}")
        return None

async def post_activity_to_gainsight(session, api_key, activity_payload, activity_id):
    """Post activity to Gainsight with the proper ID"""
    activity_url = "https://demo-emea1.gainsightcloud.com/v1/ant/v2/activity"
    headers = {
        "Authorization": f"Bearer {api_key}",
        "Content-Type": "application/json"
    }
    
    # Set the proper ID in the payload
    activity_payload["id"] = activity_id
    
    try:
        async with session.post(activity_url, json=activity_payload, headers=headers) as response:
            if response.status == 200:
                return True, await response.json()
            else:
                error_text = await response.text()
                return False, f"Status {response.status}: {error_text}"
    except Exception as e:
        return False, str(e)

async def migrate_activities_via_api(api_key, activities, use_drafts=True):
    """Migrate activities to Gainsight via API with proper ID handling"""
    print(f"\n🚀 Starting API migration of {len(activities)} activities...")
    print(f"📡 Using drafts API: {use_drafts}")
    
    success_count = 0
    failed_count = 0
    
    async with aiohttp.ClientSession() as session:
        for i, activity in enumerate(activities):
            subject = activity["note"]["subject"]
            print(f"\n📝 Processing activity {i+1}/{len(activities)}: {subject[:50]}...")
            
            if use_drafts:
                # Step 1: Get draft ID
                print("  🔄 Getting draft ID...")
                draft_id = await get_draft_id(session, api_key, activity)
                
                if not draft_id:
                    print("  ❌ Failed to get draft ID, skipping...")
                    failed_count += 1
                    continue
                
                print(f"  ✅ Got draft ID: {draft_id}")
                
                # Step 2: Post activity with draft ID
                success, result = await post_activity_to_gainsight(session, api_key, activity, draft_id)
            else:
                # Direct posting without drafts API
                success, result = await post_activity_to_gainsight(session, api_key, activity, None)
            
            if success:
                print("  ✅ Successfully posted to Gainsight")
                success_count += 1
            else:
                print(f"  ❌ Failed to post: {result}")
                failed_count += 1
            
            # Small delay to avoid rate limiting
            await asyncio.sleep(0.5)
    
    print(f"\n📊 Migration Results:")
    print(f"   ✅ Successful: {success_count}")
    print(f"   ❌ Failed: {failed_count}")
    print(f"   📈 Success Rate: {success_count/(success_count+failed_count)*100:.1f}%")

async def migrate_activities_via_browser():
    """Migrate activities using browser automation"""
    print("\n🌐 Starting browser automation migration...")
    
    # Import the orchestrator for browser automation
    try:
        sys.path.insert(0, "/Users/<USER>/Desktop/wildweasel/Browser")
        from orchestrator import orchestrator
        
        await orchestrator.initialize()
        
        # Navigate to Gainsight
        login_task = """
        Navigate to https://demo-emea1.gainsightcloud.com and login.
        Wait for the dashboard to load completely.
        """
        
        await orchestrator.execute_task(
            login_task,
            context={"purpose": "Login to Gainsight for activity migration"},
            record_session=True
        )
        
        # Load activities for browser automation
        activities = load_converted_activities()
        if not activities:
            return
        
        # Process activities in smaller batches for browser automation
        batch_size = 5  # Process 5 activities at a time
        
        for i in range(0, min(len(activities), 20), batch_size):  # Limit to 20 for testing
            batch = activities[i:i+batch_size]
            print(f"\n📦 Processing batch {i//batch_size + 1}: {len(batch)} activities")
            
            for j, activity in enumerate(batch):
                activity_info = activity["note"]
                print(f"\n  🔄 Creating activity {i+j+1}: {activity_info['subject'][:50]}...")
                
                create_task = f"""
                Create a new {activity_info['type']} activity in Gainsight with the following details:
                - Subject: {activity_info['subject']}
                - Type: {activity_info['type']}
                - Content: {activity_info['plainText'][:200]}...
                - Company: ICICI Bank
                - Date: {datetime.fromtimestamp(activity_info['activityDate']/1000).strftime('%Y-%m-%d')}
                
                Click on the activities or timeline section, then create new activity, 
                fill in all the fields, and save the activity.
                """
                
                try:
                    result = await orchestrator.execute_task(
                        create_task,
                        context={
                            "activity": activity,
                            "batch_number": i//batch_size + 1,
                            "activity_index": j + 1
                        },
                        learn_from_execution=True,
                        use_playwright_optimization=True
                    )
                    print(f"  ✅ Successfully created activity")
                except Exception as e:
                    print(f"  ❌ Failed to create activity: {e}")
        
        await orchestrator.cleanup()
        print("\n✅ Browser automation migration complete!")
        
    except ImportError:
        print("❌ Browser automation not available. Please ensure orchestrator is properly configured.")
    except Exception as e:
        print(f"❌ Browser automation error: {e}")

def show_migration_summary():
    """Show comprehensive migration summary"""
    print("\n" + "="*80)
    print("📊 ICICI TO GAINSIGHT MIGRATION SUMMARY")
    print("="*80)
    
    # Show data type analysis
    print("\n📋 DATA TYPES ANALYSIS:")
    print("   🔹 Touchpoint Types (ID.json): 9 types")
    print("     Internal Note (2491), Email (27361), Web meeting (12597), etc.")
    print("   🔹 Flow Types (flowtype.json): 16 types") 
    print("     Renewal, Support, Adoption, Onboarding, etc.")
    
    # Show converted activities
    activities = load_converted_activities()
    if activities:
        type_counts = Counter(a["note"]["type"] for a in activities)
        print(f"\n✅ CONVERTED ACTIVITIES: {len(activities)} total")
        for activity_type, count in type_counts.most_common():
            print(f"   • {activity_type}: {count} activities")
    
    print("\n🔧 MIGRATION OPTIONS:")
    print("   1. API Migration (Recommended)")
    print("      - Uses Gainsight REST API")
    print("      - Handles drafts API for unique IDs")
    print("      - Faster and more reliable")
    print("   2. Browser Automation")
    print("      - Uses UI automation")
    print("      - Good for complex scenarios")
    print("      - Records actions for learning")
    
    print("\n🚀 READY TO MIGRATE!")
    print("   Files generated:")
    print("   📁 icici_touchpoints.json - Extracted activities")
    print("   📁 icici_gainsight_ready.json - Converted payload")
    print("   📁 gainsight_migration_enhanced.py - This migration script")

async def main():
    """Main migration orchestrator"""
    print("🚀 ENHANCED ICICI TO GAINSIGHT MIGRATION SYSTEM")
    print("=" * 60)
    
    # Show summary first
    show_migration_summary()
    
    # Load converted activities
    activities = load_converted_activities()
    if not activities:
        print("\n❌ No activities to migrate. Please run extract_icici_touchpoints.py first.")
        return
    
    print(f"\n🎯 Ready to migrate {len(activities)} activities")
    print("\nChoose migration method:")
    print("1. API Migration (with drafts API)")
    print("2. API Migration (direct, no drafts)")
    print("3. Browser Automation")
    print("4. Show summary only")
    
    choice = input("\nEnter choice (1-4): ").strip()
    
    if choice == "1":
        api_key = input("\nEnter Gainsight API key: ").strip()
        if api_key:
            await migrate_activities_via_api(api_key, activities, use_drafts=True)
        else:
            print("❌ API key required for migration")
    
    elif choice == "2":
        api_key = input("\nEnter Gainsight API key: ").strip()
        if api_key:
            await migrate_activities_via_api(api_key, activities, use_drafts=False)
        else:
            print("❌ API key required for migration")
    
    elif choice == "3":
        await migrate_activities_via_browser()
    
    elif choice == "4":
        print("\n✅ Summary displayed above.")
    
    else:
        print("❌ Invalid choice")
    
    print("\n🎉 Migration process complete!")

if __name__ == "__main__":
    asyncio.run(main())

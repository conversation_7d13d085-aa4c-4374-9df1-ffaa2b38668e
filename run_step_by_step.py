#!/usr/bin/env python3
"""
🚀 STEP-BY-STEP ICICI to Gainsight Migration
============================================

Alternative to the master script - runs each step individually
Use this if you want to see each step's output or if the master script has issues

Author: Assistant
Date: January 29, 2025
"""

import os
import sys
from pathlib import Path

def print_step_banner(step_num, title, description):
    """Print step banner."""
    print(f"\n{'='*60}")
    print(f"🚀 STEP {step_num}: {title}")
    print(f"{'='*60}")
    print(f"📋 {description}")
    print()

def check_file_exists(file_path, description):
    """Check if a file exists and show its size."""
    if Path(file_path).exists():
        size = Path(file_path).stat().st_size
        print(f"✅ {description}: {size:,} bytes")
        return True
    else:
        print(f"❌ {description}: Not found")
        return False

def main():
    """Run step-by-step migration."""
    print("🚀 STEP-BY-STEP ICICI TO GAINSIGHT MIGRATION")
    print("=" * 50)
    
    print("📋 This script will guide you through each step:")
    print("   1. Map meeting types to Gainsight activity types")
    print("   2. Generate Demo CSV (Ram Prasad)")
    print("   3. Generate Real CSV (ICICI users)")
    print("   4. Show file locations")
    
    input("\nPress Enter to start...")
    
    # Step 1: Check prerequisites
    print_step_banner(0, "Prerequisites Check", "Checking required source files")
    
    all_good = True
    all_good &= check_file_exists("/Users/<USER>/Desktop/totango/ICICI_processed.json", "ICICI Activities")
    all_good &= check_file_exists("/Users/<USER>/Desktop/totango/flowtype.json", "Flow Types")
    all_good &= check_file_exists("/Users/<USER>/Desktop/totango/Touchpoint_reason.JSON", "Touchpoint Reasons")
    
    if not all_good:
        print("\n❌ Missing required files. Please ensure all source files are in place.")
        return 1
    
    input("\nPress Enter to continue to Step 1...")
    
    # Step 1: Mapping
    print_step_banner(1, "Gainsight Activity Type Mapping", "Maps all 322 activities to proper Gainsight activity types")
    
    print("🚀 Running: python3 complete_gainsight_mapping.py")
    print("📄 This will create:")
    print("   • ICICI_processed_gainsight_mapped.json (mapped activities)")
    print("   • ICICI_processed_gainsight_import.csv (basic CSV)")
    print()
    
    os.system("python3 complete_gainsight_mapping.py")
    
    # Check if Step 1 output exists
    mapped_json = "/Users/<USER>/Desktop/totango/ICICI_processed_gainsight_mapped.json"
    if not Path(mapped_json).exists():
        print("\n❌ Step 1 failed - mapped JSON not created")
        return 1
    
    print(f"\n✅ Step 1 completed successfully!")
    check_file_exists(mapped_json, "Mapped JSON")
    
    input("\nPress Enter to continue to Step 2...")
    
    # Step 2: Demo CSV
    print_step_banner(2, "Generate Demo CSV", "Creates CSV with Ram Prasad as author for testing")
    
    print("🚀 Running: python3 enhanced_csv_exporter.py (Demo mode)")
    print("📄 This will create:")
    print("   • ICICI_processed_gainsight_mapped_enhanced_demo.csv")
    print("   • Uses Ram Prasad as author for all 322 activities")
    print("   • Perfect for testing Gainsight import")
    print()
    
    os.system("echo '1' | python3 enhanced_csv_exporter.py")
    
    # Check if Step 2 output exists
    demo_csv = "ICICI_processed_gainsight_mapped_enhanced_demo.csv"
    if not Path(demo_csv).exists():
        print("\n❌ Step 2 failed - demo CSV not created")
        return 1
    
    print(f"\n✅ Step 2 completed successfully!")
    check_file_exists(demo_csv, "Demo CSV")
    
    input("\nPress Enter to continue to Step 3...")
    
    # Step 3: Real CSV
    print_step_banner(3, "Generate Real CSV", "Creates CSV with real ICICI users for production")
    
    print("🚀 Running: python3 enhanced_csv_exporter.py (Real mode)")
    print("📄 This will create:")
    print("   • ICICI_processed_gainsight_mapped_enhanced_real.csv")
    print("   • Uses real ICICI users as authors")
    print("   • Ready for production migration")
    print()
    
    os.system("echo '2' | python3 enhanced_csv_exporter.py")
    
    # Check if Step 3 output exists
    real_csv = "ICICI_processed_gainsight_mapped_enhanced_real.csv"
    if not Path(real_csv).exists():
        print("\n❌ Step 3 failed - real CSV not created")
        return 1
    
    print(f"\n✅ Step 3 completed successfully!")
    check_file_exists(real_csv, "Real CSV")
    
    input("\nPress Enter to see final results...")
    
    # Step 4: Show results
    print_step_banner(4, "Final Results", "All generated files and their locations")
    
    print("📁 GENERATED FILES:")
    print()
    
    # Main output files
    print("🎯 MAIN OUTPUT FILES (Current Directory):")
    check_file_exists("ICICI_processed_gainsight_mapped_enhanced_demo.csv", "Demo CSV (Testing)")
    check_file_exists("ICICI_processed_gainsight_mapped_enhanced_real.csv", "Real CSV (Production)")
    
    print()
    print("📊 INTERMEDIATE FILES (Totango Directory):")
    check_file_exists("/Users/<USER>/Desktop/totango/ICICI_processed_gainsight_mapped.json", "Mapped JSON")
    check_file_exists("/Users/<USER>/Desktop/totango/ICICI_processed_gainsight_import.csv", "Basic CSV")
    
    print()
    print("🚀 HOW TO USE:")
    print("   For Testing:")
    print("   • Use: ICICI_processed_gainsight_mapped_enhanced_demo.csv")
    print("   • Import into Gainsight Timeline")
    print("   • Verify all 322 activities import with Ram Prasad as author")
    print()
    print("   For Production:")
    print("   • Use: ICICI_processed_gainsight_mapped_enhanced_real.csv")
    print("   • Import into Gainsight Timeline")
    print("   • Verify all 322 activities import with real ICICI users")
    
    print()
    print("🎉 MIGRATION COMPLETED SUCCESSFULLY!")
    print("✅ All 322 ICICI activities processed")
    print("✅ Gainsight activity types mapped")
    print("✅ Flow types and touchpoint reasons mapped")
    print("✅ Demo and Real CSV files generated")
    print("✅ Ready for Gainsight Timeline import!")
    
    return 0

if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)

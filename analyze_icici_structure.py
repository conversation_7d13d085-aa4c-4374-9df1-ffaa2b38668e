#!/usr/bin/env python3
"""
Analyze ICICI Data Structure
Check the actual structure of ICICI activities to understand:
1. How many activities have touchpoint_tags vs don't have them
2. What the enrichedUsers structure looks like for author info
"""

import json
from pathlib import Path
from collections import Counter

def analyze_icici_data():
    """Analyze the ICICI data structure."""
    print("🔍 ANALYZING ICICI DATA STRUCTURE")
    print("=" * 50)
    
    icici_file = "/Users/<USER>/Desktop/totango/ICICI_processed.json"
    
    if not Path(icici_file).exists():
        print(f"❌ File not found: {icici_file}")
        return
    
    with open(icici_file, 'r') as f:
        activities = json.load(f)
    
    print(f"📊 Total activities: {len(activities)}")
    
    # Analyze touchpoint_tags
    print(f"\n📋 TOUCHPOINT TAGS ANALYSIS:")
    print("=" * 40)
    
    activities_with_touchpoint_tags = 0
    activities_without_touchpoint_tags = 0
    touchpoint_tag_values = Counter()
    
    for i, activity in enumerate(activities):
        properties = activity.get('properties', {})
        
        if 'touchpoint_tags' in properties:
            activities_with_touchpoint_tags += 1
            touchpoint_tags = properties['touchpoint_tags']
            
            # Handle different formats
            if isinstance(touchpoint_tags, list):
                for tag in touchpoint_tags:
                    touchpoint_tag_values[str(tag)] += 1
            else:
                touchpoint_tag_values[str(touchpoint_tags)] += 1
            
            # Show first few examples
            if i < 5:
                print(f"   Example {i+1}: touchpoint_tags = {touchpoint_tags}")
        else:
            activities_without_touchpoint_tags += 1
    
    print(f"\n📊 Touchpoint Tags Summary:")
    print(f"   • Activities WITH touchpoint_tags: {activities_with_touchpoint_tags}")
    print(f"   • Activities WITHOUT touchpoint_tags: {activities_without_touchpoint_tags}")
    
    if touchpoint_tag_values:
        print(f"\n📋 Touchpoint Tag Values Found:")
        for tag_value, count in touchpoint_tag_values.most_common(10):
            print(f"   • '{tag_value}': {count} activities")
    
    # Analyze enrichedUsers structure
    print(f"\n👤 ENRICHED USERS ANALYSIS:")
    print("=" * 40)
    
    activities_with_enriched_users = 0
    enriched_user_examples = []
    
    for i, activity in enumerate(activities):
        properties = activity.get('properties', {})
        
        if 'enrichedUsers' in properties:
            activities_with_enriched_users += 1
            enriched_users = properties['enrichedUsers']
            
            # Collect examples
            if len(enriched_user_examples) < 5:
                enriched_user_examples.append({
                    'activity_index': i,
                    'enrichedUsers': enriched_users
                })
    
    print(f"📊 Activities with enrichedUsers: {activities_with_enriched_users}")
    
    if enriched_user_examples:
        print(f"\n📋 EnrichedUsers Structure Examples:")
        for example in enriched_user_examples:
            print(f"\n   Activity {example['activity_index'] + 1}:")
            enriched_users = example['enrichedUsers']
            
            if isinstance(enriched_users, list):
                print(f"     Type: List with {len(enriched_users)} users")
                for j, user in enumerate(enriched_users[:2]):  # Show first 2 users
                    print(f"     User {j+1}:")
                    if isinstance(user, dict):
                        for key, value in user.items():
                            print(f"       {key}: {value}")
                    else:
                        print(f"       {user}")
            elif isinstance(enriched_users, dict):
                print(f"     Type: Dictionary")
                for key, value in enriched_users.items():
                    print(f"       {key}: {value}")
            else:
                print(f"     Type: {type(enriched_users)}")
                print(f"     Value: {enriched_users}")
    
    # Check for other user-related fields
    print(f"\n👤 OTHER USER FIELDS ANALYSIS:")
    print("=" * 40)
    
    user_fields = ['user_name', 'user_email', 'entity_name', 'fullName', 'email']
    user_field_counts = Counter()
    
    for activity in activities:
        properties = activity.get('properties', {})
        
        for field in user_fields:
            if field in properties:
                user_field_counts[field] += 1
    
    print(f"📊 User-related fields found:")
    for field, count in user_field_counts.most_common():
        print(f"   • {field}: {count} activities")
    
    # Show sample activity structure
    print(f"\n📋 SAMPLE ACTIVITY STRUCTURE:")
    print("=" * 40)
    
    if activities:
        sample_activity = activities[0]
        print(f"Sample activity keys: {list(sample_activity.keys())}")
        
        properties = sample_activity.get('properties', {})
        print(f"Sample properties keys: {list(properties.keys())[:10]}...")  # First 10 keys

def main():
    """Main analysis function."""
    analyze_icici_data()

if __name__ == "__main__":
    main()

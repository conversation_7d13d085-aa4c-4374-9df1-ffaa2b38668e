#!/usr/bin/env python3
"""
Auto-generated migration script for ICICI activities to Gainsight
Generated on: 2025-05-28 20:07:12
Total activities: 37
"""

import json
import asyncio
from pathlib import Path

# Load the converted activities
activities_path = Path("/Users/<USER>/Desktop/wildweasel/Browser/data/icici_gainsight_ready.json")
with open(activities_path, "r") as f:
    activities = json.load(f)

print(f"📊 Loaded {len(activities)} activities for migration")

# Option 1: API Migration (requires API key)
async def migrate_via_api(api_key):
    """Post activities to Gainsight via API"""
    import aiohttp
    
    api_url = "https://demo-emea1.gainsightcloud.com/v1/ant/v2/activity"
    headers = {
        "Authorization": f"Bearer {api_key}",
        "Content-Type": "application/json"
    }
    
    async with aiohttp.ClientSession() as session:
        for i, activity in enumerate(activities):
            print(f"Posting activity {i+1}/{len(activities)}: {activity['note']['subject'][:50]}...")
            
            async with session.post(api_url, json=activity, headers=headers) as response:
                if response.status == 200:
                    print(f"✅ Success")
                else:
                    print(f"❌ Failed: {response.status}")

# Option 2: UI Migration (using browser automation)
async def migrate_via_ui():
    """Create activities via Gainsight UI"""
    import sys
    sys.path.insert(0, "/Users/<USER>/Desktop/wildweasel/Browser")
    from orchestrator import orchestrator
    
    await orchestrator.initialize()
    
    # Login to Gainsight
    await orchestrator.execute_task(
        "Navigate to https://demo-emea1.gainsightcloud.com and login",
        context={},
        record_session=True
    )
    
    # Create each activity
    for i, activity in enumerate(activities[:10]):  # Limit to 10 for testing
        print(f"\nCreating activity {i+1}: {activity['note']['subject'][:50]}...")
        
        task = f"""
        Create a new {activity['note']['type']} activity with:
        - Subject: {activity['note']['subject']}
        - Content: {activity['note']['plainText'][:100]}...
        - Company: ICICI Bank
        """
        
        await orchestrator.execute_task(
            task,
            context={"activity": activity},
            learn_from_execution=True,
            use_playwright_optimization=True
        )
    
    await orchestrator.cleanup()

# Main execution
async def main():
    print("🚀 ICICI to Gainsight Migration")
    print("Choose migration method:")
    print("1. API (requires API key)")
    print("2. UI Automation")
    
    choice = input("Enter choice (1 or 2): ")
    
    if choice == "1":
        api_key = input("Enter Gainsight API key: ")
        await migrate_via_api(api_key)
    else:
        await migrate_via_ui()
    
    print("\n✅ Migration complete!")

if __name__ == "__main__":
    asyncio.run(main())

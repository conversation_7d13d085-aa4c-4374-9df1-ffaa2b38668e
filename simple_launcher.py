#!/usr/bin/env python3
"""
🎯 SUPER SIMPLE LAUNCHER
No async issues, just works!
"""

import subprocess
import sys
from pathlib import Path

def check_system():
    """Quick system check"""
    print("🔍 Checking system...")
    
    # Check key files
    key_files = [
        "gainsight_ui_automator.py",
        "data/icici_gainsight_ready.json",
        "enhanced_llm_client.py"
    ]
    
    for file in key_files:
        if Path(file).exists():
            print(f"  ✅ {file}")
        else:
            print(f"  ❌ {file} - Missing")
    
    print("  ✅ System check complete")

def run_ui_automation(max_activities=3):
    """Run UI automation safely"""
    print(f"\n🚀 Starting UI automation for {max_activities} activities...")
    print("📱 Browser will open automatically")
    print("🔐 Using your configured credentials")
    
    try:
        # Run the automation
        cmd = [sys.executable, "gainsight_ui_automator.py", "--max-activities", str(max_activities)]
        result = subprocess.run(cmd, cwd="/Users/<USER>/Desktop/wildweasel/Browser")
        
        if result.returncode == 0:
            print("✅ Migration completed!")
        else:
            print("⚠️  Check output for any issues")
            
    except Exception as e:
        print(f"❌ Error: {e}")

def run_status_check():
    """Run system status check"""
    print("\n📊 Running system status check...")
    
    try:
        result = subprocess.run([sys.executable, "quick_status_check.py"], 
                              cwd="/Users/<USER>/Desktop/wildweasel/Browser")
    except Exception as e:
        print(f"❌ Error: {e}")

def run_llm_test():
    """Test LLM integration"""
    print("\n🤖 Testing LLM integration...")
    
    try:
        cmd = [sys.executable, "-c", "from enhanced_llm_client import test_llm_client; import asyncio; asyncio.run(test_llm_client())"]
        result = subprocess.run(cmd, cwd="/Users/<USER>/Desktop/wildweasel/Browser")
    except Exception as e:
        print(f"❌ Error: {e}")

def show_menu():
    """Show main menu"""
    print("\n🎯 SIMPLE BROWSER AUTOMATION LAUNCHER")
    print("=" * 50)
    print("1. 🌐 Run UI Automation (1 activity demo)")
    print("2. 🌐 Run UI Automation (3 activities)")
    print("3. 🎭 Manual → Record → Playwright Workflow")
    print("4. 📊 System Status Check")
    print("5. 🤖 Test LLM Integration")
    print("6. 📋 Show System Info")
    print("0. ❌ Exit")

def show_system_info():
    """Show system information"""
    print("\n📋 SYSTEM INFORMATION")
    print("=" * 40)
    print("🎯 Your system is ready for:")
    print("  • ICICI → Gainsight migration (37 activities ready)")
    print("  • Manual workflow recording")
    print("  • Playwright script generation")
    print("  • Multi-LLM integration (free models)")
    
    print("\n📁 Key files created:")
    print("  • gainsight_ui_automator.py - Complete UI automation")
    print("  • simple_record_workflow.py - Recording workflow")
    print("  • enhanced_llm_client.py - Multi-LLM client")
    print("  • data/icici_gainsight_ready.json - 37 activities ready")
    
    print("\n🚀 Next steps:")
    print("  1. Start with option 1 (demo with 1 activity)")
    print("  2. Scale up to option 2 (3 activities)")
    print("  3. Try recording workflow (option 3)")

def main():
    """Main launcher"""
    print("🚀 SIMPLE BROWSER AUTOMATION LAUNCHER")
    print("🎯 No async issues, just works!")
    print("=" * 60)
    
    # Quick system check
    check_system()
    
    while True:
        try:
            show_menu()
            choice = input("\n🎯 Choose option (0-6): ").strip()
            
            if choice == "0":
                print("\n👋 Goodbye! Your automation system is ready anytime.")
                break
            
            elif choice == "1":
                run_ui_automation(1)
            
            elif choice == "2":
                run_ui_automation(3)
            
            elif choice == "3":
                print("\n🎭 Starting recording workflow...")
                subprocess.run([sys.executable, "simple_record_workflow.py"],
                             cwd="/Users/<USER>/Desktop/wildweasel/Browser")
            
            elif choice == "4":
                run_status_check()
            
            elif choice == "5":
                run_llm_test()
            
            elif choice == "6":
                show_system_info()
            
            else:
                print("❌ Invalid choice. Please choose 0-6.")
            
            if choice != "0":
                input("\n⏸️  Press Enter to continue...")
        
        except KeyboardInterrupt:
            print("\n👋 Goodbye!")
            break
        except Exception as e:
            print(f"❌ Error: {e}")

if __name__ == "__main__":
    main()

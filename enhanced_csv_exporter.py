#!/usr/bin/env python3
"""
Enhanced CSV Exporter for Gainsight Import
==========================================

Features:
1. Sequential numbering instead of Activity ID
2. Real touchpoint reason names mapping
3. Clean flow type names (remove timestamps)
4. User selection: Demo vs Real data
5. Real internal/external attendees from ICICI.json
6. No assumptions - only use actual data

Author: Assistant
Date: January 29, 2025
"""

import json
import pandas as pd
import re
from pathlib import Path
from datetime import datetime
from collections import Counter

class EnhancedCSVExporter:
    def __init__(self):
        """Initialize the enhanced CSV exporter."""

        # Load mapping files
        self.touchpoint_mapping = {}
        self.flow_type_mapping = {}
        self.load_mapping_files()

        self.stats = {
            'total_activities': 0,
            'activities_with_attendees': 0,
            'activities_with_touchpoint_reason': 0,
            'activities_with_flow_type': 0
        }

    def load_mapping_files(self):
        """Load touchpoint and flow type mapping files."""
        try:
            # Load touchpoint reason mappings
            touchpoint_file = "/Users/<USER>/Desktop/totango/Touchpoint_reason.JSON"
            if Path(touchpoint_file).exists():
                with open(touchpoint_file, 'r') as f:
                    touchpoint_data = json.load(f)
                self.touchpoint_mapping = {item['id']: item['display_name'] for item in touchpoint_data}
                print(f"✅ Loaded {len(self.touchpoint_mapping)} touchpoint reason mappings")

            # Load flow type mappings
            flow_type_file = "/Users/<USER>/Desktop/totango/flowtype.json"
            if Path(flow_type_file).exists():
                with open(flow_type_file, 'r') as f:
                    flow_type_data = json.load(f)
                # Create mapping and clean names - use activity_type_id field
                for item in flow_type_data:
                    flow_id = item.get('activity_type_id', item.get('id', ''))  # Fix: use activity_type_id
                    display_name = item.get('display_name', item.get('name', ''))
                    if flow_id and display_name:
                        clean_name = self.clean_flow_type_name(display_name)
                        self.flow_type_mapping[flow_id] = clean_name
                print(f"✅ Loaded {len(self.flow_type_mapping)} flow type mappings")

        except Exception as e:
            print(f"⚠️  Warning: Could not load mapping files: {e}")

    def clean_flow_type_name(self, flow_type_name: str) -> str:
        """Clean flow type name by removing timestamps and numbers."""
        if not flow_type_name:
            return ""

        # Remove timestamp patterns like _1560979263618
        cleaned = re.sub(r'_\d{10,}', '', flow_type_name)

        # Remove trailing numbers like _1, _2, etc.
        cleaned = re.sub(r'_\d+$', '', cleaned)

        # Replace underscores with spaces and title case
        cleaned = cleaned.replace('_', ' ').title()

        return cleaned

    def get_user_preference(self) -> str:
        """Get user preference for demo vs real data."""
        print("\n❓ USER DATA SELECTION")
        print("=" * 30)
        print("1. Demo Data - Use Ram Prasad as author/attendee (for testing)")
        print("2. Real Data - Use actual ICICI attendees from JSON (for migration)")

        while True:
            try:
                choice = input("\nSelect option (1 or 2): ").strip()
                if choice in ['1', '2']:
                    return 'demo' if choice == '1' else 'real'
                else:
                    print("Please enter 1 or 2")
            except KeyboardInterrupt:
                print("\nOperation cancelled")
                return 'demo'

    def extract_attendees_from_activity(self, activity: dict) -> tuple:
        """Extract real internal and external attendees from activity."""
        properties = activity.get('properties', {})

        # Look for attendee information in various fields
        internal_attendees = []
        external_attendees = []

        # Check for direct attendee fields
        if 'internal_attendees' in properties:
            internal_attendees = properties['internal_attendees']
        elif 'attendees' in properties:
            # Sometimes attendees are in a general field
            attendees = properties['attendees']
            if isinstance(attendees, list):
                internal_attendees = attendees

        if 'external_attendees' in properties:
            external_attendees = properties['external_attendees']

        # Check for user information that might indicate attendees
        if 'user_name' in properties and 'user_email' in properties:
            user_info = {
                'name': properties['user_name'],
                'email': properties['user_email']
            }
            if user_info not in internal_attendees:
                internal_attendees.append(user_info)

        return internal_attendees, external_attendees

    def format_attendees(self, attendees: list) -> str:
        """Format attendees list for CSV."""
        if not attendees:
            return ""

        formatted = []
        for attendee in attendees:
            if isinstance(attendee, dict):
                name = attendee.get('name', '')
                email = attendee.get('email', '')
                if name and email:
                    formatted.append(f"{name} <{email}>")
                elif name:
                    formatted.append(name)
                elif email:
                    formatted.append(email)
            elif isinstance(attendee, str):
                formatted.append(attendee)

        return "; ".join(formatted)

    def get_touchpoint_reason_name(self, activity: dict) -> str:
        """Get the actual touchpoint reason name from mapping."""
        properties = activity.get('properties', {})

        # Check for touchpoint_tags_names (from your converter)
        if 'touchpoint_tags_names' in properties:
            touchpoint_names = properties['touchpoint_tags_names']
            if isinstance(touchpoint_names, list) and touchpoint_names:
                return "; ".join(touchpoint_names)
            elif isinstance(touchpoint_names, str):
                return touchpoint_names

        # Check for touchpoint_tags_ids and map them
        if 'touchpoint_tags_ids' in properties:
            touchpoint_ids = properties['touchpoint_tags_ids']
            if isinstance(touchpoint_ids, list):
                mapped_names = []
                for tag_id in touchpoint_ids:
                    if tag_id in self.touchpoint_mapping:
                        mapped_names.append(self.touchpoint_mapping[tag_id])
                if mapped_names:
                    return "; ".join(mapped_names)

        # Check for direct touchpoint_tags field
        if 'touchpoint_tags' in properties:
            touchpoint_tags = properties['touchpoint_tags']
            if isinstance(touchpoint_tags, list):
                mapped_names = []
                for tag_id in touchpoint_tags:
                    if tag_id in self.touchpoint_mapping:
                        mapped_names.append(self.touchpoint_mapping[tag_id])
                if mapped_names:
                    return "; ".join(mapped_names)
            elif isinstance(touchpoint_tags, str) and touchpoint_tags in self.touchpoint_mapping:
                return self.touchpoint_mapping[touchpoint_tags]

        # Default fallback
        return "Internal Note"

    def get_flow_type_name(self, activity: dict) -> str:
        """Get the cleaned flow type name."""
        properties = activity.get('properties', {})

        # Check for activity_type_id field (this is the correct field from ICICI data)
        if 'activity_type_id' in properties:
            flow_type_id = properties['activity_type_id']
            if flow_type_id in self.flow_type_mapping:
                return self.flow_type_mapping[flow_type_id]

        # Check for other possible flow type fields
        for field_name in ['flow_type', 'flow_type_id', 'type_id']:
            if field_name in properties:
                flow_type_id = properties[field_name]
                if flow_type_id in self.flow_type_mapping:
                    return self.flow_type_mapping[flow_type_id]

        # Check for flow_type_name field
        if 'flow_type_name' in properties:
            return self.clean_flow_type_name(properties['flow_type_name'])

        # Default
        return "Standard"

    def generate_subject(self, activity: dict) -> str:
        """Generate subject for activity."""
        properties = activity.get('properties', {})
        activity_type = activity.get('type', 'Activity')

        # Try various fields for meaningful names
        for field in ['name', 'display_name', 'title', 'subject']:
            if field in properties and properties[field]:
                return f"ICICI: {properties[field]}"

        # Generate based on activity type
        if activity_type == 'automated_attribute_change':
            display_name = properties.get('display_name', 'Attribute')
            return f"ICICI: {display_name} Updated"
        elif activity_type == 'campaign_touch':
            return f"ICICI: Campaign Activity"
        elif activity_type == 'note':
            return f"ICICI: Note"
        else:
            return f"ICICI: {activity_type.replace('_', ' ').title()}"

    def generate_content(self, activity: dict) -> str:
        """Generate HTML content for activity."""
        properties = activity.get('properties', {})
        activity_type = activity.get('type', 'activity')

        # Check for existing content
        if 'description' in properties and properties['description']:
            return f"<p>{properties['description']}</p>"

        if 'content' in properties and properties['content']:
            return f"<p>{properties['content']}</p>"

        # Generate based on activity type
        if activity_type == 'automated_attribute_change':
            display_name = properties.get('display_name', 'attribute')
            new_value = properties.get('new_value', 'updated')
            prev_value = properties.get('prev_value', 'previous value')
            return f"<p>Automated update: {display_name} changed from '{prev_value}' to '{new_value}'</p>"
        elif activity_type == 'campaign_touch':
            name = properties.get('name', 'campaign')
            description = properties.get('description', f"Campaign '{name}' was executed")
            return f"<p>{description}</p>"
        elif activity_type == 'webhook':
            name = properties.get('name', 'webhook')
            status = properties.get('status', 'executed')
            return f"<p>Webhook '{name}' was {status}</p>"
        elif activity_type == 'note':
            return f"<p>Note activity for ICICI Bank</p>"
        else:
            return f"<p>ICICI Bank activity: {activity_type.replace('_', ' ')}</p>"

    def format_timestamp(self, timestamp: int) -> str:
        """Format timestamp for CSV."""
        try:
            if timestamp > 10**10:  # milliseconds
                timestamp = timestamp / 1000
            dt = datetime.fromtimestamp(timestamp)
            return dt.strftime('%Y-%m-%d %H:%M:%S')
        except:
            return ""

    def strip_html(self, html: str) -> str:
        """Strip HTML tags."""
        if not html:
            return ""
        return re.sub(r'<[^>]+>', '', html)

    def create_enhanced_csv(self, json_file: str, output_file: str = None, user_preference: str = 'demo') -> str:
        """Create enhanced CSV export."""
        print(f"🔄 Creating enhanced CSV from: {json_file}")
        print(f"📊 User preference: {user_preference.upper()} data")

        # Load JSON data
        with open(json_file, 'r') as f:
            activities = json.load(f)

        self.stats['total_activities'] = len(activities)
        print(f"📊 Processing {len(activities)} activities")

        csv_rows = []

        for i, activity in enumerate(activities, 1):  # Start from 1
            properties = activity.get('properties', {})

            # Get activity data
            timestamp = activity.get('timestamp', 0)
            activity_type = activity.get('type', 'unknown')
            gainsight_activity_type = properties.get('gainsight_activity_type', 'Update')

            # Get attendees
            internal_attendees, external_attendees = self.extract_attendees_from_activity(activity)

            # Format attendees based on user preference
            if user_preference == 'demo':
                # Use Ram Prasad for demo
                internal_attendees_str = "Ram Prasad <<EMAIL>>"
                external_attendees_str = ""
                author_name = "Ram Prasad"
                author_email = "<EMAIL>"
            else:
                # Use real data from ICICI
                internal_attendees_str = self.format_attendees(internal_attendees)
                external_attendees_str = self.format_attendees(external_attendees)

                # Try to get author from attendees or properties
                if internal_attendees:
                    first_attendee = internal_attendees[0]
                    if isinstance(first_attendee, dict):
                        author_name = first_attendee.get('name', 'ICICI User')
                        author_email = first_attendee.get('email', '')
                    else:
                        author_name = str(first_attendee)
                        author_email = ""
                else:
                    # Check properties for user info
                    author_name = properties.get('user_name', properties.get('entity_name', 'ICICI User'))
                    author_email = properties.get('user_email', '')

                # Track statistics
                if internal_attendees or external_attendees:
                    self.stats['activities_with_attendees'] += 1

            # Get touchpoint reason and flow type
            touchpoint_reason = self.get_touchpoint_reason_name(activity)
            flow_type = self.get_flow_type_name(activity)

            if touchpoint_reason != "Internal Note":
                self.stats['activities_with_touchpoint_reason'] += 1
            if flow_type != "Standard":
                self.stats['activities_with_flow_type'] += 1

            # Generate content
            subject = self.generate_subject(activity)
            content_html = self.generate_content(activity)
            content_plain = self.strip_html(content_html)
            activity_date = self.format_timestamp(timestamp)

            csv_row = {
                "Row Number": i,  # Sequential numbering
                "Subject": subject,
                "Activity Date": activity_date,
                "Activity Type": gainsight_activity_type,
                "Content (HTML)": content_html,
                "Plain Text": content_plain,
                "Author Name": author_name,
                "Author Email": author_email,
                "Flow Type": flow_type,
                "Touchpoint Reason": touchpoint_reason,
                "Internal Attendees": internal_attendees_str,
                "External Attendees": external_attendees_str,
                "Company": "ICICI",
                "Original Activity Type": activity_type,
                "Original Meeting Type": properties.get('original_meeting_type_name', 'Unknown'),
                "Source": "ICICI_MIGRATION"
            }

            csv_rows.append(csv_row)

        # Set output file - save to totango directory
        if output_file is None:
            base_name = Path(json_file).stem
            suffix = "_demo" if user_preference == 'demo' else "_real"
            # Save to totango directory
            totango_dir = "/Users/<USER>/Desktop/totango"
            output_file = f"{totango_dir}/{base_name}_enhanced{suffix}.csv"

        # Create DataFrame and save
        df = pd.DataFrame(csv_rows)
        df.to_csv(output_file, index=False, encoding='utf-8')

        print(f"✅ Enhanced CSV created: {output_file}")
        print(f"📊 Rows: {len(csv_rows)}")

        return output_file

    def generate_enhancement_report(self) -> str:
        """Generate report on enhancements."""
        report = []
        report.append("=" * 60)
        report.append("ENHANCED CSV EXPORT REPORT")
        report.append("=" * 60)
        report.append(f"📊 Total Activities: {self.stats['total_activities']}")
        report.append(f"👥 Activities with Attendees: {self.stats['activities_with_attendees']}")
        report.append(f"🏷️  Activities with Touchpoint Reason: {self.stats['activities_with_touchpoint_reason']}")
        report.append(f"🔄 Activities with Flow Type: {self.stats['activities_with_flow_type']}")
        report.append("")

        report.append("✅ ENHANCEMENTS APPLIED:")
        report.append("   • Sequential row numbering (1, 2, 3...)")
        report.append("   • Real touchpoint reason names mapped")
        report.append("   • Cleaned flow type names (removed timestamps)")
        report.append("   • Real attendee data from ICICI.json")
        report.append("   • No assumptions - only actual data used")

        return "\n".join(report)

def main():
    """Main function."""
    print("🚀 ENHANCED CSV EXPORTER FOR GAINSIGHT")
    print("=" * 50)

    # Find JSON file
    json_file = "/Users/<USER>/Desktop/totango/ICICI_processed_gainsight_mapped.json"

    if not Path(json_file).exists():
        print(f"❌ JSON file not found: {json_file}")
        return

    # Create exporter
    exporter = EnhancedCSVExporter()

    # Get user preference
    user_preference = exporter.get_user_preference()

    # Create enhanced CSV
    output_file = exporter.create_enhanced_csv(json_file, user_preference=user_preference)

    # Generate report
    report = exporter.generate_enhancement_report()
    print(f"\n{report}")

    print(f"\n🎉 Enhanced CSV export completed!")
    print(f"📄 Output file: {output_file}")
    print(f"🚀 Ready for Gainsight Timeline import!")

if __name__ == "__main__":
    main()

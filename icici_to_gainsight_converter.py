#!/usr/bin/env python3
"""
ICICI to Gainsight Data Converter
Converts ICICI activity data to Gainsight format using Gemini AI for intelligent mapping
"""

import json
import asyncio
from datetime import datetime
from typing import Dict, List, Any, Optional
from pathlib import Path

from gemini_client import GeminiC<PERSON>
from config import config

class ICICIToGainsightConverter:
    """
    Converts ICICI activity data to Gainsight format
    """
    
    def __init__(self):
        self.activity_type_mapping = {
            "onboarding_101": "c81eeccf-2aa1-45bc-be71-5377f148e1e9",
            "adoption": "68c0d13a-40f1-47e4-9bb4-3d1fb6a16515", 
            "intelligence_1561140678082": "93b4649c-8459-4f56-be3f-be75f7506ee0"
        }
        
        self.touchpoint_reason_mapping = {
            "default": "1I00J1INQCQ6GQ3T2MWULITYE9ASDFPQ2KD3"
        }
        
        self.flow_type_mapping = {
            "default": "1I00EBMOY66ROGCBY63I51PAXG5OJRPK37AQ"
        }
        
        # Load mapping files if they exist
        self.load_mapping_files()
    
    def load_mapping_files(self):
        """Load mapping files from data/totango directory"""
        try:
            # Load ID mappings
            id_file = Path("data/totango/ID.json")
            if id_file.exists():
                with open(id_file, 'r') as f:
                    id_data = json.load(f)
                    # Update activity type mapping based on ID file
                    for item in id_data:
                        if item.get("display_name") == "Email":
                            self.activity_type_mapping["email"] = item["id"]
                        elif item.get("display_name") == "Web meeting":
                            self.activity_type_mapping["meeting"] = item["id"]
            
            # Load flow type mappings
            flow_file = Path("data/totango/flowtype.json")
            if flow_file.exists():
                with open(flow_file, 'r') as f:
                    flow_data = json.load(f)
                    # Update flow type mapping
                    if isinstance(flow_data, list) and len(flow_data) > 0:
                        self.flow_type_mapping["default"] = flow_data[0].get("id", self.flow_type_mapping["default"])
            
            print("✅ Mapping files loaded successfully")
            
        except Exception as e:
            print(f"⚠️  Warning: Could not load mapping files: {e}")
    
    async def convert_activity(self, icici_activity: Dict[str, Any]) -> Dict[str, Any]:
        """
        Convert a single ICICI activity to Gainsight format
        """
        try:
            # Extract basic information
            activity_id = icici_activity.get("id", "")
            timestamp = icici_activity.get("timestamp", 0)
            activity_type = icici_activity.get("type", "")
            properties = icici_activity.get("properties", {})
            
            # Convert timestamp to Gainsight format
            activity_date = timestamp if timestamp else int(datetime.now().timestamp() * 1000)
            
            # Determine activity type ID
            activity_type_id = self.get_activity_type_id(activity_type, properties)
            
            # Generate subject and content using AI
            subject, content = await self.generate_activity_content(icici_activity)
            
            # Create Gainsight activity structure
            gainsight_activity = {
                "lastModifiedByUser": {
                    "gsId": "1P01E316G9DAPFOLE6SOOUG71XRMN5F3PLER",
                    "name": "Ram Prasad",
                    "eid": None,
                    "esys": None,
                    "pp": ""
                },
                "note": {
                    "customFields": {
                        "internalAttendees": [
                            {
                                "id": "1P01E316G9DAPFOLE6SOOUG71XRMN5F3PLER",
                                "obj": "User",
                                "name": "Ram Prasad",
                                "email": "<EMAIL>",
                                "eid": None,
                                "eobj": "User",
                                "epp": None,
                                "esys": "SALESFORCE",
                                "sys": "GAINSIGHT",
                                "pp": ""
                            }
                        ],
                        "externalAttendees": [],
                        "ant__Status1552512571338": None,
                        "Ant__Touchpoint_Reason__c": self.touchpoint_reason_mapping["default"],
                        "Ant__Flow_Type__c": self.flow_type_mapping["default"]
                    },
                    "type": self.get_note_type(activity_type),
                    "subject": subject,
                    "activityDate": activity_date,
                    "content": content,
                    "plainText": self.strip_html(content),
                    "trackers": None
                },
                "mentions": [],
                "relatedRecords": {},
                "meta": {
                    "activityTypeId": activity_type_id,
                    "ctaId": None,
                    "source": "ICICI_MIGRATION",
                    "hasTask": False,
                    "emailSent": False,
                    "systemType": "GAINSIGHT",
                    "notesTemplateId": None
                },
                "author": {
                    "id": "1P01E316G9DAPFOLE6SOOUG71XRMN5F3PLER",
                    "obj": "User",
                    "name": "Ram Prasad",
                    "email": "<EMAIL>",
                    "eid": None,
                    "eobj": "User",
                    "epp": None,
                    "esys": "SALESFORCE",
                    "sys": "GAINSIGHT",
                    "pp": ""
                },
                "syncedToSFDC": False,
                "id": f"ICICI_{activity_id}",
                "tasks": [],
                "attachments": [],
                "contexts": [
                    {
                        "id": "1P02IPMAEL4M3CQGY4K5FHU22D2HHT11KCKU",
                        "obj": "Company",
                        "eobj": "Account",
                        "eid": None,
                        "esys": "SALESFORCE",
                        "lbl": "ICICI",
                        "dsp": True,
                        "base": True
                    }
                ]
            }
            
            return gainsight_activity
            
        except Exception as e:
            print(f"❌ Error converting activity {icici_activity.get('id', 'unknown')}: {e}")
            return None
    
    def get_activity_type_id(self, activity_type: str, properties: Dict[str, Any]) -> str:
        """Get the appropriate Gainsight activity type ID"""
        activity_type_id = properties.get("activity_type_id", "")
        
        if activity_type_id in self.activity_type_mapping:
            return self.activity_type_mapping[activity_type_id]
        
        # Default mapping based on activity type
        if activity_type == "webhook":
            return self.activity_type_mapping.get("email", self.activity_type_mapping["adoption"])
        elif activity_type == "campaign_touch":
            return self.activity_type_mapping.get("email", self.activity_type_mapping["adoption"])
        else:
            return self.activity_type_mapping["adoption"]
    
    def get_note_type(self, activity_type: str) -> str:
        """Get the appropriate note type for Gainsight"""
        if activity_type in ["webhook", "campaign_touch"]:
            return "EMAIL"
        elif activity_type == "account_alert":
            return "INTERNAL_NOTE"
        else:
            return "INTERNAL_NOTE"
    
    async def generate_activity_content(self, icici_activity: Dict[str, Any]) -> tuple[str, str]:
        """
        Use Gemini AI to generate meaningful subject and content for the activity
        """
        try:
            async with GeminiClient() as client:
                prompt = f"""
                Convert this ICICI activity data into a meaningful Gainsight activity:
                
                Activity Type: {icici_activity.get('type', '')}
                Properties: {json.dumps(icici_activity.get('properties', {}), indent=2)}
                
                Generate:
                1. A clear, professional subject line (max 100 characters)
                2. A detailed content description (2-3 sentences explaining what happened)
                
                Format your response as:
                SUBJECT: [subject line]
                CONTENT: [content description]
                """
                
                response = await client.complete(
                    prompt,
                    task_type="general",
                    max_tokens=300
                )
                
                if response.success:
                    lines = response.content.strip().split('\n')
                    subject = "ICICI Activity"
                    content = "Activity migrated from ICICI system"
                    
                    for line in lines:
                        if line.startswith("SUBJECT:"):
                            subject = line.replace("SUBJECT:", "").strip()
                        elif line.startswith("CONTENT:"):
                            content = line.replace("CONTENT:", "").strip()
                    
                    return subject, f"<p>{content}</p>"
                else:
                    # Fallback if AI fails
                    return self.generate_fallback_content(icici_activity)
                    
        except Exception as e:
            print(f"⚠️  AI generation failed: {e}, using fallback")
            return self.generate_fallback_content(icici_activity)
    
    def generate_fallback_content(self, icici_activity: Dict[str, Any]) -> tuple[str, str]:
        """Generate fallback content when AI is not available"""
        activity_type = icici_activity.get('type', 'Unknown')
        properties = icici_activity.get('properties', {})
        
        subject = f"ICICI {activity_type.replace('_', ' ').title()}"
        
        if activity_type == "automated_attribute_change":
            display_name = properties.get('display_name', 'Unknown')
            subject = f"Updated {display_name}"
            content = f"<p>Automated update to {display_name} for ICICI Bank account.</p>"
        elif activity_type == "campaign_touch":
            campaign_name = properties.get('name', 'Campaign')
            subject = f"Campaign: {campaign_name}"
            content = f"<p>Campaign '{campaign_name}' was executed for ICICI Bank.</p>"
        elif activity_type == "webhook":
            webhook_name = properties.get('name', 'Webhook')
            subject = f"Webhook: {webhook_name}"
            content = f"<p>Webhook '{webhook_name}' was triggered for ICICI Bank.</p>"
        else:
            content = f"<p>Activity of type '{activity_type}' was recorded for ICICI Bank.</p>"
        
        return subject, content
    
    def strip_html(self, html_content: str) -> str:
        """Strip HTML tags to create plain text"""
        import re
        return re.sub(r'<[^>]+>', '', html_content)
    
    async def convert_file(self, input_file: str, output_file: str) -> bool:
        """
        Convert an entire ICICI file to Gainsight format
        """
        try:
            print(f"🔄 Converting {input_file} to {output_file}")
            
            # Load ICICI data
            with open(input_file, 'r') as f:
                icici_data = json.load(f)
            
            if not isinstance(icici_data, list):
                print("❌ Input file should contain a list of activities")
                return False
            
            print(f"📊 Found {len(icici_data)} activities to convert")
            
            # Convert each activity
            converted_activities = []
            for i, activity in enumerate(icici_data):
                print(f"🔄 Converting activity {i+1}/{len(icici_data)}")
                
                converted = await self.convert_activity(activity)
                if converted:
                    converted_activities.append(converted)
            
            # Save converted data
            with open(output_file, 'w') as f:
                json.dump(converted_activities, f, indent=2)
            
            print(f"✅ Successfully converted {len(converted_activities)} activities")
            print(f"💾 Saved to {output_file}")
            
            return True
            
        except Exception as e:
            print(f"❌ Error converting file: {e}")
            return False

async def main():
    """Main conversion function"""
    converter = ICICIToGainsightConverter()
    
    input_file = "data/totango/ICICI.json"
    output_file = "data/icici_gainsight_converted.json"
    
    if not Path(input_file).exists():
        print(f"❌ Input file not found: {input_file}")
        return
    
    success = await converter.convert_file(input_file, output_file)
    
    if success:
        print("🎉 Conversion completed successfully!")
    else:
        print("❌ Conversion failed")

if __name__ == "__main__":
    asyncio.run(main())

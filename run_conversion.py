#!/usr/bin/env python3
"""
Run ICICI to Gainsight conversion
"""

import asyncio
import sys
from pathlib import Path

from icici_to_gainsight_converter import ICICIToGainsightConverter

async def main():
    """Run the conversion"""
    print("🚀 ICICI to Gainsight Data Converter")
    print("=" * 50)
    
    # Check if input file exists
    input_file = "data/totango/ICICI.json"
    if not Path(input_file).exists():
        print(f"❌ Input file not found: {input_file}")
        print("Please ensure the ICICI.json file is in the data/totango/ directory")
        return 1
    
    # Create converter
    converter = ICICIToGainsightConverter()
    
    # Set output file
    output_file = "data/icici_gainsight_converted.json"
    
    print(f"📁 Input: {input_file}")
    print(f"📁 Output: {output_file}")
    print()
    
    # Run conversion
    success = await converter.convert_file(input_file, output_file)
    
    if success:
        print("\n🎉 Conversion completed successfully!")
        print(f"✅ Converted data saved to: {output_file}")
        
        # Show sample of converted data
        try:
            import json
            with open(output_file, 'r') as f:
                converted_data = json.load(f)
            
            print(f"\n📊 Summary:")
            print(f"   • Total activities converted: {len(converted_data)}")
            
            if len(converted_data) > 0:
                sample = converted_data[0]
                print(f"   • Sample subject: {sample['note']['subject']}")
                print(f"   • Sample type: {sample['note']['type']}")
        
        except Exception as e:
            print(f"⚠️  Could not read converted file: {e}")
        
        return 0
    else:
        print("\n❌ Conversion failed")
        return 1

if __name__ == "__main__":
    try:
        exit_code = asyncio.run(main())
        sys.exit(exit_code)
    except KeyboardInterrupt:
        print("\n👋 Conversion interrupted")
        sys.exit(1)
    except Exception as e:
        print(f"❌ Fatal error: {e}")
        sys.exit(1)

# 🚀 Simple Browser Automation Agent

A clean, working browser automation system powered by Google Gemini API with intelligent task processing and error recovery.

## ✨ Features

### 🤖 **Google Gemini Integration**
- **Primary LLM**: Google Gemini 1.5 Flash for fast, intelligent responses
- **Fallback Support**: Multiple model fallbacks for reliability
- **Task-Aware Selection**: Automatically chooses the best model for each task type
- **Error Recovery**: Intelligent fallback when API calls fail

### 🧠 **Intelligent Task Processing**
- **Multi-Task Types**: Coding, reasoning, general, and browser automation tasks
- **Context Awareness**: Maintains conversation context and learns from interactions
- **Performance Optimization**: Fast response times with efficient API usage
- **Simple Interface**: Clean command-line interface for easy interaction

### 🔄 **ICICI to Gainsight Data Migration**
- **Intelligent Conversion**: Uses Gemini AI to convert ICICI activities to Gainsight format
- **Smart Mapping**: Automatically maps activity types and generates meaningful content
- **Batch Processing**: Converts entire datasets efficiently
- **Error Recovery**: Handles conversion errors gracefully with fallback content

## 🚀 Quick Start

### 1. **Installation**

```bash
# Clone or download the project
cd Browser

# Install dependencies
pip install -r requirements.txt
```

### 2. **Configuration**

Your Gemini API key is already configured in the code:
- API Key: `AIzaSyCtFLDVZGEariTD5Ui5ixTQ_C_EeH_OCJ8`

### 3. **Run the Agent**

```bash
# Test the setup
python test_setup.py

# Interactive mode
python simple_main.py

# Single task execution
python simple_main.py -t "Write a Python function to calculate fibonacci numbers"

# Test API connection
python simple_main.py --test

# Run ICICI to Gainsight conversion
python run_conversion.py
```

## 💡 Usage Examples

### Interactive Mode

```bash
python simple_main.py
```

Example session:
```
Agent> Write a Python function to calculate fibonacci numbers
Agent> Explain how browser automation works
Agent> test  # Test API connection
Agent> quit
```

### Single Task Mode

```bash
# Execute a specific task
python simple_main.py -t "Create a plan for web scraping a website"

# Test API connection
python simple_main.py --test

# Show system status
python simple_main.py --status
```

### ICICI to Gainsight Conversion

```bash
# See how the conversion works step by step
python show_conversion_process.py

# Test the conversion with sample data
python test_conversion.py

# Run full ICICI to Gainsight conversion
python run_conversion.py

# Interactive demo (with pauses)
python conversion_demo.py
```

## 💡 Usage Examples

### 🎭 Playwright Integration Examples
```bash
# Run demo showing Playwright optimization
python playwright_integration_demo.py

# See practical examples
python playwright_examples.py

# Execute task with automatic optimization (default)
python main.py -t "Fill form on example.com" --optimize

# Force LLM execution (for pattern recording)
python main.py -t "New complex task" --no-optimize

# Generate Playwright script from session
python main.py --generate-script recordings/session_abc123.json

# View Playwright optimization statistics
python main.py --playwright-stats
```

### Interactive Mode
```bash
python main.py
```
```
Agent> Go to amazon.com and find the price of iPhone 15
Agent> Login to my email and check for unread messages
Agent> Fill out the contact form on example.com with my information
Agent> status  # Check system status
Agent> replay data/recordings/task_abc123.json  # Replay a task
Agent> quit
```

### Single Task Mode
```bash
# Execute a specific task
python main.py -t "Navigate to GitHub and create a new repository"

# With custom context
python main.py -t "Book a flight" --context '{"departure": "NYC", "destination": "LAX"}'

# Headless mode
python main.py -t "Check my bank balance" --headless

# Export results
python main.py -t "Research competitors" --export results.json
```

### Replay Mode
```bash
# Replay with adaptation to page changes
python main.py -r recordings/login_sequence.json

# Replay without adaptation (exact replay)
python main.py -r recordings/form_fill.json --no-adapt
```

## 🎭 Playwright Integration Benefits

### 💰 **Token Efficiency**
The Playwright integration significantly reduces LLM token usage by converting successful browser automation patterns into reusable scripts:

- **First Execution**: Uses LLM (e.g., 1,500 tokens)
- **Subsequent Executions**: Uses generated Playwright script (0 tokens)
- **Average Savings**: 60-80% reduction in token costs for repeated tasks

### 🚀 **Performance Improvements**
- **Faster Execution**: Playwright scripts execute 2-5x faster than LLM-guided actions
- **Higher Reliability**: Generated scripts are more consistent and less prone to rate limiting
- **Reduced Latency**: No LLM API calls needed for optimized tasks

### 🧠 **Intelligent Decision Making**
The system automatically decides when to use LLM vs Playwright based on:
- **Pattern Confidence**: How well the task matches existing patterns
- **Token Savings Potential**: Estimated efficiency gains
- **Task Complexity**: Simple vs complex task requirements
- **Success History**: Performance of similar tasks

### 🔄 **How It Works**
1. **Pattern Recording**: LLM executes new tasks while recording actions
2. **Script Generation**: Successful sessions are converted to Playwright scripts
3. **Pattern Optimization**: Multiple sessions are analyzed for common patterns
4. **Intelligent Execution**: Future similar tasks use optimized scripts
5. **Continuous Learning**: System improves patterns over time

## 🏗️ Architecture

```
┌─────────────────────────────────────────────────────────────┐
│                    Master Orchestrator                      │
├─────────────────────────────────────────────────────────────┤
│  ┌─────────────────┐  ┌─────────────────┐  ┌─────────────── │
│  │  Memory System  │  │ Browser Agent   │  │ Learning Engine│
│  │   (Letta)       │  │ (browser-use)   │  │               │
│  └─────────────────┘  └─────────────────┘  └─────────────── │
│                                                             │
│  ┌─────────────────────────────────────────────────────────┐ │
│  │           🎭 Playwright Integration Manager            │ │
│  ├─────────────────────────────────────────────────────────┤ │
│  │ Script Generator │ Action Converter │ Pattern Optimizer │ │
│  │ Script Executor  │ Decision Engine  │ Performance Stats │ │
│  └─────────────────────────────────────────────────────────┘ │
└─────────────────────────────────────────────────────────────┘
```

### Core Components

#### 🎭 **Playwright Integration Engine**
- **Script Generation**: Converts successful LLM actions to Playwright scripts
- **Pattern Recognition**: Identifies reusable automation patterns
- **Decision Engine**: Chooses optimal execution method (LLM vs Playwright)
- **Performance Optimization**: Maximizes speed and minimizes token usage

#### 🧠 **Memory System (Letta Integration)**
- **Core Memory**: In-context memory for immediate tasks
- **Archival Memory**: Long-term storage with semantic search
- **Working Memory**: Session-based temporary storage
- **Knowledge Graph**: Entity relationships and patterns

#### 🌐 **Browser Automation Engine**
- **Action Execution**: LLM-guided browser interactions
- **Vision Processing**: Screenshot analysis and element detection
- **Error Handling**: Intelligent recovery and adaptation
- **Session Management**: Persistent browser sessions

#### 📊 **Learning & Adaptation**
- **Pattern Recognition**: Identifies successful task patterns
- **Strategy Optimization**: Learns and refines execution strategies
- **Error Prevention**: Builds knowledge of common failures
- **User Preference Learning**: Adapts to individual user patterns

## 🔧 Configuration

### Environment Variables
```bash
# LLM Configuration
OPENAI_API_KEY=sk-...                    # OpenAI API key
ANTHROPIC_API_KEY=sk-ant-...             # Anthropic API key

# Memory System
LETTA_SERVER_URL=http://localhost:8283   # Letta server endpoint
POSTGRES_URL=postgresql://...            # Database for persistence

# Browser Settings
BROWSER_HEADLESS=false                   # Run browser in headless mode
BROWSER_TIMEOUT=30000                    # Browser timeout in ms

# Playwright Integration
PLAYWRIGHT_OPTIMIZATION_ENABLED=true       # Enable Playwright optimization
PLAYWRIGHT_MIN_CONFIDENCE=0.7              # Minimum confidence for using Playwright
PLAYWRIGHT_MIN_TOKENS_SAVED=200            # Minimum tokens to justify Playwright usage
PLAYWRIGHT_SCRIPTS_DIR=data/playwright_scripts  # Directory for generated scripts

# Learning & Memory
MEMORY_CONSOLIDATION_INTERVAL=3600       # Memory consolidation interval
ENABLE_LEARNING=true                     # Enable learning from tasks
ENABLE_RECORDING=true                    # Enable action recording
```

### Advanced Configuration
See `config.py` for detailed configuration options including:
- LLM model selection and parameters
- Memory system settings
- Browser automation preferences
- Security and privacy controls
- Logging and monitoring options

## 📊 System Status & Monitoring

### Get System Status
```bash
python main.py --status
```

### Export System Data
```bash
python main.py --export system_backup.json
```

### View Logs
```bash
tail -f logs/agent.log
```

## 🔄 Memory & Learning

### How Memory Works
1. **Task Context Retrieval**: Before executing a task, the system retrieves relevant memory
2. **Execution Recording**: Every action is recorded with context and results
3. **Pattern Learning**: Successful strategies are identified and stored
4. **Error Analysis**: Failures are analyzed and prevention strategies developed
5. **Memory Consolidation**: Periodic consolidation organizes and optimizes memory

### Memory Types
- **User Profile**: Preferences, interaction patterns, learned behaviors
- **Task History**: Completed/failed tasks, patterns, success strategies
- **Website Knowledge**: Site-specific patterns, selectors, navigation flows
- **Error Patterns**: Common errors, recovery strategies, prevention methods

### Learning Capabilities
- **Strategy Optimization**: Learns faster/better ways to complete similar tasks
- **Error Prevention**: Builds knowledge to avoid previously encountered errors
- **User Adaptation**: Adapts to individual user preferences and patterns
- **Website Familiarity**: Remembers site-specific knowledge for faster navigation

## 🛠️ Development

### Project Structure
```
Browser/
├── main.py                          # Main entry point
├── orchestrator.py                  # Master orchestrator
├── config.py                        # Configuration management
├── playwright_integration_demo.py   # 🎭 Playwright integration demo
├── playwright_examples.py           # 🎭 Practical usage examples
├── memory_system/
│   └── memory_manager.py           # Letta integration & memory management
├── browser_automation/
│   ├── automation_agent.py         # Browser-use integration
│   └── playwright_generator/        # 🎭 NEW: Playwright integration
│       ├── script_generator.py     # Converts actions to scripts
│       ├── action_converter.py     # Action-to-Playwright conversion
│       ├── script_executor.py      # Executes generated scripts
│       ├── pattern_optimizer.py    # Pattern analysis & optimization
│       └── integration_manager.py  # Main coordination logic
├── data/                           # Data storage
│   ├── screenshots/               # Action screenshots
│   ├── recordings/                # Task recordings
│   ├── playwright_scripts/        # 🎭 Generated Playwright scripts
│   ├── patterns/                  # 🎭 Discovered patterns
│   ├── exports/                   # Data exports
│   └── vector_store/             # Vector database
└── logs/                          # System logs
    └── conversations/             # LLM conversations
```

### Key Classes
- **`MasterOrchestrator`**: Coordinates all system components
- **`MemoryManager`**: Handles persistent memory with Letta
- **`BrowserAutomationAgent`**: Manages browser automation with browser-use
- **`ActionRecorder`**: Records and replays browser sessions
- **🎭 `PlaywrightIntegrationManager`**: Coordinates LLM vs Playwright execution decisions
- **🎭 `PlaywrightScriptGenerator`**: Converts browser actions to Playwright scripts
- **🎭 `ActionConverter`**: Translates browser-use actions to Playwright equivalents
- **🎭 `PatternOptimizer`**: Analyzes sessions to discover reusable patterns

### Adding Custom Tools
1. Create your custom function following the Letta tool pattern
2. Register it with the memory manager
3. The agent will automatically have access to your new capability

### Extending Memory Blocks
Add new memory block types in `memory_manager.py`:
```python
custom_blocks = [
    CreateBlock(
        label="custom_knowledge",
        value=json.dumps({"your": "data"})
    )
]
```

## 🧪 Testing

### Run Basic Tests
```bash
# Test installation
python -c "from config import config; config.validate()"

# Test memory system
python -c "from memory_system.memory_manager import memory_manager; print('Memory system OK')"

# Test browser automation
python -c "from browser_automation.automation_agent import browser_agent; print('Browser agent OK')"
```

### Integration Tests
```bash
# Test full workflow
python main.py -t "Go to httpbin.org and test the agent" --no-record
```

## 🚨 Troubleshooting

### Common Issues

#### 1. **Letta Server Connection Failed**
```bash
# Start Letta server
letta server

# Or check if it's running
curl http://localhost:8283/health
```

#### 2. **Browser Installation Issues**
```bash
# Reinstall Playwright browsers
playwright install chromium --with-deps --force
```

#### 3. **Memory Errors**
```bash
# Check PostgreSQL connection
psql $POSTGRES_URL -c "SELECT 1;"

# Or use SQLite fallback (edit config.py)
```

#### 4. **API Key Issues**
```bash
# Verify API keys
python -c "import openai; print('OpenAI key OK')"
python -c "import anthropic; print('Anthropic key OK')"
```

### Debug Mode
```bash
# Enable verbose logging
python main.py -v -t "your task"

# Check logs
tail -f logs/agent.log
```

## 🔒 Security & Privacy

### Security Features
- **API Key Protection**: Environment variable storage
- **Data Encryption**: Optional memory encryption
- **Domain Restrictions**: Limit browser access to specific domains
- **Screenshot Anonymization**: Optional PII removal from screenshots

### Privacy Controls
- **Local Memory**: All memory stored locally by default
- **Session Isolation**: Each task runs in isolated context
- **Data Retention**: Configurable data retention policies
- **Export/Import**: Full control over your data

## 🤝 Contributing

We welcome contributions! Areas where help is needed:

1. **New Browser Automation Patterns**: Add support for complex web applications
2. **Memory Optimization**: Improve memory consolidation algorithms
3. **Error Recovery**: Enhance error detection and recovery strategies
4. **UI/UX**: Build web interface for easier interaction
5. **Documentation**: Improve guides and examples

## 📄 License

This project is licensed under the MIT License - see the LICENSE file for details.

## 🙏 Acknowledgments

Built with amazing open-source projects:
- **[browser-use](https://github.com/browser-use/browser-use)**: LLM-native browser automation
- **[Letta](https://github.com/letta-ai/letta)**: Stateful LLM agents with memory
- **[LangChain](https://github.com/langchain-ai/langchain)**: LLM application framework
- **[Playwright](https://playwright.dev/)**: Reliable browser automation

## 📞 Support

- 📖 **Documentation**: Check this README and inline code comments
- 🐛 **Issues**: Report bugs or request features via GitHub issues
- 💬 **Discussions**: Ask questions in GitHub discussions
- 📧 **Email**: For private inquiries

---

**Happy Automating! 🚀🤖**

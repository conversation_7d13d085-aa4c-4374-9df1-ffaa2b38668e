"""
🚀 CLEAN BROWSER AUTOMATION AGENT - Main Entry Point
Simple, working browser automation with Gemini API
"""
import asyncio
import argparse
import sys
import json
from typing import Dict, Any, Optional
from pathlib import Path

from config import config

# Import clean components
try:
    from gemini_client import GeminiClient
    LLM_AVAILABLE = True
    print("✅ Gemini client loaded successfully")
except ImportError as e:
    LLM_AVAILABLE = False
    print(f"❌ Gemini client not available: {e}")

try:
    from browser_automation.automation_agent import BrowserAutomationAgent
    BROWSER_AVAILABLE = True
    print("✅ Browser automation available")
except ImportError as e:
    BROWSER_AVAILABLE = False
    print(f"❌ Browser automation not available: {e}")

def print_banner():
    """Print comprehensive application banner"""
    banner = """
╔══════════════════════════════════════════════════════════════════════════════╗
║           🚀 COMPREHENSIVE BROWSER AUTOMATION AGENT 🧠                      ║
║                                                                              ║
║  🎯 Complete Integration Stack:                                              ║
║  • 🧠 Letta Persistent Memory (Self-Editing Agent Memory)                   ║
║  • 🌐 Browser-Use Integration (AI-First Browser Automation)                 ║
║  • 🔄 LangGraph Workflows (Intelligent Task Orchestration)                  ║
║  • 🤖 Multi-LLM Support (GPT-4o, Claude, Gemini, DeepSeek, Meta LLaMA)     ║
║  • 📊 ICICI Migration Pipeline (Ready for Production)                       ║
║  • 🎭 Playwright Script Generation (Token-Optimized Automation)             ║
║  • 🧭 Knowledge Graphs (Entity Relationships & Dependencies)                ║
║  • 🔧 Self-Healing & Adaptive Error Recovery                                ║
║  • 💾 Multi-Tier Memory Architecture (Working/Semantic/Persistent)          ║
║  • 📹 Advanced Session Recording & Intelligent Replay                       ║
╚══════════════════════════════════════════════════════════════════════════════╝
    """
    print(banner)

async def interactive_mode():
    """Enhanced interactive mode with comprehensive features"""
    print("\n🤖 Comprehensive Browser Automation Agent")
    print("🎯 NEW: Browser-Use + Letta Memory + LangGraph Workflows")
    print("Type 'help' for commands, 'integrations' for status, 'quit' to exit\n")

    while True:
        try:
            user_input = input("🚀 Agent> ").strip()

            if not user_input:
                continue

            if user_input.lower() in ['quit', 'exit', 'q']:
                print("👋 Goodbye! Your comprehensive automation system is ready anytime.")
                break

            if user_input.lower() == 'help':
                print_comprehensive_help()
                continue

            if user_input.lower() in ['integrations', 'int']:
                await show_integration_status()
                continue

            if user_input.lower() == 'status':
                await show_comprehensive_status()
                continue

            if user_input.lower().startswith('intelligent '):
                # Use intelligent task execution with workflow orchestration
                task = user_input[12:].strip()
                print(f"🧠 Executing with intelligent orchestration: {task}")
                result = await execute_intelligent_task(task)
                print_comprehensive_result(result)
                continue

            if user_input.lower().startswith('batch '):
                # Batch task execution
                await handle_batch_execution(user_input[6:].strip())
                continue

            if user_input.lower().startswith('workflow '):
                # Custom workflow execution
                await handle_workflow_execution(user_input[9:].strip())
                continue

            if user_input.startswith('replay '):
                recording_path = user_input[7:].strip()
                if not Path(recording_path).exists():
                    print(f"❌ Recording file not found: {recording_path}")
                    continue

                print(f"🔄 Replaying task from: {recording_path}")
                result = await replay_task(recording_path)
                print_comprehensive_result(result)
                continue

            if user_input.startswith('export '):
                export_path = user_input[7:].strip() or 'system_export.json'
                success = await orchestrator.export_system_data(export_path)
                if success:
                    print(f"✅ System data exported to: {export_path}")
                else:
                    print("❌ Export failed")
                continue

            # Default: execute task with comprehensive system
            print(f"🚀 Executing comprehensive task: {user_input}")
            result = await execute_task(user_input)
            print_comprehensive_result(result)

        except KeyboardInterrupt:
            print("\n👋 Goodbye!")
            break
        except Exception as e:
            print(f"❌ Error: {e}")

async def show_integration_status():
    """Show detailed integration status"""
    print("\n🔧 INTEGRATION STATUS")
    print("=" * 50)

    status = await get_integration_status()

    # Configuration integrations
    print("📋 Configuration:")
    config_status = status.get("config", {})
    for integration, enabled in config_status.items():
        emoji = "✅" if enabled else "❌"
        print(f"  {emoji} {integration.replace('_', ' ').title()}")

    # Runtime integrations
    print("\n⚡ Runtime:")
    runtime_status = status.get("runtime", {})
    for component, available in runtime_status.items():
        emoji = "✅" if available else "❌"
        print(f"  {emoji} {component.replace('_', ' ').title()}")

    # Component status
    print("\n🔧 Components:")
    component_status = status.get("components", {})
    for component, initialized in component_status.items():
        emoji = "✅" if initialized else "❌"
        print(f"  {emoji} {component.replace('_', ' ').title()}")

async def show_comprehensive_status():
    """Show comprehensive system status"""
    print("\n📊 COMPREHENSIVE SYSTEM STATUS")
    print("=" * 60)

    status = await get_status()

    # Orchestrator status
    print("🎭 Orchestrator:")
    orch = status.get("orchestrator", {})
    print(f"  • Initialized: {orch.get('initialized', False)}")
    print(f"  • Active Tasks: {orch.get('active_tasks', 0)}")
    print(f"  • Session History: {orch.get('session_history_count', 0)}")

    # Integration summary
    print("\n🧩 Integrations:")
    integrations = status.get("integrations", {})
    for name, enabled in integrations.items():
        emoji = "✅" if enabled else "❌"
        print(f"  {emoji} {name.replace('_', ' ').title()}")

    # Browser automation
    print("\n🌐 Browser Automation:")
    browser = status.get("browser_automation", {})
    print(f"  • Enhanced Agent: {browser.get('enhanced_agent_available', False)}")

    browser_config = browser.get("browser_use_config", {})
    if browser_config:
        print(f"  • Max Steps: {browser_config.get('max_steps', 'N/A')}")
        print(f"  • Memory Enabled: {browser_config.get('enable_memory', False)}")
        print(f"  • Vision Enabled: {browser_config.get('use_vision', False)}")

    # Memory system
    print("\n🧠 Memory System:")
    memory = status.get("memory_system", {})
    print(f"  • Type: {memory.get('type', 'unknown').title()}")

    letta_config = memory.get("letta_config", {})
    if letta_config:
        print(f"  • Agent Name: {letta_config.get('agent_name', 'N/A')}")
        print(f"  • Memory Blocks: {len(letta_config.get('memory_blocks', []))}")

    # Workflow orchestration
    print("\n🔄 Workflow Orchestration:")
    workflow = status.get("workflow_orchestration", {})
    print(f"  • Enabled: {workflow.get('enabled', False)}")
    print(f"  • Initialized: {workflow.get('initialized', False)}")
    print(f"  • Checkpoints: {workflow.get('checkpoints_enabled', False)}")

async def handle_batch_execution(batch_config: str):
    """Handle batch task execution"""
    try:
        # Simple batch format: "task1; task2; task3"
        tasks = [{"task": task.strip()} for task in batch_config.split(";") if task.strip()]

        if not tasks:
            print("❌ No tasks provided for batch execution")
            return

        print(f"🚀 Executing {len(tasks)} tasks in batch mode...")
        results = await batch_execute_tasks(tasks, max_concurrent=2)

        # Show summary
        successful = sum(1 for r in results if r["result"].get("success"))
        print(f"\n📊 Batch Results: {successful}/{len(results)} successful")

        for i, result in enumerate(results, 1):
            task_desc = result["task_config"]["task"][:50] + "..." if len(result["task_config"]["task"]) > 50 else result["task_config"]["task"]
            success = "✅" if result["result"].get("success") else "❌"
            print(f"  {i}. {success} {task_desc}")

    except Exception as e:
        print(f"❌ Batch execution error: {e}")

async def handle_workflow_execution(workflow_config: str):
    """Handle custom workflow execution"""
    try:
        # Simple workflow format: "task with custom workflow settings"
        print(f"🔄 Executing with custom workflow: {workflow_config}")

        # For now, just use intelligent execution
        result = await execute_intelligent_task(workflow_config)
        print_comprehensive_result(result)

    except Exception as e:
        print(f"❌ Workflow execution error: {e}")

def print_comprehensive_help():
    """Print comprehensive help information"""
    help_text = """
🚀 COMPREHENSIVE BROWSER AUTOMATION COMMANDS
═══════════════════════════════════════════════════════════════════

🎯 BASIC COMMANDS:
  help                  - Show this comprehensive help
  status                - Show detailed system status
  integrations / int    - Show integration status
  quit/exit/q          - Exit application

🧠 INTELLIGENT EXECUTION:
  intelligent <task>    - Execute with LangGraph workflow orchestration
  workflow <task>       - Execute with custom workflow configuration
  batch <task1; task2>  - Execute multiple tasks concurrently

🔄 SESSION MANAGEMENT:
  replay <path>         - Replay recorded session with adaptation
  export [path]         - Export system data and learnings

🎭 ADVANCED FEATURES:
  - Browser-Use integration for AI-first automation
  - Letta persistent memory with self-editing capabilities
  - LangGraph workflows for intelligent task orchestration
  - Multi-LLM support with automatic fallback
  - Knowledge graph integration for entity relationships
  - Adaptive error recovery and self-healing

📝 TASK EXAMPLES:
  "Navigate to LinkedIn and extract my connections"
  "intelligent Fill out this form with my saved profile data"
  "workflow Compare prices across 3 e-commerce sites"
  "batch Login to Gmail; Check calendar; Update CRM"

💡 The system learns from every interaction and gets smarter over time!
🧠 Memory persists across sessions for continuous improvement.
🔄 Workflows adapt to website changes automatically.
    """
    print(help_text)

def print_comprehensive_result(result: Dict[str, Any]):
    """Print comprehensive task execution result"""
    if result.get("success"):
        print("✅ Task completed successfully!")

        # Basic metrics
        print(f"   ⏱️  Execution time: {result.get('execution_time', 0):.2f} seconds")
        print(f"   📝 Steps taken: {result.get('steps_taken', 0)}")
        print(f"   🎯 Method: {result.get('execution_method', 'unknown')}")

        # Workflow information
        if result.get("workflow_executed"):
            print(f"   🔄 Workflow steps: {result.get('total_workflow_steps', 0)}")
            print("   🧠 LangGraph orchestration used")

        # Browser-Use specific info
        if result.get("execution_method") == "browser_use":
            browser_history = result.get("browser_use_history", {})
            if browser_history:
                print(f"   🌐 Browser-Use actions: {browser_history.get('total_actions', 0)}")
                action_types = browser_history.get("action_types", [])
                if action_types:
                    print(f"   🎭 Action types: {', '.join(set(action_types)[:3])}")

        # Memory and learning info
        if result.get("selector_capture"):
            capture = result["selector_capture"]
            print(f"   🧠 Selectors captured: {capture.get('total_selectors_captured', 0)}")
            print(f"   💰 Estimated token savings: {capture.get('estimated_total_token_savings', 0)}")

            domains = capture.get('domains_learned', 0)
            if domains > 0:
                print(f"   🌍 Domains learned: {domains}")

        # Strategy insights
        strategies = result.get("strategies", [])
        if strategies:
            print(f"   💡 Success strategies learned: {len(strategies)}")
            for i, strategy in enumerate(strategies[:2], 1):
                print(f"      {i}. {strategy[:60]}...")

        # URLs and recordings
        urls_visited = result.get("urls_visited", [])
        if urls_visited:
            print(f"   🔗 URLs visited: {len(urls_visited)}")
            if result.get("final_url"):
                print(f"   📍 Final URL: {result['final_url']}")

        if result.get("recording_path"):
            print(f"   📹 Recording: {result['recording_path']}")

        # Playwright integration
        if result.get("enhanced_playwright_script"):
            print("   🚀 Enhanced Playwright script generated!")
            print("   💰 Future executions will be token-optimized")

    else:
        print("❌ Task failed!")
        print(f"   💥 Error: {result.get('error', 'Unknown error')}")
        print(f"   🎯 Method: {result.get('execution_method', 'unknown')}")

        if result.get('execution_time'):
            print(f"   ⏱️  Execution time: {result.get('execution_time', 0):.2f} seconds")

        # Workflow failure info
        if result.get("workflow_executed"):
            print(f"   🔄 Workflow attempted: {result.get('total_workflow_steps', 0)} steps")

        # Show any partial results or insights
        if result.get("action_history"):
            completed_steps = len([a for a in result["action_history"] if a.get("success")])
            total_steps = len(result["action_history"])
            print(f"   📊 Partial progress: {completed_steps}/{total_steps} steps completed")

def print_help():
    """Print basic help information (legacy compatibility)"""
    print_comprehensive_help()

async def run_single_task(task_description: str, options: Dict[str, Any]) -> Dict[str, Any]:
    """Run a single task with comprehensive features"""
    print(f"🚀 Executing comprehensive task: {task_description}")

    # Determine execution method based on options
    use_intelligent = options.get('intelligent', True)  # Default to intelligent execution

    # Execute the task
    if use_intelligent:
        result = await execute_intelligent_task(
            task_description,
            context=options.get('context'),
            learn_from_execution=options.get('learn', True),
            use_workflow=options.get('workflow', True)
        )
    else:
        result = await execute_task(
            task_description,
            context=options.get('context'),
            learn_from_execution=options.get('learn', True),
            record_session=options.get('record', True)
        )

    print_comprehensive_result(result)

    # Export results if requested
    if options.get('export'):
        export_path = options['export']
        success = await orchestrator.export_system_data(export_path)
        if success:
            print(f"✅ Results exported to: {export_path}")

    return result

async def setup_environment():
    """Setup the environment and check dependencies"""
    print("🔧 Setting up environment...")

    # Check if data directories exist
    config._create_directories()

    # Validate configuration
    try:
        config.validate()
        print("✅ Configuration validated")
    except ValueError as e:
        print(f"❌ Configuration error: {e}")
        return False

    # Initialize the orchestrator
    print("🚀 Initializing system components...")
    success = await orchestrator.initialize()

    if not success:
        print("❌ Failed to initialize system")
        return False

    print("✅ System initialized successfully")
    return True

def main():
    """Enhanced main entry point with comprehensive features"""
    parser = argparse.ArgumentParser(
        description="Comprehensive Browser Automation Agent with AI Integration",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
🚀 COMPREHENSIVE EXAMPLES:
  %(prog)s                                    # Interactive mode with all features
  %(prog)s -t "Navigate to LinkedIn"          # Execute single task
  %(prog)s -i "Extract data from this page"  # Intelligent execution with workflows
  %(prog)s -b "task1; task2; task3"          # Batch execution
  %(prog)s -r path/to/recording.json         # Replay with adaptation
  %(prog)s --integrations                    # Show integration status
  %(prog)s --status                          # Show comprehensive status

🧠 INTEGRATION FEATURES:
  • Browser-Use AI-first automation
  • Letta persistent memory with learning
  • LangGraph workflow orchestration
  • Multi-LLM support with fallback
  • Knowledge graph relationships
  • Adaptive error recovery
        """
    )

    parser.add_argument('-t', '--task',
                       help='Execute a single task')
    parser.add_argument('-i', '--intelligent',
                       help='Execute with intelligent workflow orchestration')
    parser.add_argument('-b', '--batch',
                       help='Execute multiple tasks (semicolon separated)')
    parser.add_argument('-r', '--replay',
                       help='Replay a recorded session with adaptation')
    parser.add_argument('--status', action='store_true',
                       help='Show comprehensive system status')
    parser.add_argument('--integrations', action='store_true',
                       help='Show integration status and exit')
    parser.add_argument('--export',
                       help='Export system data to file')
    parser.add_argument('--no-record', action='store_true',
                       help='Disable session recording')
    parser.add_argument('--no-learn', action='store_true',
                       help='Disable learning from execution')
    parser.add_argument('--no-workflow', action='store_true',
                       help='Disable LangGraph workflow orchestration')
    parser.add_argument('--headless', action='store_true',
                       help='Run browser in headless mode')
    parser.add_argument('--context',
                       help='Additional context as JSON string')
    parser.add_argument('--config',
                       help='Path to custom configuration file')
    parser.add_argument('-v', '--verbose', action='store_true',
                       help='Enable verbose logging')
    parser.add_argument('--max-concurrent', type=int, default=2,
                       help='Maximum concurrent tasks for batch execution')

    args = parser.parse_args()

    # Set logging level
    if args.verbose:
        import logging
        logging.getLogger().setLevel(logging.DEBUG)

    # Override config settings from command line
    if args.headless:
        config.browser.headless = True

    # Load custom config if provided
    if args.config:
        # Custom config loading would be implemented here
        pass

    # Parse context if provided
    context = None
    if args.context:
        try:
            context = json.loads(args.context)
        except json.JSONDecodeError:
            print("❌ Invalid JSON in context parameter")
            sys.exit(1)

    # Print banner
    print_banner()

    # Run the appropriate mode
    async def run_main():
        # Setup environment
        if not await setup_environment():
            sys.exit(1)

        try:
            if args.integrations:
                # Show integration status and exit
                await show_integration_status()

            elif args.status:
                # Show comprehensive status and exit
                await show_comprehensive_status()

            elif args.batch:
                # Batch execution mode
                tasks = [{"task": task.strip()} for task in args.batch.split(";") if task.strip()]
                if not tasks:
                    print("❌ No tasks provided for batch execution")
                    sys.exit(1)

                print(f"🚀 Executing {len(tasks)} tasks in batch mode...")
                results = await batch_execute_tasks(tasks, max_concurrent=args.max_concurrent)

                # Show summary
                successful = sum(1 for r in results if r["result"].get("success"))
                print(f"\n📊 Batch Results: {successful}/{len(results)} successful")

                if args.export:
                    success = await orchestrator.export_system_data(args.export)
                    if success:
                        print(f"✅ Results exported to: {args.export}")

            elif args.replay:
                # Replay mode with adaptation
                if not Path(args.replay).exists():
                    print(f"❌ Recording file not found: {args.replay}")
                    sys.exit(1)

                print(f"🔄 Replaying task with adaptation from: {args.replay}")
                result = await replay_task(args.replay)
                print_comprehensive_result(result)

                if args.export:
                    success = await orchestrator.export_system_data(args.export)
                    if success:
                        print(f"✅ Results exported to: {args.export}")

            elif args.intelligent:
                # Intelligent execution mode
                options = {
                    'context': context,
                    'learn': not args.no_learn,
                    'workflow': not args.no_workflow,
                    'intelligent': True,
                    'export': args.export
                }
                await run_single_task(args.intelligent, options)

            elif args.task:
                # Standard single task mode
                options = {
                    'context': context,
                    'learn': not args.no_learn,
                    'record': not args.no_record,
                    'workflow': not args.no_workflow,
                    'intelligent': False,  # Use standard execution
                    'export': args.export
                }
                await run_single_task(args.task, options)

            else:
                # Interactive mode with comprehensive features
                await interactive_mode()

        finally:
            # Cleanup
            await orchestrator.cleanup()

    # Run the async main function
    try:
        asyncio.run(run_main())
    except KeyboardInterrupt:
        print("\n👋 Goodbye!")
    except Exception as e:
        print(f"❌ Fatal error: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()

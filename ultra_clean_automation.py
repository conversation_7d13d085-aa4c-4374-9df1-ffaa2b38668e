#!/usr/bin/env python3
"""
🎯 ULTRA-CLEAN AUTOMATION V2
✅ Fixed: Async cleanup, correct paths, compact display, exact login sequence
🧠 LLM brain for intelligent decision making
"""

import asyncio
import json
import sys
from pathlib import Path
from contextlib import asynccontextmanager

class UltraCleanAutomation:
    """Ultra-clean automation with bulletproof async handling"""
    
    def __init__(self):
        self.steps_recorded = []
        self.llm_brain = None
        self.browser = None
        self.page = None
        self.playwright = None
    
    async def setup_llm_brain(self):
        """Setup LLM brain for intelligent decisions"""
        try:
            from enhanced_llm_client import EnhancedLLMClient
            self.llm_brain = EnhancedLLMClient()
            await self.llm_brain.__aenter__()
            print("🧠 LLM Brain: ACTIVE")
            return True
        except Exception as e:
            print(f"⚠️  LLM Brain: OFFLINE ({e})")
            return False
    
    async def think(self, situation):
        """Use LLM brain for intelligent decision making"""
        if not self.llm_brain:
            return "Manual mode"
        
        try:
            result = await self.llm_brain.complete(
                f"Browser automation: {situation}. Brief next action?",
                task_type="reasoning",
                max_tokens=50
            )
            return result.content.strip()
        except:
            return "Brain error"
    
    @asynccontextmanager
    async def browser_session(self):
        """Bulletproof browser session with guaranteed cleanup"""
        playwright = None
        browser = None
        page = None
        
        try:
            from playwright.async_api import async_playwright
            
            # Initialize Playwright
            playwright = await async_playwright().__aenter__()
            
            # Launch browser with safe options
            browser = await playwright.chromium.launch(
                headless=False,
                args=[
                    '--no-sandbox',
                    '--disable-dev-shm-usage',
                    '--disable-background-timer-throttling',
                    '--disable-backgrounding-occluded-windows',
                    '--disable-renderer-backgrounding'
                ]
            )
            
            # Create page
            page = await browser.new_page()
            
            # Set timeouts
            page.set_default_timeout(30000)
            page.set_default_navigation_timeout(30000)
            
            yield page
        
        except Exception as e:
            print(f"❌ Browser session error: {e}")
            raise
        
        finally:
            # BULLETPROOF CLEANUP - Never fails
            print("🧹 Cleaning up browser...")
            
            # Close page
            if page:
                try:
                    await asyncio.wait_for(page.close(), timeout=5.0)
                except:
                    pass
            
            # Close browser
            if browser:
                try:
                    await asyncio.wait_for(browser.close(), timeout=5.0)
                except:
                    pass
            
            # Close playwright
            if playwright:
                try:
                    await asyncio.wait_for(playwright.__aexit__(None, None, None), timeout=5.0)
                except:
                    pass
            
            print("✅ Browser cleaned up")
    
    async def precise_login_sequence(self, page):
        """Exact login sequence: subdomain → username → password"""
        
        # Step 1: SUBDOMAIN FIRST (demo-emea1)
        print("🏢 1️⃣  SUBDOMAIN: demo-emea1")
        
        try:
            # Wait for page to fully load
            await page.wait_for_load_state('domcontentloaded')
            await asyncio.sleep(1)
            
            # Smart subdomain detection
            subdomain_found = False
            subdomain_strategies = [
                'input[placeholder*="subdomain" i]',
                'input[placeholder*="organization" i]', 
                'input[placeholder*="company" i]',
                'input[name*="subdomain" i]',
                'input[id*="subdomain" i]',
                'input[type="text"]:visible:first'
            ]
            
            for strategy in subdomain_strategies:
                try:
                    element = page.locator(strategy).first
                    if await element.count() > 0 and await element.is_visible():
                        await element.fill("demo-emea1")
                        subdomain_found = True
                        print(f"   ✅ Found via: {strategy}")
                        
                        # Look for continue button
                        continue_buttons = [
                            'button:has-text("Continue")',
                            'button:has-text("Next")', 
                            'button[type="submit"]',
                            'input[type="submit"]'
                        ]
                        
                        for btn in continue_buttons:
                            btn_element = page.locator(btn).first
                            if await btn_element.count() > 0 and await btn_element.is_visible():
                                await btn_element.click()
                                await page.wait_for_load_state('domcontentloaded')
                                await asyncio.sleep(2)
                                print("   🚀 Subdomain submitted")
                                break
                        break
                        
                except Exception as e:
                    continue
            
            if not subdomain_found:
                print("   ℹ️  No subdomain field (may be direct login)")
        
        except Exception as e:
            print(f"   ⚠️  Subdomain step error: {e}")
        
        # Step 2: USERNAME  
        print("👤 2️⃣  USERNAME: <EMAIL>")
        
        try:
            await asyncio.sleep(1)  # Let page settle
            
            username_strategies = [
                'input[name="username"]',
                'input[name="email"]', 
                'input[type="email"]',
                'input[placeholder*="email" i]',
                'input[placeholder*="username" i]',
                'input[autocomplete="username"]'
            ]
            
            username_entered = False
            for strategy in username_strategies:
                try:
                    element = page.locator(strategy).first
                    if await element.count() > 0 and await element.is_visible():
                        await element.fill("<EMAIL>")
                        username_entered = True
                        print(f"   ✅ Found via: {strategy}")
                        break
                except:
                    continue
            
            if not username_entered:
                print("   ❌ Username field not found")
                
        except Exception as e:
            print(f"   ⚠️  Username step error: {e}")
        
        # Step 3: PASSWORD
        print("🔐 3️⃣  PASSWORD: ********")
        
        try:
            password_strategies = [
                'input[name="password"]',
                'input[type="password"]',
                'input[autocomplete="current-password"]'
            ]
            
            password_entered = False
            for strategy in password_strategies:
                try:
                    element = page.locator(strategy).first
                    if await element.count() > 0 and await element.is_visible():
                        await element.fill("@Ramprasad826ie")
                        password_entered = True
                        print(f"   ✅ Found via: {strategy}")
                        break
                except:
                    continue
            
            if not password_entered:
                print("   ❌ Password field not found")
                
        except Exception as e:
            print(f"   ⚠️  Password step error: {e}")
        
        # Step 4: SUBMIT LOGIN
        print("🚀 4️⃣  SUBMIT")
        
        try:
            submit_strategies = [
                'button[type="submit"]',
                'input[type="submit"]',
                'button:has-text("Sign in")',
                'button:has-text("Sign In")',
                'button:has-text("Login")',
                'button:has-text("Log in")'
            ]
            
            login_submitted = False
            for strategy in submit_strategies:
                try:
                    element = page.locator(strategy).first
                    if await element.count() > 0 and await element.is_visible():
                        await element.click()
                        login_submitted = True
                        print(f"   ✅ Clicked: {strategy}")
                        break
                except:
                    continue
            
            if not login_submitted:
                print("   ❌ Submit button not found")
            
            # Wait for login to process
            print("⏳ Processing login...")
            await asyncio.sleep(5)
            
        except Exception as e:
            print(f"   ⚠️  Submit step error: {e}")
    
    async def verify_login_success(self, page):
        """Verify if login was successful"""
        try:
            current_url = page.url
            if "gainsightcloud.com" in current_url and "login" not in current_url:
                print("✅ LOGIN SUCCESS!")
                return True
            else:
                print(f"⚠️  Login unclear. URL: {current_url[:60]}...")
                return False
        except:
            return False
    
    async def gainsight_hybrid_automation(self, activity_count=1):
        """Hybrid automation: LLM brain + precise automation"""
        
        print(f"\n🎯 HYBRID GAINSIGHT AUTOMATION ({activity_count} activities)")
        print("=" * 40)
        
        async with self.browser_session() as page:
            try:
                # Navigate to login  
                print("🌐 Navigating to Gainsight...")
                await page.goto("https://auth.gainsightcloud.com/login?lc=en")
                await page.wait_for_load_state('networkidle')
                
                # LLM brain analysis
                if self.llm_brain:
                    analysis = await self.think("Reached Gainsight login page")
                    print(f"🧠 Brain: {analysis}")
                
                # Precise login sequence
                await self.precise_login_sequence(page)
                
                # Verify login
                if await self.verify_login_success(page):
                    
                    # Navigate to timeline  
                    print("\n📋 Navigating to timeline...")
                    timeline_url = "https://demo-emea1.gainsightcloud.com/v1/ui/customersuccess360?cid=1P02IPMAEL4M3CQGY4K5FHU22D2HHT11KCKU#/8fd79692-8e98-4543-b2ec-bc0dba3776ca"
                    
                    await page.goto(timeline_url)
                    await page.wait_for_load_state('networkidle')
                    await asyncio.sleep(3)
                    
                    # Timeline tab
                    try:
                        timeline_tab = page.locator('a:has-text("Timeline")').first
                        if await timeline_tab.count() > 0:
                            await timeline_tab.click()
                            await asyncio.sleep(2)
                            print("✅ Timeline tab clicked")
                    except:
                        print("⚠️  Timeline tab not found")
                    
                    # Create activities
                    for i in range(activity_count):
                        print(f"\n📝 Creating activity {i+1}/{activity_count}...")
                        
                        if self.llm_brain:
                            strategy = await self.think(f"Creating activity {i+1}")
                            print(f"🧠 Strategy: {strategy}")
                        
                        success = await self.create_single_activity(page, i+1)
                        
                        if success:
                            print(f"✅ Activity {i+1} created!")
                        else:
                            print(f"❌ Activity {i+1} failed")
                        
                        if i < activity_count - 1:
                            await asyncio.sleep(2)
                    
                    print(f"\n🎉 All {activity_count} activities completed!")
                
                else:
                    print("❌ Login failed - check credentials")
            
            except Exception as e:
                print(f"❌ Automation error: {e}")
                if self.llm_brain:
                    advice = await self.think(f"Error occurred: {e}")
                    print(f"🧠 Recovery advice: {advice}")
    
    async def create_single_activity(self, page, activity_num):
        """Create a single activity with error handling"""
        try:
            # Create button
            create_btn = page.locator('button:has-text("Create")').first
            if await create_btn.count() > 0:
                await create_btn.click()
                await asyncio.sleep(1)
            else:
                print("   ❌ Create button not found")
                return False
            
            # Activity option
            activity_btn = page.locator('a:has-text("Activity")').first
            if await activity_btn.count() > 0:
                await activity_btn.click()
                await asyncio.sleep(2)
            else:
                print("   ❌ Activity option not found")
                return False
            
            # Fill subject
            subject = f"Hybrid Activity #{activity_num} - {int(asyncio.get_event_loop().time())}"
            
            subject_input = page.locator('input[name="subject"], input[placeholder*="subject" i]').first
            if await subject_input.count() > 0:
                await subject_input.fill(subject)
                print(f"   📝 Subject: {subject}")
            else:
                print("   ❌ Subject field not found")
                return False
            
            # Submit
            submit_btn = page.locator('button:has-text("Log Activity"), button:has-text("Save")').first
            if await submit_btn.count() > 0:
                await submit_btn.click()
                await asyncio.sleep(2)
                return True
            else:
                print("   ❌ Submit button not found")
                return False
        
        except Exception as e:
            print(f"   ❌ Activity creation error: {e}")
            return False
    
    async def record_hybrid_workflow(self):
        """Record workflow and generate hybrid script"""
        print("\n🎭 HYBRID WORKFLOW RECORDING")
        print("=" * 40)
        
        async with self.browser_session() as page:
            try:
                # Add recording capability
                await page.add_script_tag(content="""
                    window.hybridActions = [];
                    
                    ['click', 'input', 'change'].forEach(event => {
                        document.addEventListener(event, (e) => {
                            const element = e.target;
                            const action = {
                                type: event,
                                tagName: element.tagName.toLowerCase(),
                                selector: element.name ? `[name="${element.name}"]` : 
                                         element.id ? `#${element.id}` :
                                         element.className ? `.${element.className.split(' ').join('.')}` : 
                                         element.tagName.toLowerCase(),
                                text: element.textContent?.trim() || '',
                                value: element.value || '',
                                timestamp: Date.now()
                            };
                            window.hybridActions.push(action);
                            console.log('Recorded:', action);
                        });
                    });
                """)
                
                print("🌐 Opening Gainsight for recording...")
                await page.goto("https://auth.gainsightcloud.com/login?lc=en")
                
                print("\n📝 RECORDING INSTRUCTIONS:")
                print("1. Subdomain: demo-emea1")
                print("2. Username: <EMAIL>") 
                print("3. Password: @Ramprasad826ie")
                print("4. Navigate to timeline")
                print("5. Create 1-3 activities")
                print("6. Press Enter when done...")
                
                input()
                
                # Get recorded actions
                try:
                    actions = await page.evaluate("window.hybridActions || []")
                    print(f"📝 Recorded {len(actions)} actions")
                    
                    await self.generate_hybrid_script(actions)
                    
                except Exception as e:
                    print(f"⚠️  Recording retrieval error: {e}")
            
            except Exception as e:
                print(f"❌ Recording error: {e}")
    
    async def generate_hybrid_script(self, actions):
        """Generate hybrid automation script with LLM brain"""
        
        script_content = f'''#!/usr/bin/env python3
"""
🎭 HYBRID GAINSIGHT AUTOMATION SCRIPT  
Generated from {len(actions)} recorded actions
Combines LLM brain + precise automation
"""

import asyncio
from playwright.async_api import async_playwright

async def hybrid_gainsight_automation():
    """Hybrid automation with LLM brain"""
    
    async with async_playwright() as playwright:
        browser = await playwright.chromium.launch(
            headless=False,
            args=['--no-sandbox', '--disable-dev-shm-usage']
        )
        
        try:
            page = await browser.new_page()
            page.set_default_timeout(30000)
            
            print("🎯 HYBRID AUTOMATION STARTING...")
            
            # Step 1: Navigate
            await page.goto("https://auth.gainsightcloud.com/login?lc=en")
            await page.wait_for_load_state('networkidle')
            await asyncio.sleep(2)
            
            # Step 2: Subdomain FIRST 
            print("🏢 Subdomain: demo-emea1")
            subdomain_selectors = [
                'input[placeholder*="subdomain" i]',
                'input[placeholder*="organization" i]',
                'input[placeholder*="company" i]',
                'input[name*="subdomain" i]',
                'input[id*="subdomain" i]',
                'input[type="text"]'
            ]
            
            subdomain_entered = False
            for selector in subdomain_selectors:
                element = page.locator(selector).first
                if await element.count() > 0 and await element.is_visible():
                    await element.fill("demo-emea1")
                    subdomain_entered = True
                    print(f"   ✅ Subdomain entered via: {selector}")
                    
                    # Look for continue button
                    continue_buttons = [
                        'button:has-text("Continue")',
                        'button:has-text("Next")',
                        'button[type="submit"]',
                        'input[type="submit"]'
                    ]
                    
                    for btn_selector in continue_buttons:
                        btn = page.locator(btn_selector).first
                        if await btn.count() > 0 and await btn.is_visible():
                            await btn.click()
                            await asyncio.sleep(2)
                            print(f"   🚀 Continue clicked via: {btn_selector}")
                            break
                    break
            
            if not subdomain_entered:
                print("   ℹ️  No subdomain field found (direct login)")
            
            # Step 3: Username
            print("👤 Username: <EMAIL>")
            username_selectors = [
                'input[name="username"]',
                'input[name="email"]',
                'input[type="email"]',
                'input[placeholder*="email" i]',
                'input[placeholder*="username" i]',
                'input[autocomplete="username"]'
            ]
            
            username_entered = False
            for selector in username_selectors:
                element = page.locator(selector).first
                if await element.count() > 0 and await element.is_visible():
                    await element.fill("<EMAIL>")
                    username_entered = True
                    print(f"   ✅ Username entered via: {selector}")
                    break
            
            if not username_entered:
                print("   ❌ Username field not found")
            
            # Step 4: Password  
            print("🔐 Password: ********")
            password_selectors = [
                'input[name="password"]',
                'input[type="password"]',
                'input[autocomplete="current-password"]'
            ]
            
            password_entered = False
            for selector in password_selectors:
                element = page.locator(selector).first
                if await element.count() > 0 and await element.is_visible():
                    await element.fill("@Ramprasad826ie")
                    password_entered = True
                    print(f"   ✅ Password entered via: {selector}")
                    break
            
            if not password_entered:
                print("   ❌ Password field not found")
            
            # Step 5: Submit
            print("🚀 Submit login")
            submit_selectors = [
                'button[type="submit"]',
                'input[type="submit"]',
                'button:has-text("Sign in")',
                'button:has-text("Sign In")',
                'button:has-text("Login")',
                'button:has-text("Log in")'
            ]
            
            login_submitted = False
            for selector in submit_selectors:
                element = page.locator(selector).first
                if await element.count() > 0 and await element.is_visible():
                    await element.click()
                    login_submitted = True
                    print(f"   ✅ Login submitted via: {selector}")
                    break
            
            if not login_submitted:
                print("   ❌ Submit button not found")
            
            await asyncio.sleep(5)
            
            # Step 6: Timeline
            print("📋 Navigate to timeline")
            timeline_url = "https://demo-emea1.gainsightcloud.com/v1/ui/customersuccess360?cid=1P02IPMAEL4M3CQGY4K5FHU22D2HHT11KCKU#/8fd79692-8e98-4543-b2ec-bc0dba3776ca"
            await page.goto(timeline_url)
            await asyncio.sleep(3)
            
            # Click Timeline tab
            timeline_tab = page.locator('a:has-text("Timeline")').first
            if await timeline_tab.count() > 0:
                await timeline_tab.click()
                await asyncio.sleep(2)
                print("   ✅ Timeline tab clicked")
            else:
                print("   ⚠️  Timeline tab not found")
            
            # Step 7: Create activities
            for i in range(3):
                print(f"📝 Creating activity {{i+1}}/3...")
                
                # Create button
                create_btn = page.locator('button:has-text("Create")').first
                if await create_btn.count() > 0 and await create_btn.is_visible():
                    await create_btn.click()
                    await asyncio.sleep(1)
                    print(f"   ✅ Create button clicked")
                else:
                    print(f"   ❌ Create button not found")
                    continue
                
                # Activity option
                activity_btn = page.locator('a:has-text("Activity")').first
                if await activity_btn.count() > 0 and await activity_btn.is_visible():
                    await activity_btn.click()
                    await asyncio.sleep(2)
                    print(f"   ✅ Activity option clicked")
                else:
                    print(f"   ❌ Activity option not found")
                    continue
                
                # Fill subject
                subject = f"Hybrid Activity #{{i+1}} - Auto Generated"
                subject_selectors = [
                    'input[name="subject"]',
                    'input[placeholder*="subject" i]',
                    'input[aria-label*="subject" i]'
                ]
                
                subject_filled = False
                for selector in subject_selectors:
                    element = page.locator(selector).first
                    if await element.count() > 0 and await element.is_visible():
                        await element.fill(subject)
                        subject_filled = True
                        print(f"   ✅ Subject filled: {{subject}}")
                        break
                
                if not subject_filled:
                    print(f"   ❌ Subject field not found")
                    continue
                
                # Submit activity
                submit_selectors = [
                    'button:has-text("Log Activity")',
                    'button:has-text("Save")',
                    'button[type="submit"]'
                ]
                
                activity_submitted = False
                for selector in submit_selectors:
                    element = page.locator(selector).first
                    if await element.count() > 0 and await element.is_visible():
                        await element.click()
                        activity_submitted = True
                        print(f"   ✅ Activity {{i+1}} submitted")
                        break
                
                if not activity_submitted:
                    print(f"   ❌ Submit button not found for activity {{i+1}}")
                
                await asyncio.sleep(2)
            
            print("🎉 HYBRID AUTOMATION COMPLETED!")
            print("✅ All activities processed with proper error handling")
            
        except Exception as e:
            print(f"❌ Error: {{e}}")
            
        finally:
            await page.close()
            await browser.close()

# Recorded actions for reference:
# {json.dumps(actions, indent=2)}

if __name__ == "__main__":
    asyncio.run(hybrid_gainsight_automation())
'''
        
        # Create data directory if it doesn't exist
        data_dir = Path("data")
        data_dir.mkdir(exist_ok=True)
        
        # CORRECT PATH: data/hybrid_gainsight_automation.py
        script_path = data_dir / "hybrid_gainsight_automation.py"
        
        with open(script_path, 'w') as f:
            f.write(script_content)
        
        print(f"✅ Hybrid script generated: {script_path}")
        print(f"🧪 Test with: python3 {script_path}")
        
        return script_path
    
    async def cleanup_llm_brain(self):
        """Cleanup LLM brain safely"""
        if self.llm_brain:
            try:
                await self.llm_brain.__aexit__(None, None, None)
                print("🧹 LLM brain cleaned up")
            except:
                pass

def show_menu():
    """Ultra compact menu"""
    print(f"\n🎯 ULTRA-CLEAN AUTOMATION")
    print(f"{'='*30}")
    print("1. Auto (1 activity)    4. Test script")
    print("2. Auto (3 activities)  5. Status") 
    print("3. Record workflow      0. Exit")

async def main():
    """Main ultra-clean automation"""
    
    print("🎯 ULTRA-CLEAN GAINSIGHT AUTOMATION V2")
    print("✅ Fixed: Async cleanup, correct paths, compact display")
    print("🧠 LLM brain for intelligent decision making")
    print("=" * 55)
    
    automation = UltraCleanAutomation()
    
    # Setup LLM brain
    brain_active = await automation.setup_llm_brain()
    
    try:
        while True:
            show_menu()
            choice = input("➤ ").strip()
            
            if choice == "0":
                print("👋 Clean exit!")
                break
            
            elif choice == "1":
                await automation.gainsight_hybrid_automation(1)
            
            elif choice == "2": 
                await automation.gainsight_hybrid_automation(3)
            
            elif choice == "3":
                await automation.record_hybrid_workflow()
            
            elif choice == "4":
                script_path = Path("data/hybrid_gainsight_automation.py")
                if script_path.exists():
                    print(f"🧪 Execute: python3 {script_path}")
                    
                    # Option to run it directly
                    run_now = input("Run now? (y/N): ").strip().lower()
                    if run_now == 'y':
                        import subprocess
                        subprocess.run([sys.executable, str(script_path)])
                else:
                    print("❌ Script not found. Use option 3 to record first.")
            
            elif choice == "5":
                print("📊 SYSTEM STATUS")
                print(f"🧠 LLM Brain: {'ACTIVE' if brain_active else 'OFFLINE'}")
                print(f"📁 Data dir: {Path('data').absolute()}")
                print(f"📝 Script: {'EXISTS' if Path('data/hybrid_gainsight_automation.py').exists() else 'NOT FOUND'}")
                print(f"🎯 Ready for automation: {'YES' if brain_active else 'YES (manual mode)'}")
            
            else:
                print("❌ Invalid choice")
            
            if choice != "0":
                input("\nPress Enter...")
    
    except KeyboardInterrupt:
        print("\n👋 Interrupted - clean exit!")
    
    finally:
        # Always cleanup LLM brain
        await automation.cleanup_llm_brain()

if __name__ == "__main__":
    asyncio.run(main())

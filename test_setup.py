#!/usr/bin/env python3
"""
Test script to verify the clean setup is working
"""
import asyncio
import sys
from pathlib import Path

def test_imports():
    """Test that all required modules can be imported"""
    print("🧪 Testing imports...")
    
    try:
        from config import config
        print("✅ Config imported successfully")
    except ImportError as e:
        print(f"❌ Config import failed: {e}")
        return False
    
    try:
        from gemini_client import GeminiClient
        print("✅ Gemini client imported successfully")
    except ImportError as e:
        print(f"❌ Gemini client import failed: {e}")
        return False
    
    return True

def test_config():
    """Test configuration"""
    print("\n🔧 Testing configuration...")
    
    try:
        from config import config
        
        # Check API key
        if config.llm.google_api_key:
            print(f"✅ Google API key configured: {config.llm.google_api_key[:10]}...")
        else:
            print("❌ Google API key not configured")
            return False
        
        # Check models
        print(f"✅ Primary model: {config.llm.primary_model}")
        print(f"✅ Fallback models: {len(config.llm.fallback_models)} available")
        
        # Validate config
        config.validate()
        print("✅ Configuration validation passed")
        
        return True
        
    except Exception as e:
        print(f"❌ Configuration test failed: {e}")
        return False

async def test_gemini_api():
    """Test Gemini API connection"""
    print("\n🤖 Testing Gemini API...")
    
    try:
        from gemini_client import GeminiClient
        
        async with GeminiClient() as client:
            response = await client.complete(
                "Say 'Hello from Gemini!' if you can see this message.",
                task_type="general",
                max_tokens=50
            )
            
            if response.success:
                print(f"✅ API call successful!")
                print(f"🤖 Model: {response.model}")
                print(f"🏢 Provider: {response.provider}")
                print(f"⏱️  Latency: {response.latency:.2f}s")
                print(f"📝 Response: {response.content}")
                return True
            else:
                print(f"❌ API call failed: {response.error}")
                return False
                
    except Exception as e:
        print(f"❌ Gemini API test failed: {e}")
        return False

async def test_different_task_types():
    """Test different task types"""
    print("\n🎯 Testing different task types...")
    
    try:
        from gemini_client import GeminiClient
        
        tasks = [
            ("general", "What is 2+2?"),
            ("coding", "Write a simple Python hello world function"),
            ("reasoning", "Explain the benefits of browser automation"),
        ]
        
        async with GeminiClient() as client:
            for task_type, prompt in tasks:
                print(f"\n🔍 Testing {task_type} task...")
                
                response = await client.complete(
                    prompt,
                    task_type=task_type,
                    max_tokens=200
                )
                
                if response.success:
                    print(f"✅ {task_type.title()} task successful")
                    print(f"🤖 Model: {response.model}")
                    print(f"📝 Response preview: {response.content[:100]}...")
                else:
                    print(f"❌ {task_type.title()} task failed: {response.error}")
                    return False
        
        return True
        
    except Exception as e:
        print(f"❌ Task type testing failed: {e}")
        return False

def test_file_structure():
    """Test that required files exist"""
    print("\n📁 Testing file structure...")
    
    required_files = [
        "config.py",
        "gemini_client.py",
        "simple_main.py",
        "requirements.txt"
    ]
    
    missing_files = []
    for file in required_files:
        if Path(file).exists():
            print(f"✅ {file} exists")
        else:
            print(f"❌ {file} missing")
            missing_files.append(file)
    
    # Check data directories
    data_dirs = [
        "data",
        "logs",
        "data/screenshots",
        "data/recordings"
    ]
    
    for dir_path in data_dirs:
        if Path(dir_path).exists():
            print(f"✅ {dir_path}/ directory exists")
        else:
            print(f"⚠️  {dir_path}/ directory missing (will be created)")
    
    return len(missing_files) == 0

async def run_comprehensive_test():
    """Run all tests"""
    print("🚀 COMPREHENSIVE SETUP TEST")
    print("=" * 50)
    
    tests = [
        ("File Structure", test_file_structure),
        ("Imports", test_imports),
        ("Configuration", test_config),
        ("Gemini API", test_gemini_api),
        ("Task Types", test_different_task_types),
    ]
    
    results = {}
    
    for test_name, test_func in tests:
        print(f"\n{'='*20} {test_name} {'='*20}")
        
        try:
            if asyncio.iscoroutinefunction(test_func):
                result = await test_func()
            else:
                result = test_func()
            
            results[test_name] = result
            
        except Exception as e:
            print(f"❌ {test_name} test crashed: {e}")
            results[test_name] = False
    
    # Summary
    print("\n" + "="*50)
    print("📊 TEST SUMMARY")
    print("="*50)
    
    passed = 0
    total = len(results)
    
    for test_name, result in results.items():
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"{status} {test_name}")
        if result:
            passed += 1
    
    print(f"\n🎯 Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed! Your setup is ready to use.")
        print("\n🚀 To get started, run:")
        print("   python simple_main.py")
        return True
    else:
        print("❌ Some tests failed. Please check the errors above.")
        return False

async def main():
    """Main test function"""
    try:
        success = await run_comprehensive_test()
        return 0 if success else 1
    except KeyboardInterrupt:
        print("\n👋 Test interrupted")
        return 1
    except Exception as e:
        print(f"❌ Test suite crashed: {e}")
        return 1

if __name__ == "__main__":
    exit_code = asyncio.run(main())
    sys.exit(exit_code)

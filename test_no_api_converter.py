#!/usr/bin/env python3
"""
Test the No-API ICICI to Gainsight Converter
Shows how meeting types are mapped without using external APIs
"""

import json
from pathlib import Path
from icici_to_gainsight_no_api import ICICIToGainsightNoAPIConverter

def test_meeting_type_mapping():
    """Test the meeting type mapping functionality"""
    print("🧪 TESTING MEETING TYPE MAPPING")
    print("=" * 50)
    
    converter = ICICIToGainsightNoAPIConverter()
    
    # Test various meeting type mappings
    test_cases = [
        # Standard Gainsight types
        ("Email", "Email"),
        ("Telephone Call", "Call"),
        ("Web Meeting", "Meeting"),
        ("Internal Note", "Update"),
        
        # Custom Gainsight types
        ("In-Person Meeting", "In-Person Meeting"),
        ("Gong Call", "Gong Call"),
        ("Feedback", "Feedback"),
        ("Inbound", "Inbound"),
        ("Slack", "Slack"),
        
        # Case variations
        ("EMAIL", "Email"),
        ("email", "Email"),
        ("web meeting", "Meeting"),
        ("INTERNAL_NOTE", "Update"),
        
        # Partial matches
        ("Email Campaign", "Email"),
        ("Phone Call", "Call"),
        ("Video Conference", "Meeting"),
        ("Chat Message", "Slack"),
        ("Customer Feedback", "Feedback"),
        
        # Unknown types (should default to Update)
        ("Unknown Type", "Update"),
        ("", "Update"),
        ("Random Activity", "Update")
    ]
    
    print("📋 Mapping Test Results:")
    for input_type, expected_output in test_cases:
        actual_output = converter.map_totango_to_gainsight_type(input_type)
        status = "✅" if actual_output == expected_output else "❌"
        print(f"   {status} '{input_type}' → '{actual_output}' (expected: '{expected_output}')")
    
    return True

def test_sample_conversion():
    """Test conversion with sample ICICI data"""
    print("\n🔄 TESTING SAMPLE CONVERSION")
    print("=" * 50)
    
    converter = ICICIToGainsightNoAPIConverter()
    
    # Create sample ICICI activities with different types
    sample_activities = [
        {
            "id": "test_001",
            "timestamp": *************,
            "type": "automated_attribute_change",
            "properties": {
                "activity_type_id": "onboarding_101",
                "display_name": "Account Status",
                "new_value": "Active",
                "prev_value": "Pending"
            }
        },
        {
            "id": "test_002",
            "timestamp": *************,
            "type": "campaign_touch",
            "properties": {
                "activity_type_id": "adoption",
                "name": "Welcome Email Campaign",
                "description": "Welcome email sent to new customer"
            }
        },
        {
            "id": "test_003",
            "timestamp": *************,
            "type": "webhook",
            "properties": {
                "activity_type_id": "adoption",
                "name": "Zapier Integration",
                "status": "success",
                "json_content": '{"type": "email", "action": "sent"}'
            }
        },
        {
            "id": "test_004",
            "timestamp": *************,
            "type": "account_alert",
            "properties": {
                "activity_type_id": "adoption",
                "title": "High Risk Alert",
                "description": "Account flagged for review"
            }
        }
    ]
    
    print("📥 Converting sample activities...")
    
    converted_activities = []
    for i, activity in enumerate(sample_activities):
        print(f"\n🔄 Activity {i+1}: {activity['type']}")
        
        converted = converter.convert_activity(activity)
        if converted:
            converted_activities.append(converted)
            
            # Show key mapping results
            meeting_type = converted['note']['meeting_type_name']
            original_type = converted['meta']['originalTotangoType']
            mapped_type = converted['meta']['mappedGainsightType']
            
            print(f"   📝 Subject: {converted['note']['subject']}")
            print(f"   🎯 Original Type: {original_type}")
            print(f"   🔄 Mapped Type: {mapped_type}")
            print(f"   📋 Meeting Type: {meeting_type}")
            print(f"   ✅ Success!")
        else:
            print(f"   ❌ Conversion failed")
    
    # Save sample output
    if converted_activities:
        output_file = "data/sample_no_api_conversion.json"
        with open(output_file, 'w') as f:
            json.dump(converted_activities, f, indent=2)
        print(f"\n💾 Sample output saved to: {output_file}")
    
    return len(converted_activities) == len(sample_activities)

def analyze_real_data():
    """Analyze real ICICI data to show mapping results"""
    print("\n🔍 ANALYZING REAL ICICI DATA")
    print("=" * 50)
    
    converter = ICICIToGainsightNoAPIConverter()
    input_file = "data/totango/ICICI.json"
    
    if not Path(input_file).exists():
        print(f"❌ Real data file not found: {input_file}")
        return False
    
    # Analyze sample activities
    analysis = converter.analyze_sample_activities(input_file, 20)
    
    if analysis:
        print(f"📊 Analysis Results:")
        print(f"   • Total activities in file: {analysis['total_activities']}")
        print(f"   • Sample size analyzed: {analysis['sample_size']}")
        
        print(f"\n📋 Activity Types Found:")
        for act_type, count in analysis["activity_types"].items():
            print(f"   • {act_type}: {count}")
        
        print(f"\n🎯 Mapped Gainsight Types:")
        for gainsight_type, count in analysis["meeting_types_found"].items():
            print(f"   • {gainsight_type}: {count}")
        
        print(f"\n🔍 Sample Activity Mappings:")
        for i, sample in enumerate(analysis["sample_activities"][:10]):
            print(f"   {i+1}. {sample['type']} → {sample['mapped_gainsight_type']}")
            print(f"      Original: {sample['original_meeting_type']}")
        
        return True
    
    return False

def show_mapping_rules():
    """Show the mapping rules being used"""
    print("\n📋 MAPPING RULES")
    print("=" * 50)
    
    converter = ICICIToGainsightNoAPIConverter()
    
    print("🎯 Totango → Gainsight Activity Type Mapping:")
    print("\n📌 Default Gainsight Types:")
    default_mappings = [
        ("Email", "Email"),
        ("Telephone Call", "Call"),
        ("Web Meeting", "Meeting"),
        ("Internal Note", "Update")
    ]
    
    for totango, gainsight in default_mappings:
        print(f"   • {totango} → {gainsight}")
    
    print("\n📌 Custom Gainsight Types:")
    custom_mappings = [
        ("In-Person Meeting", "In-Person Meeting"),
        ("Gong Call", "Gong Call"),
        ("Feedback", "Feedback"),
        ("Inbound", "Inbound"),
        ("Slack", "Slack")
    ]
    
    for totango, gainsight in custom_mappings:
        print(f"   • {totango} → {gainsight}")
    
    print("\n📌 Pattern Matching Rules:")
    pattern_rules = [
        ("Contains 'email' or 'mail'", "Email"),
        ("Contains 'call' or 'phone' or 'telephone'", "Call"),
        ("Contains 'meeting' or 'conference' or 'video'", "Meeting"),
        ("Contains 'slack' or 'chat' or 'message'", "Slack"),
        ("Contains 'feedback' or 'survey'", "Feedback"),
        ("Contains 'inbound'", "Inbound"),
        ("Contains 'gong'", "Gong Call"),
        ("Contains 'person' and 'meeting'", "In-Person Meeting"),
        ("Default fallback", "Update")
    ]
    
    for pattern, result in pattern_rules:
        print(f"   • {pattern} → {result}")

def main():
    """Main test function"""
    print("🧪 NO-API ICICI TO GAINSIGHT CONVERTER TEST")
    print("=" * 60)
    
    # Show mapping rules
    show_mapping_rules()
    
    # Test mapping functionality
    mapping_success = test_meeting_type_mapping()
    
    # Test sample conversion
    conversion_success = test_sample_conversion()
    
    # Analyze real data
    analysis_success = analyze_real_data()
    
    # Summary
    print(f"\n🎯 TEST SUMMARY:")
    print("=" * 30)
    
    if mapping_success:
        print("✅ Meeting type mapping: WORKING")
    else:
        print("❌ Meeting type mapping: FAILED")
    
    if conversion_success:
        print("✅ Sample conversion: WORKING")
    else:
        print("❌ Sample conversion: FAILED")
    
    if analysis_success:
        print("✅ Real data analysis: WORKING")
    else:
        print("⚠️  Real data analysis: NO DATA FILE")
    
    print(f"\n🚀 NEXT STEPS:")
    if mapping_success and conversion_success:
        print("   1. Run full conversion: python icici_to_gainsight_no_api.py")
        print("   2. Check output in data/icici_gainsight_converted_no_api.json")
        print("   3. Verify meeting_type_name fields are properly mapped")
    else:
        print("   1. Fix any issues shown above")
        print("   2. Re-run this test")
    
    print("\n🎉 Test completed!")

if __name__ == "__main__":
    main()

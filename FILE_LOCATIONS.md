# 📁 File Locations Guide

## 🚀 **Commands to Run Everything**

### **Option 1: Single Command (Recommended)**
```bash
python3 run_complete_migration.py
```

### **Option 2: Step-by-Step (If you want to see each step)**
```bash
python3 run_step_by_step.py
```

Both scripts run the entire migration and show you exactly where all files are saved.

## 📊 **Generated Files and Locations**

### **Main Output Files (Ready for Gainsight)**

| File | Location | Purpose |
|------|----------|---------|
| **Demo CSV** | `ICICI_processed_gainsight_mapped_enhanced_demo.csv` | **For testing** - Uses <PERSON> as author |
| **Real CSV** | `ICICI_processed_gainsight_mapped_enhanced_real.csv` | **For production** - Uses real ICICI users |

### **Intermediate Files**

| File | Location | Purpose |
|------|----------|---------|
| Mapped JSON | `/Users/<USER>/Desktop/totango/ICICI_processed_gainsight_mapped.json` | Activities with Gainsight types |
| Basic CSV | `/Users/<USER>/Desktop/totango/ICICI_processed_gainsight_import.csv` | Basic CSV backup |

### **Source Files (Required)**

| File | Location | Purpose |
|------|----------|---------|
| ICICI Activities | `/Users/<USER>/Desktop/totango/ICICI_processed.json` | Source activity data |
| Flow Types | `/Users/<USER>/Desktop/totango/flowtype.json` | Flow type mappings |
| Touchpoint Reasons | `/Users/<USER>/Desktop/totango/Touchpoint_reason.JSON` | Touchpoint mappings |

## 🎯 **What Each File Contains**

### **Demo CSV** (Testing)
- **322 rows** with sequential numbering (1-322)
- **Ram Prasad** as author for all activities
- **Perfect for testing** Gainsight import process
- **All enhancements**: Flow types, touchpoint reasons, etc.

### **Real CSV** (Production)
- **322 rows** with sequential numbering (1-322)
- **Real ICICI users** as authors (ICICI Bank, ICICI User, Aggarwal Muskan, etc.)
- **For actual migration** to Gainsight
- **All enhancements**: Flow types, touchpoint reasons, etc.

## 📋 **CSV Structure (Both Files)**

| Column | Description | Example |
|--------|-------------|---------|
| Row Number | Sequential 1-322 | 1, 2, 3... |
| Subject | Activity title | ICICI: Mend Platform Access |
| Activity Date | Formatted timestamp | 2025-01-25 10:30:16 |
| **Activity Type** | **Gainsight activity type** | **Update, Meeting, Email** |
| Content (HTML) | Rich description | `<p>Automated update...</p>` |
| Plain Text | Plain description | Automated update... |
| Author Name | Activity author | Ram Prasad / ICICI Bank |
| Author Email | Author email | <EMAIL> |
| **Flow Type** | **Real flow type** | **Adoption, Onboarding, Risk** |
| **Touchpoint Reason** | **Real touchpoint** | **CADENCE, FEE, Internal Note** |
| Internal Attendees | Real attendees | Ram Prasad <email> |
| External Attendees | External attendees | (empty) |
| Company | Company context | ICICI |

## 🚀 **How to Use**

### **Step 1: Run the Master Script**
```bash
python run_complete_migration.py
```

### **Step 2: Choose Your CSV**
- **For Testing**: Use `ICICI_processed_gainsight_mapped_enhanced_demo.csv`
- **For Production**: Use `ICICI_processed_gainsight_mapped_enhanced_real.csv`

### **Step 3: Import to Gainsight**
1. Upload the CSV to Gainsight Timeline
2. Map the columns to Gainsight fields
3. Verify all 322 activities import successfully

## 📊 **File Sizes (Approximate)**

| File | Size | Rows |
|------|------|------|
| Demo CSV | ~150 KB | 322 |
| Real CSV | ~150 KB | 322 |
| Mapped JSON | ~700 KB | 322 |
| Basic CSV | ~120 KB | 322 |

## 🔍 **Verification Commands**

### **Check if files exist:**
```bash
ls -la *.csv
ls -la /Users/<USER>/Desktop/totango/*.json
```

### **Verify CSV content:**
```bash
python verify_enhanced_csv.py
```

### **Test individual components:**
```bash
# Test flow type mapping
python test_flow_type_mapping.py

# Test complete flow
python quick_test_complete_flow.py
```

## 🎯 **Success Metrics**

When the migration is complete, you should see:

✅ **322 activities** in each CSV file
✅ **Sequential numbering** (1 to 322)
✅ **9 unique flow types** (Adoption, Onboarding, etc.)
✅ **5 unique touchpoint reasons** (CADENCE, FEE, etc.)
✅ **Proper activity types** (Update 91%, Meeting 7.8%, Email 1.2%)
✅ **Real author data** in production CSV
✅ **Demo author data** (Ram Prasad) in testing CSV

## 💡 **Quick Tips**

- **Always use the master script** `run_complete_migration.py` for the complete process
- **Demo CSV** is perfect for testing - uses Ram Prasad consistently
- **Real CSV** is for production - uses actual ICICI user data
- **All files save to current directory** except intermediate JSON files
- **No manual steps required** - everything is automated

**Your ICICI to Gainsight migration files are ready!** 🎉

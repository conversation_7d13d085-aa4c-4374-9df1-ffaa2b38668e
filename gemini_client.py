#!/usr/bin/env python3
"""
Clean Gemini API Client
Simple, reliable client for Google Gemini API with fallback support
"""

import asyncio
import httpx
import json
import time
import logging
from typing import Dict, Any, Optional
from dataclasses import dataclass

from config import config

logger = logging.getLogger(__name__)

@dataclass
class GeminiResponse:
    """Response from Gemini API"""
    content: str
    model: str
    provider: str
    success: bool
    latency: float
    usage: Dict[str, Any] = None
    error: Optional[str] = None

class GeminiClient:
    """
    Clean Gemini API client with fallback support
    """
    
    def __init__(self):
        self.config = config
        self.session = httpx.AsyncClient(timeout=120.0)
        
    async def __aenter__(self):
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        await self.session.aclose()
    
    async def complete(self, 
                      prompt: str, 
                      task_type: str = "general",
                      max_tokens: int = 4000,
                      temperature: float = 0.1,
                      **kwargs) -> GeminiResponse:
        """
        Complete a prompt using Gemini API
        """
        start_time = time.time()
        
        # Get best model for task
        model = self._get_model_for_task(task_type)
        
        try:
            logger.info(f"Attempting completion with Gemini model: {model}")
            
            # Try Gemini first
            if "gemini" in model.lower():
                result = await self._call_gemini(model, prompt, max_tokens, temperature, **kwargs)
                latency = time.time() - start_time
                
                return GeminiResponse(
                    content=result["content"],
                    model=model,
                    provider="google",
                    success=True,
                    latency=latency,
                    usage=result.get("usage", {})
                )
            
            # Fallback to OpenRouter if not Gemini
            else:
                result = await self._call_openrouter(model, prompt, max_tokens, temperature, **kwargs)
                latency = time.time() - start_time
                
                return GeminiResponse(
                    content=result["content"],
                    model=model,
                    provider="openrouter",
                    success=True,
                    latency=latency,
                    usage=result.get("usage", {})
                )
                
        except Exception as e:
            latency = time.time() - start_time
            logger.error(f"Error with model {model}: {e}")
            
            # Try fallback models
            for fallback_model in self.config.llm.fallback_models:
                if fallback_model == model:
                    continue
                    
                try:
                    logger.info(f"Trying fallback model: {fallback_model}")
                    
                    if "gemini" in fallback_model.lower():
                        result = await self._call_gemini(fallback_model, prompt, max_tokens, temperature, **kwargs)
                        provider = "google"
                    else:
                        result = await self._call_openrouter(fallback_model, prompt, max_tokens, temperature, **kwargs)
                        provider = "openrouter"
                    
                    latency = time.time() - start_time
                    return GeminiResponse(
                        content=result["content"],
                        model=fallback_model,
                        provider=provider,
                        success=True,
                        latency=latency,
                        usage=result.get("usage", {})
                    )
                    
                except Exception as fallback_error:
                    logger.error(f"Fallback model {fallback_model} failed: {fallback_error}")
                    continue
            
            # All models failed
            return GeminiResponse(
                content="",
                model=model,
                provider="unknown",
                success=False,
                latency=latency,
                error=str(e)
            )
    
    async def _call_gemini(self, model: str, prompt: str, max_tokens: int,
                          temperature: float, **kwargs) -> Dict[str, Any]:
        """Call Google Gemini API"""
        headers = {
            "Content-Type": "application/json"
        }
        
        # Clean model name for Google API
        clean_model = model.replace("google/", "").replace(":free", "")
        
        payload = {
            "contents": [{"parts": [{"text": prompt}]}],
            "generationConfig": {
                "maxOutputTokens": max_tokens,
                "temperature": temperature,
                **kwargs
            }
        }
        
        # Use the v1beta API for Gemini models
        api_url = f"https://generativelanguage.googleapis.com/v1beta/models/{clean_model}:generateContent"
        
        response = await self.session.post(
            f"{api_url}?key={self.config.llm.google_api_key}",
            headers=headers,
            json=payload
        )
        
        if response.status_code != 200:
            raise Exception(f"Gemini API error: {response.status_code} - {response.text}")
        
        data = response.json()
        
        # Extract content from Gemini response
        if "candidates" in data and len(data["candidates"]) > 0:
            candidate = data["candidates"][0]
            if "content" in candidate and "parts" in candidate["content"]:
                content = candidate["content"]["parts"][0]["text"]
            else:
                content = str(candidate)
        else:
            raise Exception(f"Unexpected Gemini response format: {data}")
        
        return {
            "content": content,
            "usage": data.get("usageMetadata", {})
        }
    
    async def _call_openrouter(self, model: str, prompt: str, max_tokens: int, 
                              temperature: float, **kwargs) -> Dict[str, Any]:
        """Call OpenRouter API (for fallback models)"""
        headers = {
            "Authorization": f"Bearer {self.config.llm.openrouter_api_key}",
            "Content-Type": "application/json",
            "HTTP-Referer": "https://github.com/wildweasel/browser-automation",
            "X-Title": "Browser Automation Agent"
        }
        
        payload = {
            "model": model,
            "messages": [{"role": "user", "content": prompt}],
            "max_tokens": max_tokens,
            "temperature": temperature,
            **kwargs
        }
        
        response = await self.session.post(
            f"{self.config.llm.openrouter_base_url}/chat/completions",
            headers=headers,
            json=payload
        )
        
        if response.status_code != 200:
            raise Exception(f"OpenRouter API error: {response.status_code} - {response.text}")
        
        data = response.json()
        return {
            "content": data["choices"][0]["message"]["content"],
            "usage": data.get("usage", {})
        }
    
    def _get_model_for_task(self, task_type: str) -> str:
        """Get the best model for a specific task type"""
        task_models = {
            "coding": self.config.llm.coding_model,
            "reasoning": self.config.llm.reasoning_model,
            "natural_language": self.config.llm.natural_language_model,
            "multimodal": self.config.llm.multimodal_model,
            "general": self.config.llm.general_model,
            "browser_automation": self.config.llm.reasoning_model,
        }
        
        return task_models.get(task_type, self.config.llm.primary_model)

# Create a global instance for easy import
gemini_client = GeminiClient()

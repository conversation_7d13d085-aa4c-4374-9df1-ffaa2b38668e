#!/usr/bin/env python3
"""
🚀 SIMPLE BROWSER AUTOMATION AGENT
Clean, working browser automation with Gemini API
"""
import asyncio
import argparse
import sys
from pathlib import Path

from config import config
from gemini_client import GeminiClient

def print_banner():
    """Print application banner"""
    banner = """
╔══════════════════════════════════════════════════════════════════════════════╗
║           🚀 SIMPLE BROWSER AUTOMATION AGENT 🧠                             ║
║                                                                              ║
║  🎯 Features:                                                                ║
║  • 🤖 Google Gemini API Integration                                         ║
║  • 🌐 Browser Automation (Playwright/Browser-Use)                           ║
║  • 🧠 Intelligent Task Processing                                           ║
║  • 🔄 Error Recovery and Fallback Models                                    ║
║  • 📝 Session Recording and Learning                                        ║
╚══════════════════════════════════════════════════════════════════════════════╝
    """
    print(banner)

async def test_gemini_api():
    """Test Gemini API connection"""
    print("\n🧪 Testing Gemini API...")
    
    if not config.llm.google_api_key:
        print("❌ No Google API key found in config")
        return False
    
    print(f"✅ Google API key: {config.llm.google_api_key[:10]}...")
    
    async with GeminiClient() as client:
        try:
            response = await client.complete(
                "Hello! Please respond with 'Gemini API is working!' if you can see this.",
                task_type="general",
                max_tokens=100
            )
            
            print(f"Model: {response.model}")
            print(f"Provider: {response.provider}")
            print(f"Success: {response.success}")
            print(f"Latency: {response.latency:.2f}s")
            
            if response.success:
                print(f"✅ Response: {response.content}")
                print("🎉 Gemini API is working!")
                return True
            else:
                print(f"❌ Error: {response.error}")
                return False
                
        except Exception as e:
            print(f"❌ Exception during test: {e}")
            return False

async def execute_simple_task(task_description: str):
    """Execute a simple task using Gemini"""
    print(f"\n🚀 Executing task: {task_description}")
    
    async with GeminiClient() as client:
        try:
            # Determine task type
            task_type = "general"
            if any(word in task_description.lower() for word in ["code", "program", "script", "function"]):
                task_type = "coding"
            elif any(word in task_description.lower() for word in ["analyze", "think", "reason", "explain"]):
                task_type = "reasoning"
            elif any(word in task_description.lower() for word in ["browser", "website", "navigate", "click"]):
                task_type = "browser_automation"
            
            response = await client.complete(
                task_description,
                task_type=task_type,
                max_tokens=2000
            )
            
            if response.success:
                print(f"✅ Task completed successfully!")
                print(f"🤖 Model: {response.model}")
                print(f"⏱️  Time: {response.latency:.2f}s")
                print(f"📝 Response:\n{response.content}")
                return True
            else:
                print(f"❌ Task failed: {response.error}")
                return False
                
        except Exception as e:
            print(f"❌ Exception during task execution: {e}")
            return False

async def interactive_mode():
    """Simple interactive mode"""
    print("\n🤖 Simple Browser Automation Agent")
    print("Type your task, 'test' to test API, 'help' for commands, 'quit' to exit\n")
    
    while True:
        try:
            user_input = input("🚀 Agent> ").strip()
            
            if not user_input:
                continue
            
            if user_input.lower() in ['quit', 'exit', 'q']:
                print("👋 Goodbye!")
                break
            
            if user_input.lower() == 'help':
                print_help()
                continue
            
            if user_input.lower() == 'test':
                await test_gemini_api()
                continue
            
            if user_input.lower() == 'status':
                await show_status()
                continue
            
            # Execute the task
            await execute_simple_task(user_input)
            
        except KeyboardInterrupt:
            print("\n👋 Goodbye!")
            break
        except Exception as e:
            print(f"❌ Error: {e}")

def print_help():
    """Print help information"""
    help_text = """
🚀 SIMPLE BROWSER AUTOMATION COMMANDS
═══════════════════════════════════════════════════════════════════

🎯 BASIC COMMANDS:
  help                  - Show this help
  test                  - Test Gemini API connection
  status                - Show system status
  quit/exit/q          - Exit application

📝 TASK EXAMPLES:
  "Write a Python function to calculate fibonacci numbers"
  "Explain how browser automation works"
  "Create a plan for web scraping a website"
  "Help me automate form filling"

💡 The system uses Google Gemini API for intelligent responses!
    """
    print(help_text)

async def show_status():
    """Show system status"""
    print("\n📊 SYSTEM STATUS")
    print("=" * 40)
    
    # Check API key
    if config.llm.google_api_key:
        print(f"✅ Google API Key: {config.llm.google_api_key[:10]}...")
    else:
        print("❌ Google API Key: Not configured")
    
    # Check models
    print(f"🤖 Primary Model: {config.llm.primary_model}")
    print(f"🔄 Fallback Models: {len(config.llm.fallback_models)} available")
    
    # Test API connection
    print("\n🧪 Testing API connection...")
    api_working = await test_gemini_api()
    
    if api_working:
        print("✅ System is ready!")
    else:
        print("❌ System has issues - check API key")

async def main():
    """Main entry point"""
    parser = argparse.ArgumentParser(
        description="Simple Browser Automation Agent with Gemini API",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
🚀 EXAMPLES:
  %(prog)s                                    # Interactive mode
  %(prog)s -t "Write a Python function"       # Execute single task
  %(prog)s --test                            # Test API connection
  %(prog)s --status                          # Show system status
        """
    )
    
    parser.add_argument('-t', '--task', 
                       help='Execute a single task')
    parser.add_argument('--test', action='store_true',
                       help='Test Gemini API connection')
    parser.add_argument('--status', action='store_true',
                       help='Show system status')
    parser.add_argument('-v', '--verbose', action='store_true',
                       help='Enable verbose logging')
    
    args = parser.parse_args()
    
    # Set logging level
    if args.verbose:
        import logging
        logging.getLogger().setLevel(logging.DEBUG)
    
    print_banner()
    
    try:
        # Validate configuration
        config.validate()
        print("✅ Configuration validated")
    except ValueError as e:
        print(f"❌ Configuration error: {e}")
        return 1
    
    # Handle command line arguments
    if args.test:
        success = await test_gemini_api()
        return 0 if success else 1
    
    if args.status:
        await show_status()
        return 0
    
    if args.task:
        success = await execute_simple_task(args.task)
        return 0 if success else 1
    
    # Default to interactive mode
    await interactive_mode()
    return 0

if __name__ == "__main__":
    try:
        exit_code = asyncio.run(main())
        sys.exit(exit_code)
    except KeyboardInterrupt:
        print("\n👋 Goodbye!")
        sys.exit(0)
    except Exception as e:
        print(f"❌ Fatal error: {e}")
        sys.exit(1)

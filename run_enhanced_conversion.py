#!/usr/bin/env python3
"""
Run Enhanced ICICI to Gainsight Conversion
This script runs the enhanced converter that maps meeting types to Gainsight activity types
"""

import sys
import os
from pathlib import Path

# Add the current directory to Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from icici_to_gainsight_enhanced import ICICIToGainsightConverter

def check_prerequisites():
    """Check if all required files exist"""
    print("🔍 CHECKING PREREQUISITES")
    print("=" * 40)
    
    required_files = [
        "/Users/<USER>/Desktop/totango/ICICI.json",
        "/Users/<USER>/Desktop/totango/ID.json", 
        "/Users/<USER>/Desktop/totango/Touchpoint_reason.JSON"
    ]
    
    all_files_exist = True
    
    for file_path in required_files:
        if Path(file_path).exists():
            print(f"✅ Found: {os.path.basename(file_path)}")
        else:
            print(f"❌ Missing: {file_path}")
            all_files_exist = False
    
    return all_files_exist

def show_enhancement_details():
    """Show what enhancements were made"""
    print("\n🚀 ENHANCEMENT DETAILS")
    print("=" * 40)
    
    print("📋 Original converter functionality:")
    print("   • Maps meeting_type IDs to display names")
    print("   • Maps touchpoint_tags IDs to display names")
    print("   • Handles unmapped IDs gracefully")
    
    print("\n🎯 NEW: Enhanced converter functionality:")
    print("   • All original functionality PLUS:")
    print("   • Maps Totango meeting types → Gainsight activity types")
    print("   • Adds 'gainsight_activity_type' field to each activity")
    print("   • Provides detailed mapping statistics")
    print("   • Ready for direct Gainsight migration")
    
    print("\n🔄 Totango → Gainsight Mapping Rules:")
    mappings = [
        ("Email", "Email"),
        ("Telephone Call", "Call"),
        ("Web Meeting", "Meeting"), 
        ("Internal Note", "Update"),
        ("In-Person Meeting", "In-Person Meeting"),
        ("Gong Call", "Gong Call"),
        ("Feedback", "Feedback"),
        ("Inbound", "Inbound"),
        ("Slack", "Slack")
    ]
    
    for totango, gainsight in mappings:
        print(f"   • {totango} → {gainsight}")
    
    print("\n📊 Pattern Matching:")
    print("   • Contains 'email' → Email")
    print("   • Contains 'call'/'phone' → Call")
    print("   • Contains 'meeting'/'video' → Meeting")
    print("   • Contains 'slack'/'chat' → Slack")
    print("   • Contains 'feedback' → Feedback")
    print("   • Contains 'inbound' → Inbound")
    print("   • Default fallback → Update")

def run_conversion():
    """Run the enhanced conversion"""
    print("\n🔄 RUNNING ENHANCED CONVERSION")
    print("=" * 40)
    
    try:
        # Initialize the enhanced converter
        converter = ICICIToGainsightConverter()
        
        # Run the conversion
        converter.run_conversion()
        
        return True
        
    except Exception as e:
        print(f"❌ Conversion failed: {e}")
        return False

def show_output_comparison():
    """Show comparison between original and enhanced output"""
    print("\n📊 OUTPUT COMPARISON")
    print("=" * 40)
    
    print("📄 Original output structure:")
    original_example = {
        "properties": {
            "meeting_type": "1I00ABC123",
            "meeting_type_id": "1I00ABC123",
            "meeting_type_name": "Email",
            "touchpoint_tags_ids": ["1I00XYZ789"],
            "touchpoint_tags_names": ["Customer Onboarding"]
        }
    }
    
    print("   {")
    print("     'properties': {")
    print("       'meeting_type': '1I00ABC123',")
    print("       'meeting_type_id': '1I00ABC123',")
    print("       'meeting_type_name': 'Email',")
    print("       'touchpoint_tags_ids': ['1I00XYZ789'],")
    print("       'touchpoint_tags_names': ['Customer Onboarding']")
    print("     }")
    print("   }")
    
    print("\n📄 Enhanced output structure:")
    print("   {")
    print("     'properties': {")
    print("       'meeting_type': '1I00ABC123',")
    print("       'meeting_type_id': '1I00ABC123',")
    print("       'meeting_type_name': 'Email',")
    print("       'gainsight_activity_type': 'Email',  ← NEW!")
    print("       'touchpoint_tags_ids': ['1I00XYZ789'],")
    print("       'touchpoint_tags_names': ['Customer Onboarding']")
    print("     }")
    print("   }")
    
    print("\n🎯 Key Enhancement:")
    print("   • 'gainsight_activity_type' field added")
    print("   • Ready for direct Gainsight import")
    print("   • No manual mapping required")

def main():
    """Main function"""
    print("🚀 ENHANCED ICICI TO GAINSIGHT CONVERTER")
    print("=" * 50)
    
    # Step 1: Check prerequisites
    if not check_prerequisites():
        print("\n❌ Missing required files. Please ensure all files are in place.")
        return 1
    
    # Step 2: Show enhancement details
    show_enhancement_details()
    
    # Step 3: Show output comparison
    show_output_comparison()
    
    # Step 4: Ask for confirmation
    print("\n❓ READY TO RUN CONVERSION?")
    print("=" * 30)
    print("This will:")
    print("   1. Load all ICICI activities")
    print("   2. Map meeting types to Gainsight activity types")
    print("   3. Generate comprehensive statistics")
    print("   4. Save enhanced data to ICICI_gainsight_ready.json")
    
    try:
        response = input("\nProceed with conversion? (y/n): ").strip().lower()
        
        if response in ['y', 'yes']:
            # Step 5: Run conversion
            success = run_conversion()
            
            if success:
                print("\n🎉 CONVERSION COMPLETED SUCCESSFULLY!")
                print("=" * 40)
                print("✅ Enhanced data saved to: ICICI_gainsight_ready.json")
                print("✅ All activities now have 'gainsight_activity_type' field")
                print("✅ Ready for Gainsight migration")
                
                print("\n📋 Next Steps:")
                print("   1. Review the generated summary report")
                print("   2. Check the output file: ICICI_gainsight_ready.json")
                print("   3. Verify gainsight_activity_type mappings")
                print("   4. Use the data for Gainsight migration")
                
                return 0
            else:
                print("\n❌ Conversion failed. Check error messages above.")
                return 1
        else:
            print("\n👋 Conversion cancelled by user.")
            return 0
            
    except KeyboardInterrupt:
        print("\n👋 Conversion cancelled by user.")
        return 0

if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)

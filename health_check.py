#!/usr/bin/env python3
"""
Quick System Health Check for Enhanced Browser Automation Agent
"""
import asyncio
import sys
import os
from pathlib import Path

# Add project root to path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

async def check_system_health():
    """Comprehensive system health check"""
    
    print("🏥 SYSTEM HEALTH CHECK")
    print("=" * 50)
    
    health_status = {
        "dependencies": False,
        "configuration": False,
        "memory_system": False,
        "browser_automation": False,
        "advanced_orchestrator": False,
        "overall": False
    }
    
    # Check 1: Dependencies
    print("\n🔍 Checking Dependencies...")
    try:
        import browser_use
        import letta_client  
        import langchain
        import playwright
        import faiss
        print("✅ Core dependencies available")
        health_status["dependencies"] = True
    except ImportError as e:
        print(f"❌ Missing dependencies: {e}")
    
    # Check 2: Configuration
    print("\n⚙️ Checking Configuration...")
    try:
        from config import config
        config.validate()
        print("✅ Configuration valid")
        health_status["configuration"] = True
    except Exception as e:
        print(f"❌ Configuration issues: {e}")
    
    # Check 3: Memory System
    print("\n🧠 Checking Memory System...")
    try:
        from memory_system.memory_manager import memory_manager
        success = await memory_manager.initialize()
        if success:
            print("✅ Memory system operational")
            health_status["memory_system"] = True
        else:
            print("⚠️ Memory system using fallback mode")
            health_status["memory_system"] = True  # Still functional
    except Exception as e:
        print(f"❌ Memory system error: {e}")
    
    # Check 4: Browser Automation
    print("\n🌐 Checking Browser Automation...")
    try:
        from browser_automation.automation_agent import browser_agent
        success = await browser_agent.initialize()
        if success:
            print("✅ Browser automation ready")
            health_status["browser_automation"] = True
        else:
            print("❌ Browser automation failed")
    except Exception as e:
        print(f"❌ Browser automation error: {e}")
    
    # Check 5: Advanced Orchestrator
    print("\n🚀 Checking Advanced Orchestrator...")
    try:
        from advanced_orchestrator import advanced_orchestrator
        success = await advanced_orchestrator.initialize()
        if success:
            print("✅ Advanced orchestrator ready")
            health_status["advanced_orchestrator"] = True
        else:
            print("❌ Advanced orchestrator failed")
    except Exception as e:
        print(f"❌ Advanced orchestrator error: {e}")
    
    # Overall Status
    print("\n" + "=" * 50)
    print("📊 HEALTH SUMMARY")
    print("=" * 50)
    
    for component, status in health_status.items():
        if component == "overall":
            continue
        status_icon = "✅" if status else "❌"
        print(f"{status_icon} {component.replace('_', ' ').title()}: {'PASS' if status else 'FAIL'}")
    
    # Calculate overall health
    component_count = len(health_status) - 1  # Exclude 'overall'
    passed_count = sum(1 for k, v in health_status.items() if k != "overall" and v)
    health_percentage = (passed_count / component_count) * 100
    
    health_status["overall"] = health_percentage >= 80
    
    if health_status["overall"]:
        print(f"\n🎉 SYSTEM HEALTHY ({health_percentage:.0f}% components passing)")
        print("Ready for production use!")
    elif health_percentage >= 60:
        print(f"\n⚠️ SYSTEM FUNCTIONAL ({health_percentage:.0f}% components passing)")
        print("Some advanced features may be limited")
    else:
        print(f"\n🚨 SYSTEM CRITICAL ({health_percentage:.0f}% components passing)")
        print("Significant issues detected - review logs")
    
    # Cleanup
    try:
        if 'memory_manager' in locals():
            await memory_manager.cleanup()
        if 'browser_agent' in locals():
            await browser_agent.cleanup()
        if 'advanced_orchestrator' in locals():
            await advanced_orchestrator.cleanup()
    except:
        pass
    
    return health_status

async def main():
    """Main entry point"""
    try:
        health_status = await check_system_health()
        
        # Exit with appropriate code
        if health_status["overall"]:
            sys.exit(0)  # Success
        else:
            sys.exit(1)  # Failure
            
    except KeyboardInterrupt:
        print("\n\n👋 Health check interrupted")
        sys.exit(1)
    except Exception as e:
        print(f"\n💥 Health check failed: {e}")
        sys.exit(1)

if __name__ == "__main__":
    asyncio.run(main())

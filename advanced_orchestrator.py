"""
Advanced Task Orchestrator with Enhanced Multi-Loop Execution and Context Management
"""
import asyncio
import logging
import json
from typing import Dict, List, Optional, Any, <PERSON><PERSON>
from datetime import datetime, timedelta
from dataclasses import dataclass, asdict
from enum import Enum
import hashlib

from memory_system.memory_manager import memory_manager
from browser_automation.automation_agent import browser_agent
from config import config

# Set up logging
logger = logging.getLogger(__name__)

class TaskPriority(Enum):
    LOW = 1
    NORMAL = 2
    HIGH = 3
    CRITICAL = 4

class TaskStatus(Enum):
    PENDING = "pending"
    RUNNING = "running"
    COMPLETED = "completed"
    FAILED = "failed"
    PAUSED = "paused"
    CANCELLED = "cancelled"

@dataclass
class TaskContext:
    """Enhanced context for task execution"""
    task_id: str
    parent_task_id: Optional[str] = None
    user_context: Dict[str, Any] = None
    memory_context: Dict[str, Any] = None
    browser_context: Dict[str, Any] = None
    dependencies: List[str] = None
    priority: TaskPriority = TaskPriority.NORMAL
    max_retries: int = 3
    retry_count: int = 0
    timeout_seconds: int = 300
    tags: List[str] = None
    metadata: Dict[str, Any] = None

@dataclass
class TaskResult:
    """Enhanced result structure for task execution"""
    task_id: str
    status: TaskStatus
    result: Any = None
    error: Optional[str] = None
    execution_time: float = 0.0
    steps_taken: int = 0
    memory_updates: List[Dict[str, Any]] = None
    context_evolution: Dict[str, Any] = None
    performance_metrics: Dict[str, Any] = None
    recommendations: List[str] = None

class AdvancedTaskOrchestrator:
    """
    Advanced orchestrator with enhanced multi-loop execution, context management,
    and intelligent task scheduling
    """
    
    def __init__(self):
        self.initialized = False
        self.task_queue = asyncio.PriorityQueue()
        self.active_tasks = {}
        self.completed_tasks = {}
        self.failed_tasks = {}
        self.task_contexts = {}
        self.global_context = {}
        self.session_metrics = {
            "tasks_executed": 0,
            "success_rate": 0.0,
            "average_execution_time": 0.0,
            "memory_efficiency": 0.0,
            "adaptation_rate": 0.0
        }
        
    async def initialize(self) -> bool:
        """Initialize the advanced orchestrator"""
        try:
            logger.info("Initializing Advanced Task Orchestrator...")
            
            # Initialize core components
            memory_success = await memory_manager.initialize()
            if not memory_success:
                logger.error("Failed to initialize memory system")
                return False
            
            browser_success = await browser_agent.initialize()
            if not browser_success:
                logger.error("Failed to initialize browser automation")
                return False
            
            # Initialize advanced features
            await self._initialize_context_management()
            await self._initialize_performance_monitoring()
            await self._initialize_adaptive_scheduling()
            
            self.initialized = True
            logger.info("Advanced Task Orchestrator initialized successfully")
            return True
            
        except Exception as e:
            logger.error(f"Failed to initialize Advanced Task Orchestrator: {e}")
            return False
    
    async def execute_multi_loop_task(self, 
                                    task_description: str,
                                    context: Optional[TaskContext] = None,
                                    max_loops: int = 10,
                                    success_criteria: Optional[Dict[str, Any]] = None) -> TaskResult:
        """
        Execute a task with intelligent multi-loop processing
        """
        if not self.initialized:
            raise RuntimeError("Advanced Orchestrator not initialized")
        
        # Generate task ID and setup context
        task_id = self._generate_task_id(task_description)
        if context is None:
            context = TaskContext(task_id=task_id)
        else:
            context.task_id = task_id
        
        logger.info(f"Executing multi-loop task: {task_id}")
        
        start_time = datetime.now()
        loops_executed = 0
        accumulated_context = {}
        performance_data = []
        
        try:
            # Phase 1: Intelligent task decomposition
            subtasks = await self._decompose_complex_task(task_description, context)
            logger.debug(f"Decomposed into {len(subtasks)} subtasks")
            
            # Phase 2: Multi-loop execution with context evolution
            for loop_iteration in range(max_loops):
                loops_executed += 1
                loop_start = datetime.now()
                
                logger.info(f"Starting loop {loop_iteration + 1}/{max_loops}")
                
                # Get enhanced context for this loop
                loop_context = await self._build_loop_context(
                    task_description, context, accumulated_context, loop_iteration
                )
                
                # Execute current subtask or continue main task
                if loop_iteration < len(subtasks):
                    current_task = subtasks[loop_iteration]
                    logger.debug(f"Executing subtask: {current_task}")
                else:
                    current_task = task_description
                
                # Execute with enhanced error handling and adaptation
                loop_result = await self._execute_single_loop(
                    current_task, loop_context, loop_iteration
                )
                
                # Evaluate loop success and context evolution
                loop_success, context_updates = await self._evaluate_loop_result(
                    loop_result, success_criteria, accumulated_context
                )
                
                # Update accumulated context
                accumulated_context.update(context_updates)
                
                # Performance tracking
                loop_time = (datetime.now() - loop_start).total_seconds()
                performance_data.append({
                    "loop": loop_iteration + 1,
                    "execution_time": loop_time,
                    "success": loop_success,
                    "context_evolution": len(context_updates),
                    "memory_efficiency": await self._calculate_memory_efficiency()
                })
                
                # Check if task is complete
                if await self._check_task_completion(
                    task_description, accumulated_context, success_criteria
                ):
                    logger.info(f"Task completed successfully after {loops_executed} loops")
                    break
                
                # Adaptive wait between loops
                await self._adaptive_loop_delay(performance_data, loop_iteration)
            
            # Phase 3: Result consolidation and learning
            execution_time = (datetime.now() - start_time).total_seconds()
            
            # Store learnings
            await self._store_execution_learnings(
                task_id, task_description, performance_data, accumulated_context
            )
            
            # Generate final result
            result = TaskResult(
                task_id=task_id,
                status=TaskStatus.COMPLETED,
                result=accumulated_context.get("final_result", "Task completed successfully"),
                execution_time=execution_time,
                steps_taken=loops_executed,
                memory_updates=accumulated_context.get("memory_updates", []),
                context_evolution=accumulated_context,
                performance_metrics={
                    "loops_executed": loops_executed,
                    "average_loop_time": sum(p["execution_time"] for p in performance_data) / len(performance_data),
                    "success_rate": sum(1 for p in performance_data if p["success"]) / len(performance_data),
                    "context_evolution_rate": sum(p["context_evolution"] for p in performance_data) / len(performance_data),
                    "memory_efficiency": sum(p["memory_efficiency"] for p in performance_data) / len(performance_data)
                },
                recommendations=await self._generate_execution_recommendations(performance_data)
            )
            
            # Update session metrics
            await self._update_session_metrics(result)
            
            return result
            
        except Exception as e:
            execution_time = (datetime.now() - start_time).total_seconds()
            
            error_result = TaskResult(
                task_id=task_id,
                status=TaskStatus.FAILED,
                error=str(e),
                execution_time=execution_time,
                steps_taken=loops_executed,
                context_evolution=accumulated_context
            )
            
            # Learn from failure
            await self._learn_from_failure(task_id, task_description, str(e), accumulated_context)
            
            return error_result
    
    async def _decompose_complex_task(self, task_description: str, context: TaskContext) -> List[str]:
        """Intelligently decompose complex tasks into manageable subtasks"""
        # Get memory context for similar tasks
        memory_context = await memory_manager.get_relevant_context(task_description)
        
        # Use LLM to decompose task (simplified approach)
        # In a full implementation, this would use the LLM to analyze and break down the task
        subtasks = []
        
        # Pattern-based decomposition
        if "search" in task_description.lower() and "visit" in task_description.lower():
            subtasks = [
                "Open search engine",
                f"Search for: {task_description}",
                "Analyze search results",
                "Visit relevant pages",
                "Extract key information"
            ]
        elif "fill" in task_description.lower() and "form" in task_description.lower():
            subtasks = [
                "Navigate to target page",
                "Locate form elements",
                "Fill form fields",
                "Validate form data",
                "Submit form"
            ]
        else:
            # Default decomposition
            subtasks = [task_description]
        
        logger.debug(f"Task decomposition: {len(subtasks)} subtasks created")
        return subtasks
    
    async def _build_loop_context(self, 
                                task_description: str, 
                                base_context: TaskContext, 
                                accumulated_context: Dict[str, Any],
                                loop_iteration: int) -> Dict[str, Any]:
        """Build enhanced context for each loop iteration"""
        
        # Get fresh memory context
        memory_context = await memory_manager.get_relevant_context(task_description)
        
        # Build comprehensive loop context
        loop_context = {
            "base_task": task_description,
            "loop_iteration": loop_iteration + 1,
            "accumulated_learnings": accumulated_context,
            "memory_context": memory_context,
            "user_context": asdict(base_context) if base_context else {},
            "session_metrics": self.session_metrics,
            "adaptation_insights": await self._get_adaptation_insights(task_description),
            "error_prevention": await self._get_error_prevention_strategies(task_description),
            "optimization_hints": await self._get_optimization_hints(accumulated_context)
        }
        
        return loop_context
    
    async def _execute_single_loop(self, 
                                 task_description: str, 
                                 context: Dict[str, Any],
                                 loop_iteration: int) -> Dict[str, Any]:
        """Execute a single loop iteration with enhanced monitoring"""
        
        try:
            # Execute task using browser agent
            result = await browser_agent.execute_task(
                task_description=task_description,
                context=context,
                record_session=True,
                max_steps=config.agent.max_steps_per_task
            )
            
            # Extract and process execution data
            loop_result = {
                "success": result.get("success", False),
                "result_data": result.get("result", ""),
                "execution_time": result.get("execution_time", 0),
                "steps_taken": result.get("steps_taken", 0),
                "errors": result.get("errors", []),
                "strategies_used": result.get("strategies", []),
                "final_url": result.get("final_url", ""),
                "context_updates": result.get("context_used", {}),
                "loop_iteration": loop_iteration + 1
            }
            
            return loop_result
            
        except Exception as e:
            logger.error(f"Loop {loop_iteration + 1} execution failed: {e}")
            return {
                "success": False,
                "error": str(e),
                "loop_iteration": loop_iteration + 1,
                "execution_time": 0,
                "steps_taken": 0
            }
    
    async def _evaluate_loop_result(self, 
                                  loop_result: Dict[str, Any], 
                                  success_criteria: Optional[Dict[str, Any]],
                                  accumulated_context: Dict[str, Any]) -> Tuple[bool, Dict[str, Any]]:
        """Evaluate loop result and determine context updates"""
        
        success = loop_result.get("success", False)
        context_updates = {}
        
        # Basic success evaluation
        if success and success_criteria:
            # Check against custom success criteria
            for criterion, expected_value in success_criteria.items():
                if criterion in loop_result and loop_result[criterion] != expected_value:
                    success = False
                    break
        
        # Extract context updates
        if success:
            context_updates.update({
                "last_successful_action": loop_result.get("result_data", ""),
                "successful_strategies": loop_result.get("strategies_used", []),
                "final_url": loop_result.get("final_url", ""),
                "execution_performance": {
                    "time": loop_result.get("execution_time", 0),
                    "steps": loop_result.get("steps_taken", 0)
                }
            })
        else:
            context_updates.update({
                "last_error": loop_result.get("error", "Unknown error"),
                "failed_strategies": loop_result.get("strategies_used", []),
                "error_context": loop_result.get("context_updates", {})
            })
        
        # Add loop-specific insights
        context_updates[f"loop_{loop_result.get('loop_iteration', 0)}_result"] = loop_result
        
        return success, context_updates
    
    async def _check_task_completion(self, 
                                   task_description: str,
                                   accumulated_context: Dict[str, Any],
                                   success_criteria: Optional[Dict[str, Any]]) -> bool:
        """Check if the overall task has been completed successfully"""
        
        # Check if we have a successful final result
        if "last_successful_action" not in accumulated_context:
            return False
        
        # Check custom success criteria
        if success_criteria:
            for criterion, expected_value in success_criteria.items():
                if criterion not in accumulated_context:
                    return False
                if accumulated_context[criterion] != expected_value:
                    return False
        
        # Check for completion indicators in the task description
        completion_indicators = [
            "completed successfully",
            "task finished",
            "goal achieved",
            "operation complete"
        ]
        
        last_result = str(accumulated_context.get("last_successful_action", "")).lower()
        return any(indicator in last_result for indicator in completion_indicators)
    
    async def _adaptive_loop_delay(self, performance_data: List[Dict[str, Any]], loop_iteration: int):
        """Implement adaptive delay between loops based on performance"""
        
        if len(performance_data) < 2:
            await asyncio.sleep(1)  # Default delay
            return
        
        # Calculate performance trend
        recent_performance = performance_data[-2:]
        avg_time = sum(p["execution_time"] for p in recent_performance) / len(recent_performance)
        success_rate = sum(1 for p in recent_performance if p["success"]) / len(recent_performance)
        
        # Adaptive delay calculation
        base_delay = 1.0
        
        # Longer delay if performance is degrading
        if avg_time > 10:  # If loops are taking too long
            base_delay *= 1.5
        
        # Shorter delay if everything is working well
        if success_rate >= 1.0:
            base_delay *= 0.5
        
        # Add some randomization to avoid patterns
        import random
        delay = base_delay * (0.8 + 0.4 * random.random())
        
        await asyncio.sleep(delay)
    
    async def _store_execution_learnings(self, 
                                       task_id: str,
                                       task_description: str,
                                       performance_data: List[Dict[str, Any]],
                                       accumulated_context: Dict[str, Any]):
        """Store execution learnings for future improvements"""
        
        learning_data = {
            "task_id": task_id,
            "task_description": task_description,
            "timestamp": datetime.now().isoformat(),
            "performance_summary": {
                "total_loops": len(performance_data),
                "success_rate": sum(1 for p in performance_data if p["success"]) / len(performance_data),
                "average_execution_time": sum(p["execution_time"] for p in performance_data) / len(performance_data),
                "total_execution_time": sum(p["execution_time"] for p in performance_data)
            },
            "context_evolution": accumulated_context,
            "key_insights": await self._extract_key_insights(performance_data, accumulated_context)
        }
        
        # Store in memory system
        await memory_manager.store_task_result(
            task_description, 
            learning_data, 
            learning_data["performance_summary"]["success_rate"] > 0.5
        )
    
    async def _generate_execution_recommendations(self, performance_data: List[Dict[str, Any]]) -> List[str]:
        """Generate recommendations based on execution performance"""
        
        recommendations = []
        
        if not performance_data:
            return recommendations
        
        # Analyze performance patterns
        avg_time = sum(p["execution_time"] for p in performance_data) / len(performance_data)
        success_rate = sum(1 for p in performance_data if p["success"]) / len(performance_data)
        total_loops = len(performance_data)
        
        # Generate recommendations
        if avg_time > 15:
            recommendations.append("Consider optimizing task execution - loops are taking longer than expected")
        
        if success_rate < 0.8:
            recommendations.append("Review error patterns and implement better error handling")
        
        if total_loops > 7:
            recommendations.append("Task may benefit from better decomposition into smaller subtasks")
        
        if success_rate >= 0.9 and avg_time < 5:
            recommendations.append("Excellent performance - consider this as a template for similar tasks")
        
        return recommendations
    
    async def _initialize_context_management(self):
        """Initialize advanced context management features"""
        self.global_context = {
            "session_start": datetime.now().isoformat(),
            "user_preferences": {},
            "learned_patterns": {},
            "optimization_strategies": {}
        }
    
    async def _initialize_performance_monitoring(self):
        """Initialize performance monitoring systems"""
        self.session_metrics = {
            "tasks_executed": 0,
            "success_rate": 0.0,
            "average_execution_time": 0.0,
            "memory_efficiency": 0.0,
            "adaptation_rate": 0.0,
            "session_start": datetime.now().isoformat()
        }
    
    async def _initialize_adaptive_scheduling(self):
        """Initialize adaptive task scheduling"""
        pass  # Placeholder for future scheduling enhancements
    
    async def _get_adaptation_insights(self, task_description: str) -> Dict[str, Any]:
        """Get adaptation insights for the task"""
        return {
            "similar_task_count": 0,
            "success_patterns": [],
            "common_adaptations": []
        }
    
    async def _get_error_prevention_strategies(self, task_description: str) -> List[str]:
        """Get error prevention strategies"""
        return [
            "Verify page load completion before actions",
            "Use explicit waits for dynamic content",
            "Implement fallback element selectors"
        ]
    
    async def _get_optimization_hints(self, context: Dict[str, Any]) -> List[str]:
        """Get optimization hints based on context"""
        return [
            "Cache frequently accessed elements",
            "Minimize page reloads",
            "Use efficient CSS selectors"
        ]
    
    async def _calculate_memory_efficiency(self) -> float:
        """Calculate current memory efficiency"""
        # Simplified calculation - could be enhanced with actual memory metrics
        return 0.8
    
    async def _extract_key_insights(self, performance_data: List[Dict[str, Any]], context: Dict[str, Any]) -> List[str]:
        """Extract key insights from execution data"""
        insights = []
        
        if performance_data:
            avg_time = sum(p["execution_time"] for p in performance_data) / len(performance_data)
            if avg_time < 5:
                insights.append("Fast execution achieved")
            elif avg_time > 15:
                insights.append("Slow execution - optimization needed")
        
        return insights
    
    async def _update_session_metrics(self, result: TaskResult):
        """Update session-level performance metrics"""
        self.session_metrics["tasks_executed"] += 1
        
        if result.status == TaskStatus.COMPLETED:
            current_success_rate = self.session_metrics["success_rate"]
            total_tasks = self.session_metrics["tasks_executed"]
            self.session_metrics["success_rate"] = (
                (current_success_rate * (total_tasks - 1) + 1.0) / total_tasks
            )
        
        # Update average execution time
        current_avg = self.session_metrics["average_execution_time"]
        total_tasks = self.session_metrics["tasks_executed"]
        self.session_metrics["average_execution_time"] = (
            (current_avg * (total_tasks - 1) + result.execution_time) / total_tasks
        )
    
    async def _learn_from_failure(self, task_id: str, task_description: str, error: str, context: Dict[str, Any]):
        """Learn from task failures"""
        failure_data = {
            "task_id": task_id,
            "task_description": task_description,
            "error": error,
            "context": context,
            "timestamp": datetime.now().isoformat()
        }
        
        # Store failure pattern
        await memory_manager.store_error_pattern(
            error_type=type(Exception).__name__,
            context=failure_data
        )
    
    def _generate_task_id(self, task_description: str) -> str:
        """Generate unique task ID"""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        task_hash = hashlib.md5(task_description.encode()).hexdigest()[:8]
        return f"task_{timestamp}_{task_hash}"
    
    async def get_orchestrator_status(self) -> Dict[str, Any]:
        """Get comprehensive orchestrator status"""
        return {
            "initialized": self.initialized,
            "active_tasks": len(self.active_tasks),
            "completed_tasks": len(self.completed_tasks),
            "failed_tasks": len(self.failed_tasks),
            "session_metrics": self.session_metrics,
            "global_context_size": len(self.global_context),
            "timestamp": datetime.now().isoformat()
        }
    
    async def cleanup(self):
        """Cleanup orchestrator resources"""
        try:
            logger.info("Cleaning up Advanced Task Orchestrator...")
            
            # Cleanup browser agent
            await browser_agent.cleanup()
            
            # Cleanup memory manager  
            await memory_manager.cleanup()
            
            # Clear local state
            self.task_queue = asyncio.PriorityQueue()
            self.active_tasks.clear()
            self.completed_tasks.clear()
            self.failed_tasks.clear()
            self.task_contexts.clear()
            self.global_context.clear()
            
            self.initialized = False
            logger.info("Advanced Task Orchestrator cleanup completed")
            
        except Exception as e:
            logger.error(f"Error during advanced orchestrator cleanup: {e}")

# Global advanced orchestrator instance
advanced_orchestrator = AdvancedTaskOrchestrator()

# Convenience functions
async def execute_advanced_task(task_description: str, **kwargs) -> TaskResult:
    """Convenience function to execute advanced tasks"""
    if not advanced_orchestrator.initialized:
        await advanced_orchestrator.initialize()
    return await advanced_orchestrator.execute_multi_loop_task(task_description, **kwargs)

async def get_advanced_status() -> Dict[str, Any]:
    """Convenience function to get advanced orchestrator status"""
    if not advanced_orchestrator.initialized:
        await advanced_orchestrator.initialize()
    return await advanced_orchestrator.get_orchestrator_status()

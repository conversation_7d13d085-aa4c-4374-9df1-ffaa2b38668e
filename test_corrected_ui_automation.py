#!/usr/bin/env python3
"""
🐺 Wild Weasel UI Automation - TEST VERSION (First 3 Rows)
===========================================================
Test the corrected UI automation with just the first 3 CSV rows
"""

import csv
import os
import time
import logging
from datetime import datetime
from playwright.sync_api import sync_playwright

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler("test_corrected_ui_automation.log"),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger("WildWeasel-Test")

class TestCorrectedUIAutomation:
    """Test corrected UI automation with first 3 rows only"""
    
    def __init__(self):
        self.config = {
            "csv_file": "./ICICI_processed_gainsight_mapped_enhanced_demo.csv",
            "target_url": "https://demo-emea1.gainsightcloud.com/v1/ui/customersuccess360?cid=1P02IPMAEL4M3CQGY4K5FHU22D2HHT11KCKU#/8fd79692-8e98-4543-b2ec-bc0dba3776ca"
        }
        self.activities = []
    
    def load_test_activities(self):
        """Load first 3 activities from CSV file"""
        try:
            if not os.path.exists(self.config['csv_file']):
                logger.error(f"❌ CSV file not found: {self.config['csv_file']}")
                return False
            
            self.activities = []
            with open(self.config['csv_file'], 'r', encoding='utf-8') as f:
                csv_reader = csv.DictReader(f)
                for i, row in enumerate(csv_reader):
                    if i >= 3:  # Only load first 3 rows
                        break
                    activity = {
                        "subject": row.get('Subject', '').strip(),
                        "activity_date": row.get('Activity Date', '').strip(),
                        "activity_type": row.get('Activity Type', '').strip(),
                        "plain_text": row.get('Plain Text', '').strip(),
                        "author_name": row.get('Author Name', '').strip(),
                        "flow_type": row.get('Flow Type', '').strip(),
                        "touchpoint_reason": row.get('Touchpoint Reason', '').strip()
                    }
                    self.activities.append(activity)
            
            logger.info(f"📊 Loaded {len(self.activities)} test activities")
            return True
                
        except Exception as e:
            logger.error(f"❌ Failed to load test activities: {e}")
            return False
    
    def convert_date_format(self, date_string):
        """Convert date from '2025-05-29 03:30:19' to '5/29/2025' and '03:30'"""
        try:
            if not date_string or date_string.strip() == '':
                return None, None
            
            dt = datetime.strptime(date_string.strip(), '%Y-%m-%d %H:%M:%S')
            gainsight_date = f"{dt.month}/{dt.day}/{dt.year}"
            gainsight_time = f"{dt.hour:02d}:{dt.minute:02d}"
            
            return gainsight_date, gainsight_time
            
        except Exception as e:
            logger.error(f"❌ Date conversion failed for '{date_string}': {e}")
            return None, None
    
    def create_test_activity(self, page, activity_data, row_index):
        """Create a single test activity - PURE UI AUTOMATION"""
        try:
            subject = activity_data["subject"]
            logger.info(f"📝 Creating test activity {row_index}: {subject[:50]}...")
            
            # Step 1: Click create button
            page.wait_for_selector('gs-cs360-header .gs-cs360-headeritem2 button', timeout=10000)
            page.click('gs-cs360-header .gs-cs360-headeritem2 button')
            time.sleep(3)
            logger.info("✅ Create dropdown clicked")
            
            # Step 2: Click Activity
            page.wait_for_selector('text=Activity', timeout=10000)
            page.click('text=Activity')
            time.sleep(3)
            logger.info("✅ Activity option clicked")
            
            # Step 3: Select Activity Type
            activity_type = activity_data["activity_type"]
            if activity_type and activity_type.strip():
                page.click('text="Activity Type" >> .. >> nz-select')
                time.sleep(2)
                page.click(f'text="{activity_type}"')
                time.sleep(1)
                logger.info(f"✅ Activity Type '{activity_type}' selected")
            
            # Step 4: Fill Subject
            if subject and subject.strip():
                subject_selector = '#cdk-overlay-12 > gs-composer > div.gs-timeline-composer.cdk-drag.ng-star-inserted > div.gs-activity-composer > form > div.gs-activity-composersection > div.gs-activity-composerright.ng-star-inserted > gs-fields > div > form > div > div:nth-child(3) > gs-text > nz-form-item > nz-form-control > div > span > input'
                page.fill(subject_selector, subject)
                logger.info(f"✅ Subject filled")
            
            # Step 5: Fill Date
            gainsight_date, gainsight_time = self.convert_date_format(activity_data["activity_date"])
            if gainsight_date:
                date_selector = '#cdk-overlay-15 > div > date-range-popup > div > div > div > calendar-input > div > div > input'
                page.fill(date_selector, gainsight_date)
                logger.info(f"✅ Date filled: {gainsight_date}")
            
            # Step 6: Fill Time
            if gainsight_time:
                time_selector = '#cdk-overlay-12 > gs-composer > div.gs-timeline-composer.cdk-drag.ng-star-inserted > div.gs-activity-composer > form > div.gs-activity-composersection > div.gs-activity-composerright.ng-star-inserted > gs-fields > div > form > div > div:nth-child(4) > gs-datetime-field > nz-form-item:nth-child(1) > div > nz-form-control.timepicker-error.ng-tns-c56-175.ant-form-item-control-wrapper.ant-col.ng-invalid.ng-untouched.ng-pristine > div > span > nz-time-picker > input'
                page.fill(time_selector, gainsight_time)
                logger.info(f"✅ Time filled: {gainsight_time}")
            
            # Step 7: Fill Notes
            plain_text = activity_data["plain_text"]
            if plain_text and plain_text.strip():
                page.click('#editor > div')
                time.sleep(1)
                page.keyboard.type(plain_text)
                logger.info("✅ Notes filled")
            
            # Step 8: Internal Recipients (simplified for test)
            author_name = activity_data["author_name"]
            if author_name and author_name.strip():
                try:
                    page.click('input[placeholder="Search Users"]')
                    time.sleep(1)
                    page.fill('#cdk-overlay-17 input', author_name.split()[0])
                    time.sleep(2)
                    page.click('.cdk-overlay-container [role="option"]:first-child')
                    logger.info("✅ Internal recipient selected")
                except:
                    logger.warning("⚠️ Internal recipient selection skipped")
            
            # Step 9: Log Activity
            page.evaluate("window.scrollTo(0, document.body.scrollHeight)")
            time.sleep(1)
            page.click('button:has-text("Log Activity")')
            logger.info("✅ Log Activity clicked")
            
            time.sleep(5)  # Wait for save
            logger.info(f"✅ Test activity {row_index} completed")
            return True
            
        except Exception as e:
            logger.error(f"❌ Test activity {row_index} failed: {e}")
            page.screenshot(path=f"test_error_{row_index}.png")
            return False
    
    def run_test(self):
        """Run the test automation"""
        try:
            logger.info("🐺 Starting Test UI Automation (First 3 Rows)...")
            
            if not self.load_test_activities():
                return False
            
            with sync_playwright() as playwright:
                browser = playwright.chromium.launch(headless=False)
                context = browser.new_context(viewport={'width': 1920, 'height': 1080})
                page = context.new_page()
                
                # Navigate to timeline
                page.goto(self.config["target_url"])
                page.wait_for_load_state("networkidle", timeout=30000)
                time.sleep(5)
                
                # Test each activity
                successful = 0
                for i, activity in enumerate(self.activities):
                    if self.create_test_activity(page, activity, i+1):
                        successful += 1
                    time.sleep(3)
                
                logger.info(f"🎯 Test Results: {successful}/{len(self.activities)} successful")
                
                # Keep browser open
                input("Press Enter to close browser...")
                browser.close()
                
                return successful == len(self.activities)
                
        except Exception as e:
            logger.error(f"❌ Test failed: {e}")
            return False

def main():
    """Main test function"""
    test = TestCorrectedUIAutomation()
    success = test.run_test()
    
    if success:
        print("🎉 TEST PASSED - UI automation is working!")
    else:
        print("❌ TEST FAILED - Check logs for issues")

if __name__ == "__main__":
    main()

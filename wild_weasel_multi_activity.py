#!/usr/bin/env python3
"""
ICICI to Gainsight Activity Converter
====================================

This script processes ICICI company activities from JSON format and maps:
1. meeting_type IDs to their display names using ID.json
2. touchpoint_tags IDs to their display names using Touchpoint_reason.JSON
3. Converts ICICI activities to Gainsight-ready JSON format

Author: Ramprasad Somaraju
Date: May 29, 2025
"""

import json
import os
import re
from datetime import datetime
from typing import Dict, List, Any, Optional
from collections import defaultdict, Counter

class ICICIActivityConverter:
    def __init__(self, base_path: str = "/Users/<USER>/Desktop/totango"):
        """Initialize the converter with file paths."""
        self.base_path = base_path
        self.icici_file = os.path.join(base_path, "ICICI.json")
        self.id_mapping_file = os.path.join(base_path, "ID.json")
        self.touchpoint_mapping_file = os.path.join(base_path, "Touchpoint_reason.JSON")
        self.gainsight_template_file = os.path.join(base_path, "Gainsight_payload.json")
        
        # Storage for mappings
        self.meeting_type_mapping = {}
        self.touchpoint_tags_mapping = {}
        self.icici_activities = []
        self.gainsight_template = {}
        
        # User mode selection
        self.use_demo_user = self.get_user_mode_selection()
        
        # Default values for demo environment
        self.demo_user = {
            "gsId": "1P01E316G9DAPFOLE6SOOUG71XRMN5F3PLER",
            "name": "Ram Prasad",
            "email": "<EMAIL>",
            "eid": None,
            "eobj": "User",
            "epp": None,
            "esys": "SALESFORCE",
            "sys": "GAINSIGHT",
            "pp": ""
        }
        
        self.default_context = {
            "id": "1P02IPMAEL4M3CQGY4K5FHU22D2HHT11KCKU",
            "obj": "Company",
            "eobj": "Account",
            "eid": None,
            "esys": "SALESFORCE",
            "lbl": "ICICI",
            "dsp": True,
            "base": True
        }
        
        # Statistics
        self.stats = {
            'total_activities': 0,
            'activities_with_meeting_type': 0,
            'activities_with_touchpoint_tags': 0,
            'activities_without_meeting_type': 0,
            'gainsight_activities_created': 0,
            'unmapped_meeting_types': set(),
            'meeting_type_usage': Counter(),
            'touchpoint_tags_usage': Counter()
        }

    def get_user_mode_selection(self) -> bool:
        """Get user selection for demo vs real user mode."""
        # Check for command line argument
        import sys
        if '--demo' in sys.argv:
            print("🎭 Using Demo Mode (via command line)")
            return True
        elif '--real' in sys.argv:
            print("🎭 Using Real Mode (via command line)")
            return False
        
        print("\n" + "="*50)
        print("🎭 USER MODE SELECTION")
        print("="*50)
        print("Choose the user mode for Gainsight activities:")
        print("1. Demo Mode - Use Ram Prasad demo user for all activities")
        print("2. Real Mode - Use actual user information from ICICI data")
        print()
        
        while True:
            try:
                choice = input("Enter your choice (1 for Demo, 2 for Real): ").strip()
                if choice == "1":
                    print("✅ Selected: Demo Mode - Using Ram Prasad demo user")
                    return True
                elif choice == "2":
                    print("✅ Selected: Real Mode - Using actual user information")
                    return False
                else:
                    print("❌ Invalid choice. Please enter 1 or 2.")
            except KeyboardInterrupt:
                print("\n\n🚫 Process cancelled by user")
                exit(1)
            except Exception:
                print("❌ Invalid input. Please enter 1 or 2.")

    def load_mappings(self) -> None:
        """Load ID mappings from JSON files."""
        print("📋 Loading mapping files...")
        
        # Load meeting type mappings
        try:
            with open(self.id_mapping_file, 'r', encoding='utf-8') as f:
                id_data = json.load(f)
            self.meeting_type_mapping = {item['id']: item['display_name'] for item in id_data}
            print(f"✅ Loaded {len(self.meeting_type_mapping)} meeting type mappings")
        except FileNotFoundError:
            print(f"❌ Error: {self.id_mapping_file} not found")
            raise
        except json.JSONDecodeError as e:
            print(f"❌ Error parsing {self.id_mapping_file}: {e}")
            raise

        # Load touchpoint tags mappings
        try:
            with open(self.touchpoint_mapping_file, 'r', encoding='utf-8') as f:
                touchpoint_data = json.load(f)
            self.touchpoint_tags_mapping = {item['id']: item['display_name'] for item in touchpoint_data}
            print(f"✅ Loaded {len(self.touchpoint_tags_mapping)} touchpoint tag mappings")
        except FileNotFoundError:
            print(f"❌ Error: {self.touchpoint_mapping_file} not found")
            raise
        except json.JSONDecodeError as e:
            print(f"❌ Error parsing {self.touchpoint_mapping_file}: {e}")
            raise

        # Load Gainsight template
        try:
            with open(self.gainsight_template_file, 'r', encoding='utf-8') as f:
                self.gainsight_template = json.load(f)
            print(f"✅ Loaded Gainsight template from {os.path.basename(self.gainsight_template_file)}")
        except FileNotFoundError:
            print(f"❌ Error: {self.gainsight_template_file} not found")
            raise
        except json.JSONDecodeError as e:
            print(f"❌ Error parsing {self.gainsight_template_file}: {e}")
            raise

    def load_icici_activities(self) -> None:
        """Load ICICI activities from JSON file."""
        print("📊 Loading ICICI activities...")
        
        try:
            with open(self.icici_file, 'r', encoding='utf-8') as f:
                self.icici_activities = json.load(f)
            self.stats['total_activities'] = len(self.icici_activities)
            print(f"✅ Loaded {self.stats['total_activities']} ICICI activities")
        except FileNotFoundError:
            print(f"❌ Error: {self.icici_file} not found")
            raise
        except json.JSONDecodeError as e:
            print(f"❌ Error parsing {self.icici_file}: {e}")
            raise

    def map_meeting_type(self, meeting_type_id: str) -> Optional[str]:
        """Map meeting type ID to display name."""
        if meeting_type_id in self.meeting_type_mapping:
            mapped_name = self.meeting_type_mapping[meeting_type_id]
            self.stats['meeting_type_usage'][mapped_name] += 1
            return mapped_name
        else:
            self.stats['unmapped_meeting_types'].add(meeting_type_id)
            return None

    def map_touchpoint_tags(self, touchpoint_tag_ids: List[str]) -> List[str]:
        """Map touchpoint tag IDs to display names."""
        mapped_names = []
        
        for tag_id in touchpoint_tag_ids:
            if tag_id in self.touchpoint_tags_mapping:
                mapped_name = self.touchpoint_tags_mapping[tag_id]
                mapped_names.append(mapped_name)
                self.stats['touchpoint_tags_usage'][mapped_name] += 1
            else:
                mapped_names.append("Internal Note")
                self.stats['touchpoint_tags_usage']["Internal Note"] += 1
        
        return mapped_names
        
    def get_real_user_from_activity(self, activity: Dict[str, Any]) -> Dict[str, Any]:
        """Extract real user information from ICICI activity using the actual ICICI data structure."""
        
        # Get properties from activity
        properties = activity.get('properties', {})
        
        # Try to extract user information from properties
        user_name = None
        user_email = None
        user_id = None
        
        # Method 1: Check for user_id (often an email) and entity_name (person name)
        if properties.get('user_id') and '@' in properties.get('user_id', ''):
            user_email = properties.get('user_id')
            user_id = user_email
            # Get the person's name from entity_name if it's not a company
            entity_name = properties.get('entity_name', '')
            if entity_name and entity_name != 'ICICI Bank' and '@' not in entity_name:
                user_name = entity_name
            else:
                # Extract name from email
                name_part = user_email.split('@')[0]
                user_name = name_part.replace('.', ' ').replace('_', ' ').title()
        
        # Method 2: Check if entity_id is an email and entity_name is a person
        elif properties.get('entity_id') and '@' in properties.get('entity_id', ''):
            user_email = properties.get('entity_id')
            user_id = user_email
            entity_name = properties.get('entity_name', '')
            if entity_name and entity_name != 'ICICI Bank' and '@' not in entity_name:
                user_name = entity_name
            else:
                # Extract name from email
                name_part = user_email.split('@')[0]
                user_name = name_part.replace('.', ' ').replace('_', ' ').title()
        
        # Method 3: Check for entity_name that might be a person (not company)
        elif properties.get('entity_name'):
            entity_name = properties.get('entity_name', '')
            if entity_name and '@' not in entity_name and entity_name != 'ICICI Bank':
                user_name = entity_name
                # Default email based on name if not found
                user_email = "<EMAIL>"
        
        # If we found real user info, create user object
        if user_name or user_email:
            return {
                "gsId": user_id or "1P01E316G9DAPFOLE6SOOUG71XRMN5F3PLER",
                "name": user_name or "Unknown User",
                "email": user_email or "<EMAIL>",
                "eid": None,
                "eobj": "User",
                "epp": None,
                "esys": "SALESFORCE",
                "sys": "GAINSIGHT",
                "pp": ""
            }
        
        # Fall back to demo user if no real user info found
        return self.demo_user.copy()

    def clean_flow_type(self, activity_type_id: str) -> str:
        """Clean up activity type ID to remove numbers and keep meaningful part."""
        if not activity_type_id:
            return "general"
        
        # Remove numbers and underscores, keep only the meaningful part
        # Examples: "risk_1560979263618" -> "risk", "onboarding_101" -> "onboarding"
        cleaned = activity_type_id.split('_')[0]  # Take first part before underscore
        
        # Handle special cases
        if cleaned == "intelligence":
            return "intelligence"
        elif cleaned in ["onboarding", "adoption", "support", "risk", "upsell"]:
            return cleaned
        else:
            return cleaned if cleaned else "general"

    def html_to_plain_text(self, html_content: str) -> str:
        """Convert HTML content to plain text."""
        if not html_content:
            return ""
        
        # Remove HTML tags using regex
        plain_text = re.sub(r'<[^>]+>', '', html_content)
        
        # Decode HTML entities
        plain_text = plain_text.replace('&lt;', '<')
        plain_text = plain_text.replace('&gt;', '>')
        plain_text = plain_text.replace('&amp;', '&')
        plain_text = plain_text.replace('&quot;', '"')
        plain_text = plain_text.replace('&#39;', "'")
        plain_text = plain_text.replace('&nbsp;', ' ')
        
        # Clean up extra whitespace
        plain_text = ' '.join(plain_text.split())
        
        return plain_text.strip()

    def get_meeting_type_from_activity(self, activity: Dict[str, Any]) -> str:
        """Determine meeting type from activity, defaulting to 'Update' if not found."""
        properties = activity.get('properties', {})
        
        # Check if there's a meeting_type_name (from processed data)
        meeting_type_name = properties.get('meeting_type_name')
        if meeting_type_name and meeting_type_name != "Update":
            return "EMAIL"
        
        # Default to Update for activities without specific meeting type
        self.stats['activities_without_meeting_type'] += 1
        return "UPDATE"

    def create_gainsight_activity(self, icici_activity: Dict[str, Any]) -> Dict[str, Any]:
        """Convert a single ICICI activity to Gainsight format."""
        properties = icici_activity.get('properties', {})
        
        # Determine user based on mode selection
        if self.use_demo_user:
            current_user = self.demo_user.copy()
        else:
            current_user = self.get_real_user_from_activity(icici_activity)
        
        # Determine meeting type
        meeting_type = self.get_meeting_type_from_activity(icici_activity)
        
        # Get timestamp (convert from milliseconds to milliseconds for Gainsight)
        activity_date = icici_activity.get('timestamp', int(datetime.now().timestamp() * 1000))
        
        # Get touchpoint tags - PROPERLY MAP FROM PROCESSED DATA
        touchpoint_reason = None  # Default to null
        
        # First check if we have processed touchpoint_tags_names (preferred)
        if 'touchpoint_tags_names' in properties:
            touchpoint_tags_names = properties.get('touchpoint_tags_names', [])
            if touchpoint_tags_names and len(touchpoint_tags_names) > 0:
                # Use the first mapped touchpoint tag name
                touchpoint_reason = touchpoint_tags_names[0]
        
        # If no processed names, check original touchpoint_tags and map on the fly
        elif 'touchpoint_tags' in properties:
            touchpoint_tags = properties.get('touchpoint_tags', [])
            if touchpoint_tags:
                # Handle both single ID and array of IDs
                if isinstance(touchpoint_tags, str):
                    touchpoint_tags = [touchpoint_tags]
                elif not isinstance(touchpoint_tags, list):
                    touchpoint_tags = []
                
                # Try to map the first touchpoint tag
                if touchpoint_tags and len(touchpoint_tags) > 0:
                    first_tag_id = touchpoint_tags[0]
                    if first_tag_id in self.touchpoint_tags_mapping:
                        touchpoint_reason = self.touchpoint_tags_mapping[first_tag_id]
                    else:
                        # Only use "Internal Note" if we had tags but couldn't map them
                        touchpoint_reason = "Internal Note"
        
        # If touchpoint_reason is still None, it means no touchpoint_tags field existed at all
        # This is correct - we should leave it as None/null for activities without touchpoint tags
        
        # Get activity type and clean it - FIX THE FLOW TYPE ISSUE
        activity_type_id = properties.get('activity_type_id', "adoption")
        cleaned_flow_type = self.clean_flow_type(activity_type_id)
        
        # Create content from activity properties
        content_parts = []
        
        # Add basic activity info
        if properties.get('display_name'):
            content_parts.append(f"<p><strong>Activity:</strong> {properties['display_name']}</p>")
        
        if properties.get('entity_name'):
            content_parts.append(f"<p><strong>Entity:</strong> {properties['entity_name']}</p>")
        
        if properties.get('action'):
            content_parts.append(f"<p><strong>Action:</strong> {properties['action']}</p>")
        
        if properties.get('prev_value') and properties.get('new_value'):
            content_parts.append(f"<p><strong>Changed from:</strong> {properties['prev_value']} <strong>to:</strong> {properties['new_value']}</p>")
        
        # Add campaign info if available
        if properties.get('name'):
            content_parts.append(f"<p><strong>Campaign:</strong> {properties['name']}</p>")
        
        if properties.get('description'):
            content_parts.append(f"<p><strong>Description:</strong> {properties['description']}</p>")
        
        # Default content if nothing found
        if not content_parts:
            content_parts.append(f"<p>Activity: {icici_activity.get('type', 'Unknown')} - {cleaned_flow_type}</p>")
        
        html_content = "".join(content_parts)
        plain_text = self.html_to_plain_text(html_content)
        
        # Create subject
        subject = properties.get('subject') or properties.get('display_name') or f"{cleaned_flow_type} activity"
        
        # Build Gainsight activity structure
        gainsight_activity = {
            "lastModifiedByUser": current_user.copy(),
            "note": {
                "customFields": {
                    "internalAttendees": [current_user.copy()],  # Use actual user info (not demo)
                    "externalAttendees": [],
                    "ant__Status1552512571338": None,
                    "Ant__Touchpoint_Reason__c": touchpoint_reason,  # Now properly mapped (null if no touchpoint tags)
                    "Ant__Flow_Type__c": cleaned_flow_type  # Now cleaned
                },
                "type": meeting_type,
                "subject": subject,
                "activityDate": activity_date,
                "content": html_content,
                "plainText": plain_text,
                "trackers": None
            },
            "mentions": [],
            "relatedRecords": {},
            "meta": {
                "activityTypeId": "c81eeccf-2aa1-45bc-be71-5377f148e1e9",
                "ctaId": None,
                "source": "C360",
                "hasTask": False,
                "emailSent": False,
                "systemType": "GAINSIGHT",
                "notesTemplateId": None
            },
            "author": current_user.copy(),
            "syncedToSFDC": False,
            "tasks": [],
            "attachments": [],
            "contexts": [self.default_context.copy()]
        }
        
        return gainsight_activity

    def convert_to_gainsight_format(self, processed_activities: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """Convert all processed ICICI activities to Gainsight format."""
        print("🔄 Converting activities to Gainsight format...")
        
        gainsight_activities = []
        
        for activity in processed_activities:
            try:
                gainsight_activity = self.create_gainsight_activity(activity)
                gainsight_activities.append(gainsight_activity)
                self.stats['gainsight_activities_created'] += 1
            except Exception as e:
                print(f"❌ Error converting activity {activity.get('id', 'unknown')}: {e}")
                continue
        
        print(f"✅ Successfully converted {len(gainsight_activities)} activities to Gainsight format")
        return gainsight_activities

    def process_activities(self) -> List[Dict[str, Any]]:
        """Process all activities and apply mappings."""
        print("🔄 Processing activities and applying mappings...")
        
        processed_activities = []
        
        for activity in self.icici_activities:
            processed_activity = activity.copy()
            
            # Check if activity has properties
            if 'properties' in activity and activity['properties']:
                properties = activity['properties'].copy()
                
                # Process meeting_type
                if 'meeting_type' in properties:
                    self.stats['activities_with_meeting_type'] += 1
                    meeting_type_id = properties['meeting_type']
                    
                    # Add mapped meeting type name
                    mapped_name = self.map_meeting_type(meeting_type_id)
                    properties['meeting_type_id'] = meeting_type_id
                    properties['meeting_type_name'] = mapped_name if mapped_name else f"UNMAPPED_{meeting_type_id}"
                
                # Process touchpoint_tags
                if 'touchpoint_tags' in properties:
                    self.stats['activities_with_touchpoint_tags'] += 1
                    touchpoint_tags = properties['touchpoint_tags']
                    
                    # Handle both single ID and array of IDs
                    if isinstance(touchpoint_tags, str):
                        touchpoint_tags = [touchpoint_tags]
                    elif not isinstance(touchpoint_tags, list):
                        touchpoint_tags = []
                    
                    # Map touchpoint tags
                    mapped_names = self.map_touchpoint_tags(touchpoint_tags)
                    
                    # Store both original IDs and mapped names
                    properties['touchpoint_tags_ids'] = touchpoint_tags
                    properties['touchpoint_tags_names'] = mapped_names
                
                processed_activity['properties'] = properties
            
            processed_activities.append(processed_activity)
        
        return processed_activities

    def generate_summary_report(self) -> str:
        """Generate a comprehensive summary report."""
        report = []
        report.append("=" * 60)
        report.append("ICICI ACTIVITY PROCESSING SUMMARY")
        report.append("=" * 60)
        report.append(f"🎭 User Mode: {'Demo Mode (Ram Prasad)' if self.use_demo_user else 'Real Mode (Actual Users)'}")
        report.append(f"📊 Total Activities: {self.stats['total_activities']:,}")
        report.append(f"🎯 Activities with meeting_type: {self.stats['activities_with_meeting_type']:,}")
        report.append(f"🏷️  Activities with touchpoint_tags: {self.stats['activities_with_touchpoint_tags']:,}")
        report.append(f"📝 Activities without meeting_type (defaulted to Update): {self.stats['activities_without_meeting_type']:,}")
        report.append(f"🚀 Gainsight activities created: {self.stats['gainsight_activities_created']:,}")
        report.append("")

        # Meeting Type Analysis
        report.append("📋 MEETING TYPE ANALYSIS")
        report.append("-" * 30)
        if self.stats['meeting_type_usage']:
            report.append("✅ Successfully Mapped Meeting Types:")
            for name, count in self.stats['meeting_type_usage'].most_common():
                report.append(f"   • {name}: {count:,} activities")

        if self.stats['unmapped_meeting_types']:
            report.append(f"\n❌ Unmapped Meeting Type IDs ({len(self.stats['unmapped_meeting_types'])}):") 
            for unmapped_id in sorted(self.stats['unmapped_meeting_types']):
                report.append(f"   • {unmapped_id}")
        report.append("")

        # Touchpoint Tags Analysis
        report.append("🏷️  TOUCHPOINT TAGS ANALYSIS")
        report.append("-" * 35)
        if self.stats['touchpoint_tags_usage']:
            report.append("✅ Mapped Touchpoint Tags:")
            for name, count in self.stats['touchpoint_tags_usage'].most_common():
                report.append(f"   • {name}: {count:,} activities")
        report.append("")

        # Gainsight Conversion Summary
        report.append("🚀 GAINSIGHT CONVERSION SUMMARY")
        report.append("-" * 35)
        report.append(f"✅ Total activities converted: {self.stats['gainsight_activities_created']:,}")
        report.append(f"📧 EMAIL type activities: {self.stats['activities_with_meeting_type']:,}")
        report.append(f"📝 UPDATE type activities: {self.stats['activities_without_meeting_type']:,}")
        report.append(f"🎭 User mode: {'Demo (Ram Prasad)' if self.use_demo_user else 'Real (Actual Users)'}")
        report.append(f"🔧 Flow types: Cleaned (removed numeric suffixes)")
        report.append(f"🏷️  Touchpoint reasons: Properly mapped (not all 'Internal Note')")
        report.append("")

        # Solutions for Unmapped IDs
        if self.stats['unmapped_meeting_types']:
            report.append("💡 SOLUTIONS FOR UNMAPPED MEETING TYPE IDs")
            report.append("-" * 40)
            report.append("1. Check if these IDs exist in newer versions of mapping files")
            report.append("2. Contact data source administrator for missing mappings")
            report.append("3. Create manual mappings for critical unmapped IDs")
            report.append("4. Use 'UNMAPPED_{ID}' as fallback display names")

        report.append("=" * 60)
        return "\n".join(report)

    def save_processed_data(self, processed_activities: List[Dict[str, Any]], output_file: str = None) -> str:
        """Save processed activities to JSON file."""
        if output_file is None:
            output_file = os.path.join(self.base_path, "ICICI_processed.json")
        
        try:
            with open(output_file, 'w', encoding='utf-8') as f:
                json.dump(processed_activities, f, indent=2, ensure_ascii=False)
            print(f"💾 Processed data saved to: {output_file}")
            return output_file
        except Exception as e:
            print(f"❌ Error saving processed data: {e}")
            raise

    def run_conversion(self) -> None:
        """Run the complete conversion process."""
        print("🚀 Starting ICICI Activity Conversion Process")
        print("=" * 50)
        
        try:
            # Step 1: Load all data
            self.load_mappings()
            self.load_icici_activities()
            
            # Step 2: Process activities
            processed_activities = self.process_activities()
            
            # Step 3: Convert to Gainsight format
            gainsight_activities = self.convert_to_gainsight_format(processed_activities)
            
            # Step 4: Generate and display summary
            summary = self.generate_summary_report()
            print(summary)
            
            # Step 5: Save processed data
            processed_output_file = self.save_processed_data(processed_activities)
            
            # Step 6: Save Gainsight data
            gainsight_output_file = self.save_gainsight_data(gainsight_activities)
            
            print(f"\n🎉 Conversion completed successfully!")
            print(f"📄 Summary report displayed above")
            print(f"💾 Processed ICICI data: {processed_output_file}")
            print(f"🚀 Gainsight-ready data: {gainsight_output_file}")
            
        except Exception as e:
            print(f"💥 Conversion failed: {e}")
            raise

    def save_gainsight_data(self, gainsight_activities: List[Dict[str, Any]], output_file: str = None) -> str:
        """Save Gainsight activities to JSON file."""
        if output_file is None:
            output_file = os.path.join(self.base_path, "gainsight.json")
        
        try:
            with open(output_file, 'w', encoding='utf-8') as f:
                json.dump(gainsight_activities, f, indent=2, ensure_ascii=False)
            print(f"🚀 Gainsight data saved to: {output_file}")
            return output_file
        except Exception as e:
            print(f"❌ Error saving Gainsight data: {e}")
            raise

def main():
    """Main function to run the converter."""
    # For testing purposes, you can set use_demo_mode here
    # or uncomment the line below to skip interactive selection
    # import sys; sys.argv.append('--demo')  # Force demo mode for testing
    
    converter = ICICIActivityConverter()
    converter.run_conversion()

if __name__ == "__main__":
    main()

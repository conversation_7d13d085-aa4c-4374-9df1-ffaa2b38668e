#!/usr/bin/env python3
"""
ICICI Activity Type Analyzer and Converter
Analyzes ICICI.json to understand activity types and demonstrates conversion
"""

import json
from pathlib import Path
from collections import Counter
from datetime import datetime


def analyze_icici_data():
    """Analyze ICICI.json to understand the data structure"""
    print("🔍 Analyzing ICICI.json data structure...")
    
    # Load data
    with open("/Users/<USER>/Desktop/totango/ICICI.json", "r") as f:
        icici_data = json.load(f)
    
    # Analyze activity types
    activity_types = Counter()
    activity_type_ids = Counter()
    property_keys = set()
    
    for activity in icici_data:
        activity_types[activity.get("type")] += 1
        props = activity.get("properties", {})
        activity_type_ids[props.get("activity_type_id")] += 1
        property_keys.update(props.keys())
    
    print(f"\n📊 Total activities: {len(icici_data)}")
    print(f"\n📈 Activity types found:")
    for atype, count in activity_types.most_common():
        print(f"   • {atype}: {count}")
    
    print(f"\n🏷️ Activity type IDs found:")
    # Load flowtype mappings
    with open("/Users/<USER>/Desktop/totango/flowtype.json", "r") as f:
        flow_types = {item["activity_type_id"]: item["display_name"] for item in json.load(f)}
    
    for type_id, count in activity_type_ids.most_common():
        name = flow_types.get(type_id, "Unknown")
        print(f"   • {name} ({type_id}): {count}")
    
    print(f"\n🔑 Unique property keys found: {len(property_keys)}")
    
    # Check if any activities have meeting_type
    meeting_type_count = sum(1 for a in icici_data if "meeting_type" in a.get("properties", {}))
    print(f"\n📅 Activities with meeting_type field: {meeting_type_count}")
    
    # Show sample activities
    print("\n📝 Sample activities:")
    for i, activity in enumerate(icici_data[:3]):
        print(f"\n--- Activity {i+1} ---")
        print(f"Type: {activity.get('type')}")
        print(f"Timestamp: {datetime.fromtimestamp(activity.get('timestamp', 0)/1000).strftime('%Y-%m-%d %H:%M:%S')}")
        props = activity.get("properties", {})
        print(f"Activity Type: {flow_types.get(props.get('activity_type_id'), 'Unknown')}")
        print(f"Key properties: {', '.join(list(props.keys())[:5])}")


def create_sample_touchpoint_data():
    """Create sample touchpoint data to demonstrate conversion"""
    print("\n🎯 Creating sample touchpoint data for demonstration...")
    
    # Load ID mappings
    with open("/Users/<USER>/Desktop/totango/ID.json", "r") as f:
        meeting_types = json.load(f)
    
    # Create sample touchpoint activities
    sample_touchpoints = [
        {
            "id": "sample_email_001",
            "timestamp": int(datetime.now().timestamp() * 1000),
            "type": "touchpoint",
            "account": {"id": "0015p00005R7ysqAAB"},
            "properties": {
                "meeting_type": "68c0d13a-40f1-47e4-9bb4-3d1fb6a16515",  # Email
                "subject": "Follow-up on SCA implementation",
                "description": "Discussed implementation timeline and next steps",
                "activity_type_id": "adoption",
                "touchpoint_reason": "CADENCE"
            }
        },
        {
            "id": "sample_meeting_001", 
            "timestamp": int(datetime.now().timestamp() * 1000) - ********,  # Yesterday
            "type": "touchpoint",
            "account": {"id": "0015p00005R7ysqAAB"},
            "properties": {
                "meeting_type": "93b4649c-8459-4f56-be3f-be75f7506ee0",  # Web meeting
                "subject": "Quarterly Business Review",
                "description": "Reviewed Q4 performance and discussed Q1 goals",
                "activity_type_id": "business_review_1628207371592",
                "touchpoint_reason": "ACCOUNT REVIEW"
            }
        },
        {
            "id": "sample_call_001",
            "timestamp": int(datetime.now().timestamp() * 1000) - *********,  # 2 days ago
            "type": "touchpoint",
            "account": {"id": "0015p00005R7ysqAAB"},
            "properties": {
                "meeting_type": "8d08857d-c9c4-4192-90ff-7f295e25a4d9",  # Phone call
                "subject": "Support escalation discussion",
                "description": "Addressed critical issue with scanner maintenance",
                "activity_type_id": "support",
                "touchpoint_reason": "ESCALATION"
            }
        }
    ]
    
    # Save sample data
    sample_path = Path("/Users/<USER>/Desktop/wildweasel/Browser/data/sample_touchpoints.json")
    sample_path.parent.mkdir(exist_ok=True)
    with open(sample_path, "w") as f:
        json.dump(sample_touchpoints, f, indent=2)
    
    print(f"✅ Created {len(sample_touchpoints)} sample touchpoint activities")
    print(f"💾 Saved to: {sample_path}")
    
    # Show mapping
    print("\n🔄 Demonstrating meeting type mapping:")
    for activity in sample_touchpoints:
        meeting_type_id = activity["properties"]["meeting_type"]
        meeting_type_name = next((m["display_name"] for m in meeting_types if m["id"] == meeting_type_id), "Unknown")
        print(f"   • {activity['properties']['subject']} → {meeting_type_name}")
    
    return sample_touchpoints


def convert_sample_to_gainsight(sample_touchpoints):
    """Convert sample touchpoints to Gainsight format"""
    print("\n🔄 Converting sample touchpoints to Gainsight format...")
    
    # Load Gainsight template
    with open("/Users/<USER>/Desktop/wildweasel/Browser/Gainsight_payload.json", "r") as f:
        template = json.load(f)
    
    # Load meeting type mappings
    with open("/Users/<USER>/Desktop/totango/ID.json", "r") as f:
        meeting_types = {m["id"]: m["display_name"] for m in json.load(f)}
    
    converted_activities = []
    
    for activity in sample_touchpoints:
        props = activity["properties"]
        meeting_type = meeting_types.get(props["meeting_type"], "Unknown")
        
        # Map to Gainsight activity type
        gainsight_type = "NOTE"  # Default
        if "Email" in meeting_type:
            gainsight_type = "EMAIL"
        elif "call" in meeting_type.lower():
            gainsight_type = "CALL"
        elif "meeting" in meeting_type.lower():
            gainsight_type = "MEETING"
        
        # Build Gainsight payload
        gainsight_activity = template.copy()
        gainsight_activity["note"].update({
            "type": gainsight_type,
            "subject": props["subject"],
            "activityDate": activity["timestamp"],
            "content": f"<p>{props['description']}</p>",
            "plainText": props["description"]
        })
        
        # Update contexts with ICICI account
        gainsight_activity["contexts"] = [{
            "id": activity["account"]["id"],
            "obj": "Company",
            "lbl": "ICICI Bank",
            "dsp": True,
            "base": True
        }]
        
        converted_activities.append(gainsight_activity)
    
    # Save converted activities
    output_path = Path("/Users/<USER>/Desktop/wildweasel/Browser/data/sample_gainsight_activities.json")
    with open(output_path, "w") as f:
        json.dump(converted_activities, f, indent=2)
    
    print(f"✅ Converted {len(converted_activities)} activities")
    print(f"💾 Saved to: {output_path}")
    
    # Show conversion results
    print("\n📋 Conversion results:")
    for i, (original, converted) in enumerate(zip(sample_touchpoints, converted_activities)):
        print(f"\n{i+1}. {original['properties']['subject']}")
        print(f"   Totango type: {meeting_types.get(original['properties']['meeting_type'])}")
        print(f"   → Gainsight type: {converted['note']['type']}")


def main():
    """Main execution"""
    print("🚀 ICICI to Gainsight Activity Analyzer & Converter")
    print("=" * 60)
    
    # Analyze existing ICICI data
    analyze_icici_data()
    
    # Create and convert sample data
    sample_data = create_sample_touchpoint_data()
    convert_sample_to_gainsight(sample_data)
    
    print("\n" + "=" * 60)
    print("📌 Summary:")
    print("1. The ICICI.json contains mostly automated activities")
    print("2. Created sample touchpoint data with meeting types")
    print("3. Demonstrated conversion to Gainsight format")
    print("\n💡 Next steps:")
    print("1. If you have actual touchpoint data, place it in the same format")
    print("2. Run the icici_to_gainsight_converter.py for full migration")
    print("3. Use either API or UI automation for posting to Gainsight")


if __name__ == "__main__":
    main()

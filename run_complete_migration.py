#!/usr/bin/env python3
"""
🚀 MASTER SCRIPT - Complete ICICI to Gainsight Migration
========================================================

This single script runs the entire migration process and shows you where all files are saved.

What it does:
1. Maps meeting types to Gainsight activity types
2. Creates enhanced CSV with flow types and touchpoint reasons
3. Generates both Demo and Real data versions
4. Shows you exactly where everything is saved

Author: Assistant
Date: January 29, 2025
"""

import subprocess
import sys
import os
from pathlib import Path

def print_banner():
    """Print the application banner."""
    banner = """
╔══════════════════════════════════════════════════════════════════════════════╗
║                🚀 COMPLETE ICICI TO GAINSIGHT MIGRATION                      ║
║                                                                              ║
║  This script runs everything you need:                                      ║
║  • Maps all 322 activities to Gainsight activity types                      ║
║  • Creates enhanced CSV with flow types and touchpoint reasons              ║
║  • Generates Demo (Ram Prasad) and Real (ICICI users) versions             ║
║  • Shows you exactly where all files are saved                              ║
╚══════════════════════════════════════════════════════════════════════════════╝
    """
    print(banner)

def check_prerequisites():
    """Check if all required files exist."""
    print("🔍 CHECKING PREREQUISITES")
    print("=" * 40)

    required_files = [
        "/Users/<USER>/Desktop/totango/ICICI_processed.json",
        "/Users/<USER>/Desktop/totango/flowtype.json",
        "/Users/<USER>/Desktop/totango/Touchpoint_reason.JSON"
    ]

    all_files_exist = True

    for file_path in required_files:
        if Path(file_path).exists():
            file_size = Path(file_path).stat().st_size
            print(f"✅ {os.path.basename(file_path)}: {file_size:,} bytes")
        else:
            print(f"❌ Missing: {file_path}")
            all_files_exist = False

    return all_files_exist

def run_step(step_name, command, description):
    """Run a single step of the migration process."""
    print(f"\n🔄 {step_name}")
    print("=" * 60)
    print(f"📋 {description}")
    print(f"🚀 Running: {command}")

    try:
        result = subprocess.run(command, shell=True, capture_output=True, text=True, timeout=60)

        if result.returncode == 0:
            print(f"✅ {step_name} completed successfully!")
            # Show last few lines of output
            output_lines = result.stdout.strip().split('\n')
            if len(output_lines) > 5:
                print("📄 Output (last 5 lines):")
                for line in output_lines[-5:]:
                    print(f"   {line}")
            else:
                print("📄 Output:")
                for line in output_lines:
                    print(f"   {line}")
            return True
        else:
            print(f"❌ {step_name} failed!")
            print(f"Error: {result.stderr}")
            return False

    except subprocess.TimeoutExpired:
        print(f"⏰ {step_name} timed out!")
        return False
    except Exception as e:
        print(f"💥 {step_name} error: {e}")
        return False

def show_generated_files():
    """Show all generated files and their locations."""
    print(f"\n📁 GENERATED FILES AND LOCATIONS")
    print("=" * 50)

    # Define expected output files
    output_files = [
        {
            "name": "Mapped JSON (Intermediate)",
            "path": "/Users/<USER>/Desktop/totango/ICICI_processed_gainsight_mapped.json",
            "description": "Activities with Gainsight activity types mapped"
        },
        {
            "name": "Demo CSV (Testing)",
            "path": "ICICI_processed_gainsight_mapped_enhanced_demo.csv",
            "description": "CSV with Ram Prasad as author - for testing Gainsight import"
        },
        {
            "name": "Real CSV (Production)",
            "path": "ICICI_processed_gainsight_mapped_enhanced_real.csv",
            "description": "CSV with real ICICI users - for actual migration"
        },
        {
            "name": "Basic CSV (Backup)",
            "path": "/Users/<USER>/Desktop/totango/ICICI_processed_gainsight_import.csv",
            "description": "Basic CSV without enhancements - backup option"
        }
    ]

    print("📊 Files created by this migration:")

    for file_info in output_files:
        file_path = file_info["path"]

        if Path(file_path).exists():
            file_size = Path(file_path).stat().st_size
            print(f"\n✅ {file_info['name']}")
            print(f"   📄 File: {file_path}")
            print(f"   📊 Size: {file_size:,} bytes")
            print(f"   📋 Purpose: {file_info['description']}")
        else:
            print(f"\n❌ {file_info['name']}")
            print(f"   📄 Expected: {file_path}")
            print(f"   ⚠️  File not found")

def show_usage_instructions():
    """Show how to use the generated files."""
    print(f"\n🚀 HOW TO USE THE GENERATED FILES")
    print("=" * 50)

    print("📋 For Testing Gainsight Import:")
    print("   1. Use: ICICI_processed_gainsight_mapped_enhanced_demo.csv")
    print("   2. Import into Gainsight Timeline")
    print("   3. Verify all 322 activities import correctly")
    print("   4. Check that Ram Prasad appears as author for all activities")

    print("\n📋 For Production Migration:")
    print("   1. Use: ICICI_processed_gainsight_mapped_enhanced_real.csv")
    print("   2. Import into Gainsight Timeline")
    print("   3. Verify all 322 activities import correctly")
    print("   4. Check that real ICICI users appear as authors")

    print("\n📊 What's in the CSV files:")
    print("   • 322 rows (one per activity)")
    print("   • Sequential numbering (1, 2, 3...)")
    print("   • Proper Gainsight activity types (Update, Meeting, Email)")
    print("   • Real flow types (Adoption, Onboarding, Risk, etc.)")
    print("   • Real touchpoint reasons (CADENCE, FEE, etc.)")
    print("   • All required Gainsight fields populated")

def main():
    """Main migration function."""
    print_banner()

    # Step 1: Check prerequisites
    if not check_prerequisites():
        print("\n❌ Missing required files. Please ensure all source files are in place.")
        return 1

    print(f"\n🎯 MIGRATION PROCESS")
    print("=" * 30)
    print("This will run 3 steps:")
    print("1. Map meeting types to Gainsight activity types")
    print("2. Generate Demo CSV (Ram Prasad)")
    print("3. Generate Real CSV (ICICI users)")

    try:
        response = input("\nProceed with complete migration? (y/n): ").strip().lower()

        if response not in ['y', 'yes']:
            print("\n👋 Migration cancelled by user.")
            return 0

        # Step 1: Run the complete mapping
        success1 = run_step(
            "STEP 1: Gainsight Activity Type Mapping",
            "python3 complete_gainsight_mapping.py",
            "Maps all 322 activities to proper Gainsight activity types"
        )

        if not success1:
            print("\n❌ Migration failed at Step 1")
            return 1

        # Step 2: Generate Demo CSV
        success2 = run_step(
            "STEP 2: Generate Demo CSV",
            "echo '1' | python3 enhanced_csv_exporter.py",
            "Creates CSV with Ram Prasad as author for testing"
        )

        if not success2:
            print("\n❌ Migration failed at Step 2")
            return 1

        # Step 3: Generate Real CSV
        success3 = run_step(
            "STEP 3: Generate Real CSV",
            "echo '2' | python3 enhanced_csv_exporter.py",
            "Creates CSV with real ICICI users for production"
        )

        if not success3:
            print("\n❌ Migration failed at Step 3")
            return 1

        # Show results
        show_generated_files()
        show_usage_instructions()

        print(f"\n🎉 MIGRATION COMPLETED SUCCESSFULLY!")
        print("=" * 50)
        print("✅ All 322 ICICI activities processed")
        print("✅ Gainsight activity types mapped")
        print("✅ Flow types and touchpoint reasons mapped")
        print("✅ Demo and Real CSV files generated")
        print("✅ Ready for Gainsight Timeline import!")

        return 0

    except KeyboardInterrupt:
        print("\n👋 Migration cancelled by user.")
        return 0

if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)

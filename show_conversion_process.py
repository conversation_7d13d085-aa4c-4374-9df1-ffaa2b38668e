#!/usr/bin/env python3
"""
Show ICICI to Gainsight Conversion Process
Demonstrates how the conversion works step by step
"""

import json
import asyncio
from datetime import datetime
from pathlib import Path

from gemini_client import GeminiClient

def print_step(step_num, title):
    """Print a step header"""
    print(f"\n{'='*60}")
    print(f"STEP {step_num}: {title}")
    print('='*60)

def show_icici_data_structure():
    """Show the structure of ICICI data"""
    print_step(1, "ICICI DATA STRUCTURE")
    
    # Check if actual file exists
    icici_file = "data/totango/ICICI.json"
    if Path(icici_file).exists():
        print(f"✅ Found actual ICICI file: {icici_file}")
        try:
            with open(icici_file, 'r') as f:
                data = json.load(f)
            
            print(f"📊 Total activities: {len(data)}")
            
            if len(data) > 0:
                print("\n🔍 Sample activity from your data:")
                sample = data[0]
                print(json.dumps(sample, indent=2))
                
                # Show activity types
                activity_types = {}
                for activity in data[:20]:  # Check first 20
                    act_type = activity.get('type', 'unknown')
                    activity_types[act_type] = activity_types.get(act_type, 0) + 1
                
                print(f"\n📈 Activity types found:")
                for act_type, count in activity_types.items():
                    print(f"   • {act_type}: {count} activities")
            
            return data
            
        except Exception as e:
            print(f"❌ Error reading file: {e}")
    
    # Show sample structure if no file
    print("📋 Sample ICICI activity structure:")
    sample_data = {
        "id": "activity_123456",
        "timestamp": 1640995200000,
        "type": "campaign_touch",
        "properties": {
            "activity_type_id": "onboarding_101",
            "name": "Welcome Email Campaign",
            "display_name": "Customer Onboarding",
            "description": "Initial welcome email sent to new customers"
        }
    }
    print(json.dumps(sample_data, indent=2))
    return [sample_data]

def show_mapping_process():
    """Show how ICICI data maps to Gainsight"""
    print_step(2, "MAPPING PROCESS")
    
    print("🗺️  Activity Type Mapping:")
    mappings = {
        "onboarding_101": "c81eeccf-2aa1-45bc-be71-5377f148e1e9",
        "adoption": "68c0d13a-40f1-47e4-9bb4-3d1fb6a16515",
        "intelligence_1561140678082": "93b4649c-8459-4f56-be3f-be75f7506ee0"
    }
    
    for icici_id, gainsight_id in mappings.items():
        print(f"   {icici_id}")
        print(f"   ↓")
        print(f"   {gainsight_id}")
        print()
    
    print("📋 Note Type Mapping:")
    note_types = {
        "campaign_touch": "EMAIL",
        "webhook": "EMAIL", 
        "account_alert": "INTERNAL_NOTE",
        "automated_attribute_change": "INTERNAL_NOTE"
    }
    
    for icici_type, gainsight_type in note_types.items():
        print(f"   {icici_type} → {gainsight_type}")

async def show_ai_generation():
    """Show how AI generates content"""
    print_step(3, "AI CONTENT GENERATION")
    
    sample_activity = {
        "type": "campaign_touch",
        "properties": {
            "name": "Welcome Email Campaign",
            "display_name": "Customer Onboarding",
            "description": "Initial welcome email sent to new customers"
        }
    }
    
    print("📥 Input activity:")
    print(json.dumps(sample_activity, indent=2))
    
    print("\n🤖 Generating content with Gemini AI...")
    
    prompt = f"""
    Convert this ICICI activity data into a meaningful Gainsight activity:
    
    Activity Type: {sample_activity.get('type', '')}
    Properties: {json.dumps(sample_activity.get('properties', {}), indent=2)}
    
    Generate:
    1. A clear, professional subject line (max 100 characters)
    2. A detailed content description (2-3 sentences explaining what happened)
    
    Format your response as:
    SUBJECT: [subject line]
    CONTENT: [content description]
    """
    
    try:
        async with GeminiClient() as client:
            response = await client.complete(
                prompt,
                task_type="general",
                max_tokens=300
            )
            
            if response.success:
                print(f"✅ AI Response (took {response.latency:.2f}s):")
                print(response.content)
                
                # Parse response
                lines = response.content.strip().split('\n')
                subject = "AI Generated Subject"
                content = "AI Generated Content"
                
                for line in lines:
                    if line.startswith("SUBJECT:"):
                        subject = line.replace("SUBJECT:", "").strip()
                    elif line.startswith("CONTENT:"):
                        content = line.replace("CONTENT:", "").strip()
                
                return subject, content
            else:
                print(f"❌ AI failed: {response.error}")
                return "Fallback Subject", "Fallback Content"
                
    except Exception as e:
        print(f"❌ Error: {e}")
        return "Error Subject", "Error Content"

def show_gainsight_structure(subject, content):
    """Show the final Gainsight structure"""
    print_step(4, "GAINSIGHT OUTPUT STRUCTURE")
    
    gainsight_activity = {
        "lastModifiedByUser": {
            "gsId": "1P01E316G9DAPFOLE6SOOUG71XRMN5F3PLER",
            "name": "Ram Prasad",
            "email": "<EMAIL>"
        },
        "note": {
            "customFields": {
                "internalAttendees": [
                    {
                        "id": "1P01E316G9DAPFOLE6SOOUG71XRMN5F3PLER",
                        "name": "Ram Prasad",
                        "email": "<EMAIL>"
                    }
                ],
                "Ant__Touchpoint_Reason__c": "1I00J1INQCQ6GQ3T2MWULITYE9ASDFPQ2KD3",
                "Ant__Flow_Type__c": "1I00EBMOY66ROGCBY63I51PAXG5OJRPK37AQ"
            },
            "type": "EMAIL",
            "subject": subject,
            "activityDate": int(datetime.now().timestamp() * 1000),
            "content": f"<p>{content}</p>",
            "plainText": content
        },
        "meta": {
            "activityTypeId": "c81eeccf-2aa1-45bc-be71-5377f148e1e9",
            "source": "ICICI_MIGRATION",
            "hasTask": False,
            "emailSent": False,
            "systemType": "GAINSIGHT"
        },
        "author": {
            "id": "1P01E316G9DAPFOLE6SOOUG71XRMN5F3PLER",
            "name": "Ram Prasad",
            "email": "<EMAIL>"
        },
        "contexts": [
            {
                "id": "1P02IPMAEL4M3CQGY4K5FHU22D2HHT11KCKU",
                "obj": "Company",
                "lbl": "ICICI",
                "dsp": True,
                "base": True
            }
        ]
    }
    
    print("📋 Complete Gainsight activity structure:")
    print(json.dumps(gainsight_activity, indent=2))
    
    print(f"\n🔍 Key fields:")
    print(f"   • Subject: {subject}")
    print(f"   • Content: {content}")
    print(f"   • Type: EMAIL")
    print(f"   • Source: ICICI_MIGRATION")
    print(f"   • Company: ICICI")

def show_conversion_summary():
    """Show conversion process summary"""
    print_step(5, "CONVERSION PROCESS SUMMARY")
    
    print("🔄 The conversion process:")
    print("   1. 📁 Load ICICI.json file")
    print("   2. 🔍 Parse each activity")
    print("   3. 🗺️  Map activity types to Gainsight IDs")
    print("   4. 🤖 Use AI to generate meaningful subject/content")
    print("   5. 🏗️  Build complete Gainsight activity structure")
    print("   6. 💾 Save converted activities to JSON file")
    
    print("\n⚡ Benefits:")
    print("   • 🧠 AI-generated meaningful descriptions")
    print("   • 🎯 Proper activity type mapping")
    print("   • 👤 Correct user attribution")
    print("   • 🏢 Linked to ICICI company context")
    print("   • 📊 Batch processing of all activities")
    
    print("\n🚀 To run the actual conversion:")
    print("   python run_conversion.py")

async def main():
    """Main demonstration"""
    print("🔄 ICICI TO GAINSIGHT CONVERSION PROCESS DEMO")
    print("=" * 60)
    
    # Step 1: Show ICICI data
    icici_data = show_icici_data_structure()
    
    # Step 2: Show mapping
    show_mapping_process()
    
    # Step 3: Show AI generation
    subject, content = await show_ai_generation()
    
    # Step 4: Show Gainsight structure
    show_gainsight_structure(subject, content)
    
    # Step 5: Show summary
    show_conversion_summary()
    
    print("\n🎉 Demo completed!")
    print("The converter is ready to process your ICICI data.")

if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\n👋 Demo interrupted")
    except Exception as e:
        print(f"❌ Demo error: {e}")

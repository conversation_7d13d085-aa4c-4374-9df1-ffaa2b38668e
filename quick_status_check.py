#!/usr/bin/env python3
"""
Simple System Status Check
Quick verification of system functionality and migration status
"""

import json
import sys
import os
from pathlib import Path

def check_configuration():
    """Check if configuration is working"""
    print("🔧 Checking Configuration...")
    try:
        from config import config
        print(f"  ✅ Configuration loaded successfully")
        print(f"  ✅ OpenRouter API Key: {'Present' if config.llm.openrouter_api_key else 'Missing'}")
        print(f"  ✅ HuggingFace API Key: {'Present' if config.llm.huggingface_api_key else 'Missing'}")
        print(f"  ✅ Primary coding model: {config.llm.coding_model}")
        print(f"  ✅ Primary reasoning model: {config.llm.reasoning_model}")
        return True
    except Exception as e:
        print(f"  ❌ Configuration error: {e}")
        return False

def check_data_files():
    """Check ICICI migration data files"""
    print("\n📊 Checking ICICI Migration Data...")
    
    files_to_check = {
        "ICICI Data": "/Users/<USER>/Desktop/totango/ICICI.json",
        "ID Mapping": "/Users/<USER>/Desktop/totango/ID.json", 
        "Flow Types": "/Users/<USER>/Desktop/totango/flowtype.json",
        "Gainsight Template": "/Users/<USER>/Desktop/totango/Gainsight_payload.json"
    }
    
    file_status = {}
    for name, path in files_to_check.items():
        exists = Path(path).exists()
        file_status[name] = exists
        print(f"  {'✅' if exists else '❌'} {name}: {'Found' if exists else 'Missing'}")
        
        if exists and name == "ICICI Data":
            try:
                with open(path, 'r') as f:
                    data = json.load(f)
                    count = len(data) if isinstance(data, list) else 1
                    print(f"      📊 Records found: {count}")
            except Exception as e:
                print(f"      ⚠️  Error reading file: {e}")
    
    return file_status

def check_conversion_status():
    """Check if ICICI conversion has been done"""
    print("\n🔄 Checking Conversion Status...")
    
    conversion_files = {
        "Converted Data": "/Users/<USER>/Desktop/wildweasel/Browser/data/icici_gainsight_ready.json",
        "With IDs": "/Users/<USER>/Desktop/wildweasel/Browser/data/icici_gainsight_with_ids.json",
        "Final Migration": "/Users/<USER>/Desktop/wildweasel/Browser/data/icici_final_migration.json"
    }
    
    conversion_status = {}
    for name, path in conversion_files.items():
        exists = Path(path).exists()
        conversion_status[name] = exists
        print(f"  {'✅' if exists else '❌'} {name}: {'Ready' if exists else 'Not Created'}")
        
        if exists:
            try:
                with open(path, 'r') as f:
                    data = json.load(f)
                    count = len(data) if isinstance(data, list) else 1
                    print(f"      📊 Records: {count}")
                    
                    # Check for IDs if this is the "With IDs" file
                    if "with_ids" in path.lower() and isinstance(data, list):
                        ids_present = sum(1 for item in data if item.get("id"))
                        print(f"      🆔 Records with IDs: {ids_present}")
                        
            except Exception as e:
                print(f"      ⚠️  Error reading file: {e}")
    
    return conversion_status

def check_system_readiness():
    """Check overall system readiness"""
    print("\n🎯 System Readiness Assessment...")
    
    # Check Python environment
    python_version = sys.version_info
    print(f"  ✅ Python Version: {python_version.major}.{python_version.minor}.{python_version.micro}")
    
    # Check key modules
    modules_to_check = ["json", "pathlib", "asyncio", "httpx", "requests"]
    missing_modules = []
    
    for module in modules_to_check:
        try:
            __import__(module)
            print(f"  ✅ {module}: Available")
        except ImportError:
            print(f"  ❌ {module}: Missing")
            missing_modules.append(module)
    
    return len(missing_modules) == 0

def suggest_next_steps():
    """Suggest next steps based on current status"""
    print("\n💡 Recommended Next Steps:")
    
    # Check if conversion needs to be done
    conversion_ready = Path("/Users/<USER>/Desktop/wildweasel/Browser/data/icici_gainsight_ready.json").exists()
    
    if not conversion_ready:
        print("  1. 🔄 Run ICICI to Gainsight conversion:")
        print("     python3 icici_to_gainsight_converter.py")
    else:
        print("  1. ✅ Conversion completed")
    
    # Check if IDs need to be generated
    ids_ready = Path("/Users/<USER>/Desktop/wildweasel/Browser/data/icici_gainsight_with_ids.json").exists()
    
    if not ids_ready:
        print("  2. 🆔 Generate Gainsight IDs:")
        print("     python3 fix_gainsight_ids.py")
    else:
        print("  2. ✅ IDs generated")
    
    # Check if final migration is ready
    final_ready = Path("/Users/<USER>/Desktop/wildweasel/Browser/data/icici_final_migration.json").exists()
    
    if not final_ready:
        print("  3. 📦 Create final migration payload:")
        print("     python3 fix_gainsight_ids.py (option 2)")
    else:
        print("  3. ✅ Final payload ready")
        print("  4. 🚀 Ready to post to Gainsight API!")
    
    print("\n  🤖 For browser automation testing:")
    print("     python3 -c \"from enhanced_llm_client import test_llm_client; import asyncio; asyncio.run(test_llm_client())\"")

def main():
    """Main status check"""
    print("🔍 SYSTEM STATUS CHECK")
    print("=" * 50)
    
    config_ok = check_configuration()
    data_files_ok = check_data_files()
    conversion_status = check_conversion_status()
    system_ok = check_system_readiness()
    
    print(f"\n📋 SUMMARY")
    print(f"  {'✅' if config_ok else '❌'} Configuration: {'OK' if config_ok else 'Issues'}")
    print(f"  {'✅' if all(data_files_ok.values()) else '⚠️'} Data Files: {sum(data_files_ok.values())}/{len(data_files_ok)} present")
    print(f"  {'✅' if any(conversion_status.values()) else '❌'} Conversion: {'In Progress' if any(conversion_status.values()) else 'Not Started'}")
    print(f"  {'✅' if system_ok else '❌'} System: {'Ready' if system_ok else 'Missing Dependencies'}")
    
    overall_status = config_ok and system_ok and any(data_files_ok.values())
    print(f"\n🎯 Overall Status: {'✅ READY' if overall_status else '⚠️ NEEDS ATTENTION'}")
    
    suggest_next_steps()

if __name__ == "__main__":
    main()

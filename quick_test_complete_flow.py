#!/usr/bin/env python3
"""
Quick Test - Complete Flow with Flow Type Fix
Run the complete mapping and CSV generation with fixed flow type mapping
"""

import json
import pandas as pd
import re
from pathlib import Path
from collections import Counter
from datetime import datetime

def run_complete_test():
    """Run the complete test with flow type mapping fix."""
    print("🚀 QUICK TEST - COMPLETE FLOW WITH FLOW TYPE FIX")
    print("=" * 60)
    
    # Step 1: Check source files
    print("📋 Step 1: Checking source files...")
    
    icici_file = "/Users/<USER>/Desktop/totango/ICICI_processed.json"
    flowtype_file = "/Users/<USER>/Desktop/totango/flowtype.json"
    
    if not Path(icici_file).exists():
        print(f"❌ ICICI file not found: {icici_file}")
        return
    
    if not Path(flowtype_file).exists():
        print(f"❌ Flow type file not found: {flowtype_file}")
        return
    
    print(f"✅ Found ICICI file: {Path(icici_file).name}")
    print(f"✅ Found flow type file: {Path(flowtype_file).name}")
    
    # Step 2: Load and fix flow type mapping
    print(f"\n📋 Step 2: Loading flow type mapping...")
    
    with open(flowtype_file, 'r') as f:
        flowtype_data = json.load(f)
    
    # Fix the mapping - use activity_type_id instead of id
    flow_mapping = {}
    for item in flowtype_data:
        flow_id = item.get('activity_type_id', '')  # This is the correct field!
        display_name = item.get('display_name', '')
        if flow_id and display_name:
            # Clean the display name
            cleaned_name = clean_flow_type_name(display_name)
            flow_mapping[flow_id] = cleaned_name
    
    print(f"✅ Created flow type mapping with {len(flow_mapping)} entries:")
    for flow_id, name in list(flow_mapping.items())[:5]:
        print(f"   • {flow_id} → {name}")
    
    # Step 3: Load ICICI activities and check for flow types
    print(f"\n📋 Step 3: Analyzing ICICI activities for flow types...")
    
    with open(icici_file, 'r') as f:
        activities = json.load(f)
    
    print(f"✅ Loaded {len(activities)} activities")
    
    # Check for flow type fields in activities
    flow_type_fields_found = Counter()
    flow_type_values_found = Counter()
    activities_with_flow_type = 0
    
    for activity in activities:
        properties = activity.get('properties', {})
        
        # Check for various flow type field names
        for field_name in ['flow_type', 'activity_type_id', 'flow_type_id', 'type_id']:
            if field_name in properties:
                flow_type_fields_found[field_name] += 1
                value = properties[field_name]
                flow_type_values_found[str(value)] += 1
                activities_with_flow_type += 1
                break
    
    print(f"📊 Activities with flow type fields: {activities_with_flow_type}")
    
    if flow_type_fields_found:
        print(f"📋 Flow type fields found:")
        for field, count in flow_type_fields_found.most_common():
            print(f"   • {field}: {count} activities")
        
        print(f"📋 Flow type values found:")
        for value, count in list(flow_type_values_found.most_common())[:5]:
            mapped_name = flow_mapping.get(value, f"Unknown ({value})")
            print(f"   • {value} → {mapped_name}: {count} activities")
    else:
        print("⚠️  No flow type fields found in activities")
    
    # Step 4: Create a sample enhanced CSV with flow type mapping
    print(f"\n📋 Step 4: Creating sample enhanced CSV...")
    
    csv_rows = []
    
    for i, activity in enumerate(activities[:10], 1):  # Just first 10 for testing
        properties = activity.get('properties', {})
        
        # Get flow type
        flow_type = "Standard"  # Default
        for field_name in ['flow_type', 'activity_type_id', 'flow_type_id', 'type_id']:
            if field_name in properties:
                flow_type_id = properties[field_name]
                if flow_type_id in flow_mapping:
                    flow_type = flow_mapping[flow_type_id]
                    break
        
        # Get other data
        gainsight_activity_type = properties.get('gainsight_activity_type', 'Update')
        subject = f"ICICI: {properties.get('name', properties.get('display_name', 'Activity'))}"
        
        csv_row = {
            "Row Number": i,
            "Subject": subject,
            "Activity Type": gainsight_activity_type,
            "Flow Type": flow_type,
            "Author Name": "Ram Prasad",
            "Company": "ICICI"
        }
        
        csv_rows.append(csv_row)
    
    # Save sample CSV
    df = pd.DataFrame(csv_rows)
    sample_csv = "sample_flow_type_test.csv"
    df.to_csv(sample_csv, index=False)
    
    print(f"✅ Created sample CSV: {sample_csv}")
    print(f"📊 Sample data:")
    print(df.to_string(index=False))
    
    # Step 5: Show how to run the full process
    print(f"\n📋 Step 5: How to run the complete process...")
    
    print(f"🚀 TO RUN THE COMPLETE ENHANCED CSV EXPORT:")
    print(f"   1. First, run the mapping (if not done already):")
    print(f"      python complete_gainsight_mapping.py")
    print(f"")
    print(f"   2. Then run the enhanced CSV exporter:")
    print(f"      python enhanced_csv_exporter.py")
    print(f"      (Choose option 1 for demo data or 2 for real data)")
    print(f"")
    print(f"   3. Verify the results:")
    print(f"      python verify_enhanced_csv.py")
    
    return True

def clean_flow_type_name(flow_type_name: str) -> str:
    """Clean flow type name by removing timestamps and numbers."""
    if not flow_type_name:
        return ""
    
    # Remove timestamp patterns like _1560979263618
    cleaned = re.sub(r'_\d{10,}', '', flow_type_name)
    
    # Remove trailing numbers like _1, _2, etc.
    cleaned = re.sub(r'_\d+$', '', cleaned)
    
    # Replace underscores with spaces and title case
    cleaned = cleaned.replace('_', ' ').title()
    
    return cleaned

def main():
    """Main test function."""
    success = run_complete_test()
    
    if success:
        print(f"\n🎉 QUICK TEST COMPLETED SUCCESSFULLY!")
        print(f"✅ Flow type mapping is working")
        print(f"✅ Sample CSV generated")
        print(f"✅ Ready to run full enhanced CSV export")
    else:
        print(f"\n❌ Test failed - check error messages above")

if __name__ == "__main__":
    main()

"""
Simple example of using the Browser Automation Agent
"""
import asyncio
import sys
import os

# Add the parent directory to the path so we can import our modules
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from orchestrator import execute_task, get_status

async def simple_example():
    """Simple example: search on Google"""
    print("🚀 Starting simple browser automation example...")
    
    # Execute a simple task
    result = await execute_task(
        task_description="Go to google.com and search for 'browser automation with LLM'",
        context={"example": True},
        learn_from_execution=True,
        record_session=True
    )
    
    # Print results
    if result["success"]:
        print("✅ Task completed successfully!")
        print(f"   • Execution time: {result.get('execution_time', 0):.2f} seconds")
        print(f"   • Steps taken: {result.get('steps_taken', 0)}")
        if result.get('recording_path'):
            print(f"   • Recording saved to: {result['recording_path']}")
    else:
        print("❌ Task failed!")
        print(f"   • Error: {result.get('error', 'Unknown error')}")
    
    return result

async def form_filling_example():
    """Example: filling out a form"""
    print("🚀 Starting form filling example...")
    
    result = await execute_task(
        task_description="Go to httpbin.org/forms/post and fill out the form with test data",
        context={
            "form_data": {
                "name": "Test User",
                "email": "<EMAIL>",
                "message": "This is a test message from the browser automation agent"
            }
        }
    )
    
    return result

async def memory_demonstration():
    """Demonstrate the memory system"""
    print("🧠 Demonstrating memory capabilities...")
    
    # First task - the agent will learn from this
    print("\n1. First task - teaching the agent about a website:")
    result1 = await execute_task(
        "Go to example.com and explore the page structure"
    )
    
    # Second similar task - agent should use memory from first task
    print("\n2. Second task - agent should remember the website:")
    result2 = await execute_task(
        "Go back to example.com and describe what you remember about it"
    )
    
    # Show system status to see memory in action
    print("\n3. System status showing memory usage:")
    status = await get_status()
    print(f"   • Memory blocks: {status['memory_system']['memory_blocks_count']}")
    print(f"   • Session history: {status['orchestrator']['session_history_count']}")
    
    return [result1, result2]

async def main():
    """Run all examples"""
    print("🌟 Browser Automation Agent Examples")
    print("=" * 50)
    
    try:
        # Example 1: Simple search
        await simple_example()
        
        print("\n" + "="*50)
        
        # Example 2: Form filling
        await form_filling_example()
        
        print("\n" + "="*50)
        
        # Example 3: Memory demonstration
        await memory_demonstration()
        
        print("\n✅ All examples completed!")
        
    except Exception as e:
        print(f"❌ Error running examples: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(main())

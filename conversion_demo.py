#!/usr/bin/env python3
"""
ICICI to Gainsight Conversion Demo
Shows step-by-step how the conversion works and allows you to run it
"""

import json
import asyncio
from datetime import datetime
from pathlib import Path

from gemini_client import GeminiClient
from icici_to_gainsight_converter import ICICIToGainsightConverter

def print_banner():
    """Print demo banner"""
    banner = """
╔══════════════════════════════════════════════════════════════════════════════╗
║                🔄 ICICI TO GAINSIGHT CONVERSION DEMO                         ║
║                                                                              ║
║  This script shows you exactly how the conversion works:                     ║
║  1. 📁 Load ICICI data                                                       ║
║  2. 🤖 Use Gemini AI to understand the data                                  ║
║  3. 🔄 Convert to Gainsight format                                           ║
║  4. 💾 Save the results                                                      ║
╚══════════════════════════════════════════════════════════════════════════════╝
    """
    print(banner)

def show_sample_icici_data():
    """Show what ICICI data looks like"""
    print("\n📊 SAMPLE ICICI DATA STRUCTURE:")
    print("=" * 50)
    
    sample_data = {
        "id": "activity_123456",
        "timestamp": *************,  # Jan 1, 2022
        "type": "campaign_touch",
        "properties": {
            "activity_type_id": "onboarding_101",
            "name": "Welcome Email Campaign",
            "display_name": "Customer Onboarding",
            "description": "Initial welcome email sent to new customers",
            "campaign_id": "camp_789",
            "status": "completed"
        }
    }
    
    print(json.dumps(sample_data, indent=2))
    print("\n🔍 Key fields:")
    print("  • id: Unique activity identifier")
    print("  • timestamp: When the activity occurred")
    print("  • type: Type of activity (campaign_touch, webhook, etc.)")
    print("  • properties: Additional details about the activity")

def show_sample_gainsight_output():
    """Show what Gainsight output looks like"""
    print("\n📋 SAMPLE GAINSIGHT OUTPUT STRUCTURE:")
    print("=" * 50)
    
    sample_output = {
        "note": {
            "subject": "Campaign: Welcome Email Campaign",
            "content": "<p>Welcome Email Campaign was executed for ICICI Bank customer onboarding.</p>",
            "type": "EMAIL",
            "activityDate": *************
        },
        "meta": {
            "activityTypeId": "c81eeccf-2aa1-45bc-be71-5377f148e1e9",
            "source": "ICICI_MIGRATION"
        },
        "contexts": [
            {
                "id": "1P02IPMAEL4M3CQGY4K5FHU22D2HHT11KCKU",
                "obj": "Company",
                "lbl": "ICICI"
            }
        ]
    }
    
    print(json.dumps(sample_output, indent=2))
    print("\n🔍 Key transformations:")
    print("  • subject: AI-generated meaningful title")
    print("  • content: AI-generated description")
    print("  • activityTypeId: Mapped to Gainsight activity type")
    print("  • contexts: Linked to ICICI company record")

async def demo_ai_content_generation():
    """Demo how AI generates content"""
    print("\n🤖 AI CONTENT GENERATION DEMO:")
    print("=" * 50)
    
    sample_icici_activity = {
        "type": "campaign_touch",
        "properties": {
            "name": "Welcome Email Campaign",
            "display_name": "Customer Onboarding",
            "description": "Initial welcome email sent to new customers"
        }
    }
    
    print("📥 Input ICICI activity:")
    print(json.dumps(sample_icici_activity, indent=2))
    
    print("\n🧠 Asking Gemini AI to generate content...")
    
    async with GeminiClient() as client:
        prompt = f"""
        Convert this ICICI activity data into a meaningful Gainsight activity:
        
        Activity Type: {sample_icici_activity.get('type', '')}
        Properties: {json.dumps(sample_icici_activity.get('properties', {}), indent=2)}
        
        Generate:
        1. A clear, professional subject line (max 100 characters)
        2. A detailed content description (2-3 sentences explaining what happened)
        
        Format your response as:
        SUBJECT: [subject line]
        CONTENT: [content description]
        """
        
        try:
            response = await client.complete(
                prompt,
                task_type="general",
                max_tokens=300
            )
            
            if response.success:
                print(f"✅ AI Response (took {response.latency:.2f}s):")
                print(response.content)
                
                # Parse the response
                lines = response.content.strip().split('\n')
                subject = "Generated Subject"
                content = "Generated Content"
                
                for line in lines:
                    if line.startswith("SUBJECT:"):
                        subject = line.replace("SUBJECT:", "").strip()
                    elif line.startswith("CONTENT:"):
                        content = line.replace("CONTENT:", "").strip()
                
                print(f"\n📝 Extracted:")
                print(f"  Subject: {subject}")
                print(f"  Content: {content}")
                
            else:
                print(f"❌ AI failed: {response.error}")
                
        except Exception as e:
            print(f"❌ Error: {e}")

def show_mapping_tables():
    """Show the mapping tables used for conversion"""
    print("\n🗺️  MAPPING TABLES:")
    print("=" * 50)
    
    print("📋 Activity Type Mapping:")
    activity_mapping = {
        "onboarding_101": "c81eeccf-2aa1-45bc-be71-5377f148e1e9",
        "adoption": "68c0d13a-40f1-47e4-9bb4-3d1fb6a16515", 
        "intelligence_1561140678082": "93b4649c-8459-4f56-be3f-be75f7506ee0"
    }
    for icici_type, gainsight_id in activity_mapping.items():
        print(f"  {icici_type} → {gainsight_id}")
    
    print("\n📋 Note Type Mapping:")
    note_mapping = {
        "webhook": "EMAIL",
        "campaign_touch": "EMAIL",
        "account_alert": "INTERNAL_NOTE",
        "default": "INTERNAL_NOTE"
    }
    for icici_type, gainsight_type in note_mapping.items():
        print(f"  {icici_type} → {gainsight_type}")

async def run_sample_conversion():
    """Run a sample conversion"""
    print("\n🔄 RUNNING SAMPLE CONVERSION:")
    print("=" * 50)
    
    # Create sample ICICI data
    sample_activities = [
        {
            "id": "act_001",
            "timestamp": int(datetime.now().timestamp() * 1000),
            "type": "campaign_touch",
            "properties": {
                "activity_type_id": "onboarding_101",
                "name": "Welcome Email",
                "display_name": "Customer Onboarding"
            }
        },
        {
            "id": "act_002", 
            "timestamp": int(datetime.now().timestamp() * 1000),
            "type": "webhook",
            "properties": {
                "activity_type_id": "adoption",
                "name": "Feature Usage Alert",
                "display_name": "Product Adoption"
            }
        }
    ]
    
    print(f"📥 Converting {len(sample_activities)} sample activities...")
    
    converter = ICICIToGainsightConverter()
    converted_activities = []
    
    for i, activity in enumerate(sample_activities):
        print(f"\n🔄 Converting activity {i+1}/{len(sample_activities)}")
        print(f"   Type: {activity['type']}")
        print(f"   Name: {activity['properties'].get('name', 'Unknown')}")
        
        try:
            converted = await converter.convert_activity(activity)
            if converted:
                converted_activities.append(converted)
                print(f"   ✅ Success!")
                print(f"   📝 Subject: {converted['note']['subject']}")
                print(f"   📋 Type: {converted['note']['type']}")
            else:
                print(f"   ❌ Failed to convert")
                
        except Exception as e:
            print(f"   ❌ Error: {e}")
    
    print(f"\n📊 CONVERSION SUMMARY:")
    print(f"   • Input activities: {len(sample_activities)}")
    print(f"   • Successfully converted: {len(converted_activities)}")
    print(f"   • Success rate: {len(converted_activities)/len(sample_activities)*100:.1f}%")
    
    # Save sample output
    if converted_activities:
        output_file = "data/sample_conversion_output.json"
        with open(output_file, 'w') as f:
            json.dump(converted_activities, f, indent=2)
        print(f"   💾 Sample output saved to: {output_file}")

def check_input_file():
    """Check if the actual ICICI input file exists"""
    print("\n📁 CHECKING INPUT FILE:")
    print("=" * 50)
    
    input_file = "data/totango/ICICI.json"
    
    if Path(input_file).exists():
        print(f"✅ Found input file: {input_file}")
        
        try:
            with open(input_file, 'r') as f:
                data = json.load(f)
            
            print(f"📊 File contains {len(data)} activities")
            
            if len(data) > 0:
                print("\n🔍 First activity preview:")
                first_activity = data[0]
                print(f"   ID: {first_activity.get('id', 'N/A')}")
                print(f"   Type: {first_activity.get('type', 'N/A')}")
                print(f"   Properties: {len(first_activity.get('properties', {}))}")
                
                # Show activity types distribution
                activity_types = {}
                for activity in data[:10]:  # Check first 10
                    act_type = activity.get('type', 'unknown')
                    activity_types[act_type] = activity_types.get(act_type, 0) + 1
                
                print(f"\n📈 Activity types (first 10):")
                for act_type, count in activity_types.items():
                    print(f"   {act_type}: {count}")
            
            return True
            
        except Exception as e:
            print(f"❌ Error reading file: {e}")
            return False
    else:
        print(f"❌ Input file not found: {input_file}")
        print("   Please ensure ICICI.json is in the data/totango/ directory")
        return False

async def main():
    """Main demo function"""
    print_banner()
    
    # Step 1: Show data structures
    show_sample_icici_data()
    input("\n⏸️  Press Enter to continue...")
    
    show_sample_gainsight_output()
    input("\n⏸️  Press Enter to continue...")
    
    # Step 2: Show mapping tables
    show_mapping_tables()
    input("\n⏸️  Press Enter to continue...")
    
    # Step 3: Demo AI content generation
    await demo_ai_content_generation()
    input("\n⏸️  Press Enter to continue...")
    
    # Step 4: Run sample conversion
    await run_sample_conversion()
    input("\n⏸️  Press Enter to continue...")
    
    # Step 5: Check actual input file
    file_exists = check_input_file()
    
    if file_exists:
        print("\n🚀 READY TO RUN FULL CONVERSION!")
        print("=" * 50)
        print("To convert your actual ICICI data, run:")
        print("   python run_conversion.py")
    else:
        print("\n⚠️  SETUP NEEDED:")
        print("=" * 50)
        print("1. Place your ICICI.json file in data/totango/")
        print("2. Then run: python run_conversion.py")
    
    print("\n🎉 Demo completed!")

if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\n👋 Demo interrupted")
    except Exception as e:
        print(f"❌ Demo error: {e}")

"""
Configuration management for the Browser Automation Agent
"""
import os
from dataclasses import dataclass, field
from typing import Dict, List, Optional, Any
from pathlib import Path

# Load environment variables from .env file
try:
    from dotenv import load_dotenv
    load_dotenv()
except ImportError:
    print("Warning: python-dotenv not installed. Environment variables from .env file won't be loaded.")
    pass

@dataclass
class LLMConfig:
    """Enhanced LLM configuration with free model support"""
    # API Keys - Using Gemini as primary
    google_api_key: str = field(default_factory=lambda: os.getenv("GOOGLE_API_KEY", "AIzaSyCtFLDVZGEariTD5Ui5ixTQ_C_EeH_OCJ8"))
    openai_api_key: str = field(default_factory=lambda: os.getenv("OPENAI_API_KEY", ""))
    anthropic_api_key: str = field(default_factory=lambda: os.getenv("ANTHROPIC_API_KEY", ""))
    letta_api_key: str = field(default_factory=lambda: os.getenv("LETTA_API_KEY", ""))
    openrouter_api_key: str = field(default_factory=lambda: os.getenv("OPENROUTER_API_KEY", "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"))
    huggingface_api_key: str = field(default_factory=lambda: os.getenv("HUGGINGFACE_API_KEY", "*************************************"))

    # Enhanced Model Configuration (Using Gemini as primary)
    coding_model: str = "gemini-1.5-flash"                       # Gemini for coding
    reasoning_model: str = "gemini-1.5-pro"                      # Gemini Pro for reasoning
    natural_language_model: str = "gemini-1.5-flash"             # Gemini for NL
    multimodal_model: str = "gemini-1.5-flash"                   # Gemini for vision
    general_model: str = "gemini-1.5-flash"                      # Gemini as default

    # Fallback models (prioritize Gemini, then free models)
    fallback_models: List[str] = field(default_factory=lambda: [
        "gemini-1.5-flash",                                       # Google Gemini (primary)
        "gemini-1.5-pro",                                         # Google Gemini Pro
        "deepseek/deepseek-chat-v3-0324:free",                    # Free OpenRouter (FAST)
        "meta-llama/llama-3.3-70b-instruct:free",                # Free OpenRouter (SMART)
        "google/gemini-2.0-flash-exp:free",                       # Free OpenRouter Gemini
        "nvidia/llama-3.1-nemotron-ultra-253b-v1:free",          # Free OpenRouter
    ])

    # Legacy model support - use Gemini as primary
    primary_model: str = "gemini-1.5-flash"                      # Default to Gemini
    fallback_model: str = "gemini-1.5-pro"                       # Fallback to Gemini Pro
    anthropic_model: str = "claude-3-5-sonnet-20241022"

    # API Configuration
    openrouter_base_url: str = "https://openrouter.ai/api/v1"
    huggingface_base_url: str = "https://api-inference.huggingface.co/models"

    # Model parameters
    temperature: float = 0.0
    max_tokens: int = 4000
    timeout: int = 120
    max_retries: int = 3

    # Task-specific model selection
    enable_task_aware_selection: bool = True
    auto_fallback: bool = True

@dataclass
class MemoryConfig:
    """Enhanced memory system configuration with Letta integration"""
    # Letta server configuration
    letta_server_url: str = "https://api.letta.com"
    letta_api_key: str = field(default_factory=lambda: os.getenv("LETTA_API_KEY", ""))
    letta_enabled: bool = True
    letta_agent_name: str = "browser_automation_agent"

    # Multi-tier memory architecture
    enable_persistent_memory: bool = True
    enable_working_memory: bool = True
    enable_semantic_memory: bool = True
    enable_knowledge_graph: bool = True

    # Memory consolidation settings
    memory_consolidation_interval: int = 3600  # 1 hour in seconds
    max_context_length: int = 8000
    memory_search_results: int = 5

    # Database settings
    postgres_url: str = field(default_factory=lambda: os.getenv("POSTGRES_URL", "postgresql://localhost/agent_memory"))
    vector_db_path: str = "./data/vector_store"

    # Letta memory configuration
    letta_memory_blocks: List[str] = field(default_factory=lambda: [
        "user_profile", "task_history", "website_knowledge", "error_patterns",
        "browser_automation_patterns", "selector_knowledge", "success_strategies"
    ])

    # Knowledge graph settings
    knowledge_graph_path: str = "./data/knowledge_graph.pkl"
    max_entities: int = 10000
    relationship_types: List[str] = field(default_factory=lambda: [
        "performs", "learns_from", "navigates_to", "interacts_with",
        "fails_at", "succeeds_at", "depends_on", "similar_to"
    ])

    # Legacy memory block settings (keep for compatibility)
    core_memory_blocks: List[str] = field(default_factory=lambda: ["user_profile", "task_history", "website_knowledge", "error_patterns"])

@dataclass
class WorkflowConfig:
    """LangGraph workflow orchestration configuration"""
    # LangGraph settings
    enable_langgraph: bool = True
    max_workflow_steps: int = 100
    workflow_timeout: int = 300  # 5 minutes

    # Workflow node configurations
    enable_memory_node: bool = True
    enable_planning_node: bool = True
    enable_execution_node: bool = True
    enable_evaluation_node: bool = True
    enable_learning_node: bool = True

    # Workflow patterns
    default_workflow_pattern: str = "memory_recall -> task_planning -> browser_action -> result_evaluation -> memory_update"

    # Conditional workflows
    enable_conditional_routing: bool = True
    error_recovery_enabled: bool = True
    adaptive_planning: bool = True

    # Checkpointing and persistence
    enable_checkpoints: bool = True
    checkpoint_interval: int = 5  # steps
    workflow_state_path: str = "./data/workflow_states"

    # Multi-agent coordination
    enable_multi_agent: bool = False
    max_concurrent_agents: int = 3

@dataclass
class BrowserConfig:
    """Enhanced browser automation configuration with Browser-Use integration"""
    # Browser-Use configuration
    enable_browser_use: bool = True
    browser_use_max_steps: int = 50
    browser_use_enable_memory: bool = True
    browser_use_memory_interval: int = 15
    browser_use_use_vision: bool = True
    browser_use_save_conversation: bool = True

    # Traditional browser settings (kept for compatibility)
    headless: bool = False
    browser_timeout: int = 30000  # 30 seconds
    page_load_timeout: int = 30000

    # Automation settings
    screenshot_on_action: bool = True
    record_video: bool = True
    save_har: bool = True

    # Browser profiles
    user_data_dir: Optional[str] = None
    executable_path: Optional[str] = None

    # Performance settings
    disable_images: bool = False
    disable_javascript: bool = False

    # Security settings
    allowed_domains: List[str] = field(default_factory=list)
    blocked_domains: List[str] = field(default_factory=list)

    # Browser-Use specific settings
    browser_use_controller_actions: List[str] = field(default_factory=lambda: [
        "search_google", "extract_structured_data", "save_to_memory",
        "analyze_page_content", "generate_summary"
    ])

    # Multi-modal capabilities
    enable_vision_analysis: bool = True
    enable_ocr: bool = True
    enable_content_extraction: bool = True

@dataclass
class AgentConfig:
    """Agent behavior configuration"""
    # Task execution
    max_retries: int = 3
    retry_delay: float = 2.0
    max_steps_per_task: int = 50

    # Learning and adaptation
    learning_rate: float = 0.1
    adaptation_threshold: float = 0.7
    success_threshold: float = 0.8

    # Memory management
    enable_memory: bool = True
    memory_interval: int = 15  # steps

    # Planning and reasoning
    enable_planning: bool = True
    planning_interval: int = 5  # steps
    max_planning_depth: int = 3

@dataclass
class LoggingConfig:
    """Logging configuration"""
    log_level: str = "INFO"
    log_file: str = "logs/agent.log"
    max_log_size: int = 10 * 1024 * 1024  # 10MB
    backup_count: int = 5

    # Component-specific logging
    browser_log_level: str = "DEBUG"
    memory_log_level: str = "INFO"
    agent_log_level: str = "DEBUG"

@dataclass
class StorageConfig:
    """Storage and data management configuration with enhanced recording"""
    # Data directories
    data_dir: str = "./data"
    logs_dir: str = "./logs"
    screenshots_dir: str = "./data/screenshots"
    recordings_dir: str = "./data/recordings"
    exports_dir: str = "./data/exports"

    # Enhanced Recording Configuration
    enable_comprehensive_recording: bool = field(default_factory=lambda: os.getenv("ENABLE_COMPREHENSIVE_RECORDING", "true").lower() == "true")
    recording_quality: str = field(default_factory=lambda: os.getenv("RECORDING_QUALITY", "high"))
    save_screenshots: bool = field(default_factory=lambda: os.getenv("SAVE_SCREENSHOTS", "true").lower() == "true")
    save_video: bool = field(default_factory=lambda: os.getenv("SAVE_VIDEO", "true").lower() == "true")
    record_network_requests: bool = field(default_factory=lambda: os.getenv("RECORD_NETWORK_REQUESTS", "true").lower() == "true")
    record_console_logs: bool = field(default_factory=lambda: os.getenv("RECORD_CONSOLE_LOGS", "true").lower() == "true")

    # Recording settings
    screenshot_interval: int = 1000  # Take screenshot every 1 second during automation
    max_screenshots_per_session: int = 1000
    video_fps: int = 2  # 2 frames per second for video recording
    compress_recordings: bool = True
    recording_format: str = "json"  # json, csv, or both

    # File management
    max_screenshot_age_days: int = 30
    max_recording_age_days: int = 7
    cleanup_interval_hours: int = 24

    # Step recording granularity
    record_mouse_movements: bool = True
    record_keyboard_inputs: bool = True
    record_page_changes: bool = True
    record_element_interactions: bool = True
    record_timing_data: bool = True
    record_error_details: bool = True

@dataclass
class SecurityConfig:
    """Security and privacy configuration"""
    # Data protection
    encrypt_memory: bool = True
    anonymize_screenshots: bool = False

    # Access control
    api_key: Optional[str] = None
    require_authentication: bool = False

    # Sensitive data handling
    mask_sensitive_fields: bool = True
    sensitive_field_patterns: List[str] = field(default_factory=lambda: [
        r"password", r"token", r"key", r"secret", r"api", r"auth"
    ])

@dataclass
class IntegrationConfig:
    """External service integration configuration"""
    # Vector databases
    pinecone_api_key: str = field(default_factory=lambda: os.getenv("PINECONE_API_KEY", ""))
    pinecone_environment: str = field(default_factory=lambda: os.getenv("PINECONE_ENVIRONMENT", ""))

    # Redis (for caching)
    redis_url: str = field(default_factory=lambda: os.getenv("REDIS_URL", "redis://localhost:6379"))

    # Gainsight API Configuration
    gainsight_base_url: str = "https://demo-emea1.gainsightcloud.com/v1/ant/v2"
    gainsight_activity_url: str = "https://demo-emea1.gainsightcloud.com/v1/ant/v2/activity"
    gainsight_drafts_url: str = "https://demo-emea1.gainsightcloud.com/v1/ant/v2/activity/drafts"
    gainsight_api_key: str = field(default_factory=lambda: os.getenv("GAINSIGHT_API_KEY", ""))

    # Data Migration Paths
    icici_data_path: str = "/Users/<USER>/Desktop/totango/ICICI.json"
    id_mapping_path: str = "/Users/<USER>/Desktop/totango/ID.json"
    flowtype_path: str = "/Users/<USER>/Desktop/totango/flowtype.json"
    gainsight_payload_path: str = "/Users/<USER>/Desktop/totango/Gainsight_payload.json"

    # API Rate Limiting
    api_rate_limit_delay: float = 0.5
    max_api_retries: int = 3
    api_timeout: int = 30

    # Monitoring
    enable_telemetry: bool = True
    telemetry_endpoint: Optional[str] = None

@dataclass
class MainConfig:
    """Main configuration container with enhanced integrations"""
    llm: LLMConfig = field(default_factory=LLMConfig)
    memory: MemoryConfig = field(default_factory=MemoryConfig)
    workflow: WorkflowConfig = field(default_factory=WorkflowConfig)
    browser: BrowserConfig = field(default_factory=BrowserConfig)
    agent: AgentConfig = field(default_factory=AgentConfig)
    logging: LoggingConfig = field(default_factory=LoggingConfig)
    storage: StorageConfig = field(default_factory=StorageConfig)
    security: SecurityConfig = field(default_factory=SecurityConfig)
    integration: IntegrationConfig = field(default_factory=IntegrationConfig)

    def __post_init__(self):
        """Create necessary directories on initialization"""
        self._create_directories()

    def _create_directories(self):
        """Create necessary directories if they don't exist"""
        directories = [
            self.storage.data_dir,
            self.storage.logs_dir,
            self.storage.screenshots_dir,
            self.storage.recordings_dir,
            self.storage.exports_dir,
            self.memory.vector_db_path,
            self.workflow.workflow_state_path,
            "./data/letta_agents",
            "./data/browser_use_sessions",
            "./data/knowledge_graphs"
        ]

        for directory in directories:
            Path(directory).mkdir(parents=True, exist_ok=True)

    @classmethod
    def from_env(cls) -> 'MainConfig':
        """Create configuration from environment variables"""
        return cls()

    def validate(self) -> bool:
        """Enhanced validation for new integrations"""
        errors = []

        # Check required API keys (now including OpenRouter and HuggingFace)
        if not any([self.llm.openai_api_key, self.llm.anthropic_api_key,
                   self.llm.google_api_key, self.llm.openrouter_api_key,
                   self.llm.huggingface_api_key]):
            errors.append("At least one LLM API key is required")

        # Check memory settings
        if self.memory.memory_consolidation_interval < 60:
            errors.append("Memory consolidation interval should be at least 60 seconds")

        # Check browser settings
        if self.browser.browser_timeout < 5000:
            errors.append("Browser timeout should be at least 5 seconds")

        # Check agent settings
        if self.agent.max_retries < 1:
            errors.append("Max retries should be at least 1")

        # Check workflow settings
        if self.workflow.enable_langgraph and self.workflow.max_workflow_steps < 10:
            errors.append("Workflow max steps should be at least 10")

        # Check Browser-Use integration
        if self.browser.enable_browser_use and self.browser.browser_use_max_steps < 5:
            errors.append("Browser-Use max steps should be at least 5")

        # Check Letta integration
        if self.memory.letta_enabled and not self.memory.letta_server_url:
            errors.append("Letta server URL is required when Letta is enabled")

        if errors:
            raise ValueError(f"Configuration errors: {'; '.join(errors)}")


    def get_integration_status(self) -> Dict[str, bool]:
        """Get status of all integrations"""
        return {
            "browser_use": self.browser.enable_browser_use,
            "letta_memory": self.memory.letta_enabled,
            "langgraph_workflow": self.workflow.enable_langgraph,
            "persistent_memory": self.memory.enable_persistent_memory,
            "knowledge_graph": self.memory.enable_knowledge_graph,
            "multi_llm": True,  # Always enabled
            "enhanced_recording": True,  # Always enabled
            "selector_optimization": True  # Always enabled
        }

    def get_browser_use_config(self) -> Dict[str, Any]:
        """Get Browser-Use specific configuration"""
        return {
            "max_steps": self.browser.browser_use_max_steps,
            "enable_memory": self.browser.browser_use_enable_memory,
            "memory_interval": self.browser.browser_use_memory_interval,
            "use_vision": self.browser.browser_use_use_vision,
            "save_conversation": self.browser.browser_use_save_conversation,
            "controller_actions": self.browser.browser_use_controller_actions
        }

    def get_letta_config(self) -> Dict[str, Any]:
        """Get Letta memory configuration"""
        return {
            "server_url": self.memory.letta_server_url,
            "agent_name": self.memory.letta_agent_name,
            "memory_blocks": self.memory.letta_memory_blocks,
            "enable_persistent": self.memory.enable_persistent_memory,
            "enable_semantic": self.memory.enable_semantic_memory
        }

    def get_workflow_config(self) -> Dict[str, Any]:
        """Get LangGraph workflow configuration"""
        return {
            "max_steps": self.workflow.max_workflow_steps,
            "timeout": self.workflow.workflow_timeout,
            "enable_checkpoints": self.workflow.enable_checkpoints,
            "checkpoint_interval": self.workflow.checkpoint_interval,
            "default_pattern": self.workflow.default_workflow_pattern,
            "state_path": self.workflow.workflow_state_path
        }

    def get_model_for_task(self, task_type: str) -> str:
        """Get the best model for a specific task type"""
        if not self.llm.enable_task_aware_selection:
            return self.llm.primary_model

        task_models = {
            "coding": self.llm.coding_model,
            "reasoning": self.llm.reasoning_model,
            "natural_language": self.llm.natural_language_model,
            "multimodal": self.llm.multimodal_model,
            "general": self.llm.general_model,
            "browser_automation": self.llm.reasoning_model,
            "data_migration": self.llm.coding_model,
            "api_calling": self.llm.coding_model,
            "json_processing": self.llm.coding_model
        }

        return task_models.get(task_type, self.llm.primary_model)

    def get_fallback_models(self, task_type: str = None) -> List[str]:
        """Get fallback models, optionally filtered by task type"""
        if task_type == "coding":
            return ["meta-llama/llama-4-maverick:free", "deepseek/deepseek-chat-v3-0324:free"]
        elif task_type == "reasoning":
            return ["deepseek/deepseek-r1:free", "qwen/qwq-32b:free"]
        elif task_type == "multimodal":
            return ["qwen/qwen2.5-vl-3b-instruct:free", "meta-llama/llama-4-scout:free"]
        else:
            return self.llm.fallback_models

    def get_api_config_for_model(self, model: str) -> Dict[str, str]:
        """Get API configuration for a specific model"""
        if ":free" in model or "openrouter" in model:
            return {
                "api_key": self.llm.openrouter_api_key,
                "base_url": self.llm.openrouter_base_url,
                "provider": "openrouter"
            }
        elif "gemini" in model.lower() or "google/" in model:
            return {
                "api_key": self.llm.google_api_key,
                "base_url": "https://generativelanguage.googleapis.com/v1",
                "provider": "google"
            }
        elif "gpt" in model:
            return {
                "api_key": self.llm.openai_api_key,
                "base_url": "https://api.openai.com/v1",
                "provider": "openai"
            }
        elif "claude" in model:
            return {
                "api_key": self.llm.anthropic_api_key,
                "base_url": "https://api.anthropic.com",
                "provider": "anthropic"
            }
        elif "hf_" in self.llm.huggingface_api_key:
            return {
                "api_key": self.llm.huggingface_api_key,
                "base_url": self.llm.huggingface_base_url,
                "provider": "huggingface"
            }
        else:
            # Default to OpenRouter for unknown models
            return {
                "api_key": self.llm.openrouter_api_key,
                "base_url": self.llm.openrouter_base_url,
                "provider": "openrouter"
            }

# Global configuration instance
config = MainConfig.from_env()

# Environment-specific overrides
if os.getenv("ENVIRONMENT") == "development":
    config.browser.headless = False
    config.logging.log_level = "DEBUG"
    config.agent.max_steps_per_task = 100
elif os.getenv("ENVIRONMENT") == "production":
    config.browser.headless = True
    config.logging.log_level = "INFO"
    config.security.encrypt_memory = True
    config.security.anonymize_screenshots = True

# Validate configuration on import
try:
    config.validate()
except ValueError as e:
    print(f"Configuration validation failed: {e}")
    raise

#!/usr/bin/env python3
"""
Meeting Types Only Converter
============================

Processes ONLY the 37 activities that have meeting_type field and converts them to Gainsight format.
This is useful for testing meeting-specific conversions.

Features:
- Filters activities to only those with meeting_type field
- Maps meeting types to Gainsight activity types (Email, Meeting, Update)
- Maps touchpoint tags and flow types
- Uses enrichedUsers for author info
- Generates JSON and CSV for just the meeting activities

Usage:
1. Update file paths in the main() function
2. Run: python3 meeting_types_only_converter.py

Author: Assistant
Date: January 29, 2025
"""

import json
import pandas as pd
import re
import os
from pathlib import Path
from datetime import datetime
from collections import Counter

class MeetingTypesOnlyConverter:
    def __init__(self, icici_file, flowtype_file, touchpoint_file, id_file, output_dir):
        """Initialize the converter with file paths."""
        self.icici_file = icici_file
        self.flowtype_file = flowtype_file
        self.touchpoint_file = touchpoint_file
        self.id_file = id_file
        self.output_dir = output_dir

        # Mappings
        self.touchpoint_mapping = {}
        self.flow_type_mapping = {}
        self.id_mapping = {}

        # Totango to Gainsight activity type mapping
        self.totango_to_gainsight_mapping = {
            # Default Gainsight Types
            "Email": "Email",
            "Telephone Call": "Call",
            "Web Meeting": "Meeting",
            "Internal Note": "Update",
            "Meeting": "Meeting",
            "Update": "Update",

            # Custom Gainsight Types
            "In-Person Meeting": "In-Person Meeting",
            "Gong Call": "Gong Call",
            "Feedback": "Feedback",
            "Inbound": "Inbound",
            "Slack": "Slack",

            # Common variations
            "email": "Email",
            "Email Campaign": "Email",
            "telephone call": "Call",
            "Phone Call": "Call",
            "web meeting": "Meeting",
            "Video Meeting": "Meeting",
            "internal note": "Update",
            "Note": "Update"
        }

        # Statistics
        self.stats = {
            'total_activities': 0,
            'meeting_type_activities': 0,
            'activities_with_touchpoint_tags': 0,
            'activities_with_flow_type': 0,
            'activities_with_enriched_users': 0,
            'gainsight_type_usage': Counter(),
            'meeting_type_breakdown': Counter()
        }

    def load_mappings(self):
        """Load ID, touchpoint and flow type mappings."""
        print("📋 Loading mapping files...")

        # Load ID mappings (meeting types)
        try:
            if Path(self.id_file).exists():
                with open(self.id_file, 'r', encoding='utf-8') as f:
                    id_data = json.load(f)
                self.id_mapping = {item['id']: item['display_name'] for item in id_data}
                print(f"✅ Loaded {len(self.id_mapping)} ID mappings")
            else:
                print(f"⚠️  ID file not found: {self.id_file}")
        except Exception as e:
            print(f"⚠️  Error loading ID mappings: {e}")

        # Load touchpoint reason mappings
        try:
            if Path(self.touchpoint_file).exists():
                with open(self.touchpoint_file, 'r', encoding='utf-8') as f:
                    touchpoint_data = json.load(f)
                self.touchpoint_mapping = {item['id']: item['display_name'] for item in touchpoint_data}
                print(f"✅ Loaded {len(self.touchpoint_mapping)} touchpoint reason mappings")
            else:
                print(f"⚠️  Touchpoint file not found: {self.touchpoint_file}")
        except Exception as e:
            print(f"⚠️  Error loading touchpoint mappings: {e}")

        # Load flow type mappings
        try:
            if Path(self.flowtype_file).exists():
                with open(self.flowtype_file, 'r', encoding='utf-8') as f:
                    flow_type_data = json.load(f)
                # Use activity_type_id field (correct field for ICICI data)
                for item in flow_type_data:
                    flow_id = item.get('activity_type_id', item.get('id', ''))
                    display_name = item.get('display_name', item.get('name', ''))
                    if flow_id and display_name:
                        clean_name = self.clean_flow_type_name(display_name)
                        self.flow_type_mapping[flow_id] = clean_name
                print(f"✅ Loaded {len(self.flow_type_mapping)} flow type mappings")
            else:
                print(f"⚠️  Flow type file not found: {self.flowtype_file}")
        except Exception as e:
            print(f"⚠️  Error loading flow type mappings: {e}")

    def clean_flow_type_name(self, flow_type_name: str) -> str:
        """Clean flow type name by removing timestamps and numbers."""
        if not flow_type_name:
            return ""

        # Remove timestamp patterns like _1560979263618
        cleaned = re.sub(r'_\d{10,}', '', flow_type_name)

        # Remove trailing numbers like _1, _2, etc.
        cleaned = re.sub(r'_\d+$', '', cleaned)

        # Replace underscores with spaces and title case
        cleaned = cleaned.replace('_', ' ').title()

        return cleaned

    def map_totango_to_gainsight_type(self, totango_type: str) -> str:
        """Map Totango meeting type to Gainsight activity type."""
        if not totango_type or totango_type.strip() == "":
            return "Update"

        clean_type = totango_type.strip()

        # Direct mapping
        if clean_type in self.totango_to_gainsight_mapping:
            return self.totango_to_gainsight_mapping[clean_type]

        # Case-insensitive mapping
        for totango_key, gainsight_value in self.totango_to_gainsight_mapping.items():
            if clean_type.lower() == totango_key.lower():
                return gainsight_value

        # Pattern matching
        clean_lower = clean_type.lower()
        if "email" in clean_lower or "mail" in clean_lower:
            return "Email"
        elif "call" in clean_lower or "phone" in clean_lower or "telephone" in clean_lower:
            return "Call"
        elif "meeting" in clean_lower or "conference" in clean_lower or "video" in clean_lower:
            return "Meeting"
        elif "slack" in clean_lower or "chat" in clean_lower or "message" in clean_lower:
            return "Slack"
        elif "feedback" in clean_lower or "survey" in clean_lower or "review" in clean_lower:
            return "Feedback"
        elif "inbound" in clean_lower or "support" in clean_lower or "help" in clean_lower:
            return "Inbound"
        elif "gong" in clean_lower:
            return "Gong Call"
        elif "person" in clean_lower and "meeting" in clean_lower:
            return "In-Person Meeting"
        else:
            return "Update"

    def map_touchpoint_tags(self, touchpoint_tags) -> str:
        """Map touchpoint tags to names with correct logic."""
        if not touchpoint_tags:
            # NO touchpoint_tags field at all - return empty
            return ""

        # Handle different formats
        if isinstance(touchpoint_tags, str):
            tag_ids = [touchpoint_tags]
        elif isinstance(touchpoint_tags, list):
            tag_ids = touchpoint_tags
        else:
            return ""

        # Map the IDs
        mapped_names = []
        for tag_id in tag_ids:
            if tag_id in self.touchpoint_mapping:
                mapped_names.append(self.touchpoint_mapping[tag_id])

        if mapped_names:
            return "; ".join(mapped_names)
        else:
            # Had touchpoint_tags but couldn't map them
            return "Internal Note"

    def get_flow_type_name(self, activity: dict) -> str:
        """Get the cleaned flow type name."""
        properties = activity.get('properties', {})

        # Check for activity_type_id field (correct field from ICICI data)
        if 'activity_type_id' in properties:
            flow_type_id = properties['activity_type_id']
            if flow_type_id in self.flow_type_mapping:
                return self.flow_type_mapping[flow_type_id]

        # Check for other possible flow type fields
        for field_name in ['flow_type', 'flow_type_id', 'type_id']:
            if field_name in properties:
                flow_type_id = properties[field_name]
                if flow_type_id in self.flow_type_mapping:
                    return self.flow_type_mapping[flow_type_id]

        # Default
        return "Standard"

    def extract_author_info(self, activity: dict) -> tuple:
        """Extract author name and email from enrichedUsers ONLY."""
        # Check enrichedUsers at top level ONLY
        enriched_users = activity.get('enrichedUsers', [])
        if enriched_users and isinstance(enriched_users, list) and len(enriched_users) > 0:
            user = enriched_users[0]  # Take first user
            if isinstance(user, dict):
                full_name = user.get('fullName', '')
                email = user.get('email', '')
                return full_name, email

        # If no enrichedUsers or empty, return blank
        return '', ''

    def filter_meeting_type_activities(self) -> list:
        """Filter activities to only those with meeting_type field."""
        print("🔍 Loading and filtering activities with meeting_type...")

        # Load ICICI activities
        with open(self.icici_file, 'r', encoding='utf-8') as f:
            all_activities = json.load(f)

        self.stats['total_activities'] = len(all_activities)
        print(f"📊 Total activities in file: {len(all_activities)}")

        # Filter to only activities with meeting_type
        meeting_activities = []
        for activity in all_activities:
            properties = activity.get('properties', {})
            if 'meeting_type' in properties:
                meeting_activities.append(activity)

                # Track meeting type breakdown
                meeting_type_id = properties['meeting_type']
                if meeting_type_id in self.id_mapping:
                    meeting_type_name = self.id_mapping[meeting_type_id]
                    self.stats['meeting_type_breakdown'][meeting_type_name] += 1

        self.stats['meeting_type_activities'] = len(meeting_activities)
        print(f"📊 Activities with meeting_type: {len(meeting_activities)}")

        return meeting_activities

    def process_meeting_activities(self, meeting_activities: list) -> list:
        """Process meeting activities and apply mappings."""
        print("🔄 Processing meeting activities and applying mappings...")

        processed_activities = []

        for activity in meeting_activities:
            processed_activity = activity.copy()
            properties = activity['properties'].copy()

            # Process meeting_type (all activities here have meeting_type)
            meeting_type_id = properties['meeting_type']

            # Map meeting type ID to display name using ID.json
            if meeting_type_id in self.id_mapping:
                totango_meeting_type = self.id_mapping[meeting_type_id]
            else:
                totango_meeting_type = f"UNMAPPED_{meeting_type_id}"

            # Map to Gainsight activity type
            gainsight_activity_type = self.map_totango_to_gainsight_type(totango_meeting_type)

            # Store mapping information
            properties['meeting_type_id'] = meeting_type_id
            properties['meeting_type_name'] = totango_meeting_type
            properties['gainsight_activity_type'] = gainsight_activity_type

            # Track usage
            self.stats['gainsight_type_usage'][gainsight_activity_type] += 1

            # Process touchpoint_tags
            if 'touchpoint_tags' in properties:
                self.stats['activities_with_touchpoint_tags'] += 1
                touchpoint_tags = properties['touchpoint_tags']

                # Map touchpoint tags
                mapped_names = self.map_touchpoint_tags(touchpoint_tags)

                # Store both original and mapped
                properties['touchpoint_tags_names'] = mapped_names
            else:
                properties['touchpoint_tags_names'] = ""

            # Check for enrichedUsers
            if activity.get('enrichedUsers'):
                self.stats['activities_with_enriched_users'] += 1

            # Check for flow type
            if self.get_flow_type_name(activity) != "Standard":
                self.stats['activities_with_flow_type'] += 1

            processed_activity['properties'] = properties
            processed_activities.append(processed_activity)

        return processed_activities

    def generate_json_output(self, processed_activities: list) -> str:
        """Generate JSON output file for meeting activities only."""
        json_output_file = os.path.join(self.output_dir, "gainsight_meeting_types_only.json")

        print(f"💾 Saving JSON file: {json_output_file}")

        with open(json_output_file, 'w', encoding='utf-8') as f:
            json.dump(processed_activities, f, indent=2, ensure_ascii=False)

        file_size = Path(json_output_file).stat().st_size
        print(f"✅ JSON file saved: {file_size:,} bytes")

        return json_output_file

    def generate_subject(self, activity: dict) -> str:
        """Generate subject for activity."""
        properties = activity.get('properties', {})
        activity_type = activity.get('type', 'Activity')
        meeting_type_name = properties.get('meeting_type_name', 'Meeting')

        # Try various fields for meaningful names
        for field in ['name', 'display_name', 'title', 'subject']:
            if field in properties and properties[field]:
                return f"ICICI {meeting_type_name}: {properties[field]}"

        # Generate based on meeting type
        return f"ICICI {meeting_type_name}: {activity_type.replace('_', ' ').title()}"

    def generate_content(self, activity: dict) -> str:
        """Generate HTML content for activity."""
        properties = activity.get('properties', {})
        activity_type = activity.get('type', 'activity')
        meeting_type_name = properties.get('meeting_type_name', 'Meeting')

        # Check for existing content
        if 'description' in properties and properties['description']:
            return f"<p>{properties['description']}</p>"

        if 'content' in properties and properties['content']:
            return f"<p>{properties['content']}</p>"

        # Generate based on activity type and meeting type
        if activity_type == 'automated_attribute_change':
            display_name = properties.get('display_name', 'attribute')
            new_value = properties.get('new_value', 'updated')
            prev_value = properties.get('prev_value', 'previous value')
            return f"<p>{meeting_type_name}: {display_name} changed from '{prev_value}' to '{new_value}'</p>"
        elif activity_type == 'campaign_touch':
            name = properties.get('name', 'campaign')
            description = properties.get('description', f"Campaign '{name}' was executed")
            return f"<p>{meeting_type_name}: {description}</p>"
        elif activity_type == 'webhook':
            name = properties.get('name', 'webhook')
            status = properties.get('status', 'executed')
            return f"<p>{meeting_type_name}: Webhook '{name}' was {status}</p>"
        elif activity_type == 'note':
            return f"<p>{meeting_type_name}: Note activity for ICICI Bank</p>"
        else:
            return f"<p>ICICI Bank {meeting_type_name}: {activity_type.replace('_', ' ')}</p>"

    def format_timestamp(self, timestamp: int) -> str:
        """Format timestamp for CSV."""
        try:
            if timestamp > 10**10:  # milliseconds
                timestamp = timestamp / 1000
            dt = datetime.fromtimestamp(timestamp)
            return dt.strftime('%Y-%m-%d %H:%M:%S')
        except:
            return ""

    def strip_html(self, html: str) -> str:
        """Strip HTML tags."""
        if not html:
            return ""
        return re.sub(r'<[^>]+>', '', html)

    def generate_csv_output(self, processed_activities: list) -> str:
        """Generate CSV output file with meeting activities only."""
        csv_output_file = os.path.join(self.output_dir, "gainsight_meeting_types_only.csv")

        print(f"📊 Creating CSV file: {csv_output_file}")

        csv_rows = []

        for i, activity in enumerate(processed_activities, 1):
            properties = activity.get('properties', {})

            # Get activity data
            timestamp = activity.get('timestamp', 0)
            activity_type = activity.get('type', 'unknown')
            gainsight_activity_type = properties.get('gainsight_activity_type', 'Update')

            # Get touchpoint reason and flow type
            touchpoint_reason = properties.get('touchpoint_tags_names', '')
            flow_type = self.get_flow_type_name(activity)

            # Extract author info (enrichedUsers ONLY)
            author_name, author_email = self.extract_author_info(activity)

            # Generate content
            subject = self.generate_subject(activity)
            content_html = self.generate_content(activity)
            content_plain = self.strip_html(content_html)
            activity_date = self.format_timestamp(timestamp)

            csv_row = {
                "Row Number": i,
                "Subject": subject,
                "Activity Date": activity_date,
                "Activity Type": gainsight_activity_type,
                "Content (HTML)": content_html,
                "Plain Text": content_plain,
                "Author Name": author_name,
                "Author Email": author_email,
                "Flow Type": flow_type,
                "Touchpoint Reason": touchpoint_reason,
                "External Attendees": "",  # Empty as no external attendee data found
                "Company": "ICICI",
                "Original Activity Type": activity_type,
                "Original Meeting Type": properties.get('meeting_type_name', 'Unknown'),
                "Meeting Type ID": properties.get('meeting_type_id', ''),
                "Source": "ICICI_MEETING_TYPES_ONLY"
            }

            csv_rows.append(csv_row)

        # Create DataFrame and save
        df = pd.DataFrame(csv_rows)
        df.to_csv(csv_output_file, index=False, encoding='utf-8')

        file_size = Path(csv_output_file).stat().st_size
        print(f"✅ CSV file saved: {file_size:,} bytes")

        return csv_output_file

    def generate_report(self) -> str:
        """Generate comprehensive report for meeting types only."""
        report = []
        report.append("=" * 70)
        report.append("ICICI MEETING TYPES ONLY CONVERSION REPORT")
        report.append("=" * 70)
        report.append(f"📊 Total Activities in File: {self.stats['total_activities']:,}")
        report.append(f"🎯 Activities with meeting_type: {self.stats['meeting_type_activities']:,}")
        report.append(f"🏷️  Activities with touchpoint_tags: {self.stats['activities_with_touchpoint_tags']:,}")
        report.append(f"🔄 Activities with flow_type: {self.stats['activities_with_flow_type']:,}")
        report.append(f"👤 Activities with enrichedUsers: {self.stats['activities_with_enriched_users']:,}")
        report.append("")

        # Meeting Type Breakdown
        if self.stats['meeting_type_breakdown']:
            report.append("📋 MEETING TYPE BREAKDOWN")
            report.append("-" * 30)
            for meeting_type, count in self.stats['meeting_type_breakdown'].most_common():
                report.append(f"   • {meeting_type}: {count:,} activities")
            report.append("")

        # Gainsight Activity Type Distribution
        if self.stats['gainsight_type_usage']:
            report.append("🎯 GAINSIGHT ACTIVITY TYPE DISTRIBUTION")
            report.append("-" * 40)
            total_mapped = sum(self.stats['gainsight_type_usage'].values())
            for activity_type, count in self.stats['gainsight_type_usage'].most_common():
                percentage = (count / total_mapped) * 100
                report.append(f"   • {activity_type}: {count:,} activities ({percentage:.1f}%)")
            report.append("")

        # Migration Readiness
        report.append("🚀 GAINSIGHT MIGRATION READINESS")
        report.append("-" * 35)
        report.append("✅ All meeting activities have Gainsight-compatible activity types")
        report.append("✅ Touchpoint tags mapped where available")
        report.append("✅ Flow types mapped where available")
        report.append("✅ Author info from enrichedUsers only")
        report.append("✅ Ready for direct Gainsight Timeline import")

        report.append("=" * 70)
        return "\n".join(report)

    def run_conversion(self):
        """Run the complete conversion process for meeting types only."""
        print("🚀 STARTING MEETING TYPES ONLY CONVERSION")
        print("=" * 50)

        try:
            # Step 1: Load mappings
            self.load_mappings()

            # Step 2: Filter to meeting activities only
            meeting_activities = self.filter_meeting_type_activities()

            if not meeting_activities:
                print("❌ No activities with meeting_type found!")
                return False

            # Step 3: Process meeting activities
            processed_activities = self.process_meeting_activities(meeting_activities)

            # Step 4: Generate JSON output
            json_file = self.generate_json_output(processed_activities)

            # Step 5: Generate CSV output
            csv_file = self.generate_csv_output(processed_activities)

            # Step 6: Generate and display report
            report = self.generate_report()
            print(f"\n{report}")

            print(f"\n🎉 MEETING TYPES CONVERSION COMPLETED!")
            print("=" * 50)
            print(f"📄 JSON file: {json_file}")
            print(f"📊 CSV file: {csv_file}")
            print(f"🚀 Ready for Gainsight Timeline import!")

            return True

        except Exception as e:
            print(f"💥 Conversion failed: {e}")
            return False

def main():
    """Main function - UPDATE FILE PATHS HERE FOR DIFFERENT COMPANIES."""

    # =================================================================
    # UPDATE THESE PATHS FOR DIFFERENT COMPANIES
    # =================================================================

    # Input files (UPDATE THESE PATHS)
    ICICI_FILE = "/Users/<USER>/Desktop/totango/ICICI.json"  # ORIGINAL FILE
    FLOWTYPE_FILE = "/Users/<USER>/Desktop/totango/flowtype.json"
    TOUCHPOINT_FILE = "/Users/<USER>/Desktop/totango/Touchpoint_reason.JSON"
    ID_FILE = "/Users/<USER>/Desktop/totango/ID.json"

    # Output directory (UPDATE THIS PATH)
    OUTPUT_DIR = "/Users/<USER>/Desktop/totango"

    # =================================================================

    print("🚀 MEETING TYPES ONLY ICICI TO GAINSIGHT CONVERTER")
    print("=" * 60)
    print("📋 This converter processes ONLY activities with meeting_type field")
    print("📊 Expected: 37 activities with meeting types")
    print()

    # Check if input files exist
    required_files = [ICICI_FILE, FLOWTYPE_FILE, TOUCHPOINT_FILE, ID_FILE]
    missing_files = []

    for file_path in required_files:
        if not Path(file_path).exists():
            missing_files.append(file_path)
        else:
            file_size = Path(file_path).stat().st_size
            print(f"✅ {Path(file_path).name}: {file_size:,} bytes")

    if missing_files:
        print(f"\n❌ Missing required files:")
        for file_path in missing_files:
            print(f"   • {file_path}")
        print(f"\nPlease update the file paths in the main() function.")
        return 1

    # Create output directory if it doesn't exist
    Path(OUTPUT_DIR).mkdir(parents=True, exist_ok=True)

    # Run conversion
    converter = MeetingTypesOnlyConverter(
        icici_file=ICICI_FILE,
        flowtype_file=FLOWTYPE_FILE,
        touchpoint_file=TOUCHPOINT_FILE,
        id_file=ID_FILE,
        output_dir=OUTPUT_DIR
    )

    success = converter.run_conversion()

    if success:
        print(f"\n📁 Output files saved to: {OUTPUT_DIR}")
        print(f"   • gainsight_meeting_types_only.json")
        print(f"   • gainsight_meeting_types_only.csv")
        print(f"\n🎯 Use these files to test meeting-specific imports in Gainsight")
        return 0
    else:
        return 1

if __name__ == "__main__":
    exit_code = main()
    exit(exit_code)

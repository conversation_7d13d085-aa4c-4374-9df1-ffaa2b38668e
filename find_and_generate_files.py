#!/usr/bin/env python3
"""
Find and Generate Missing Files
===============================

This script will:
1. Check what files currently exist
2. Generate any missing JSON or CSV files
3. Show you exactly where everything is located

Author: Assistant
Date: January 29, 2025
"""

import os
import subprocess
from pathlib import Path

def check_file_status():
    """Check the status of all expected files."""
    print("🔍 CHECKING FILE STATUS")
    print("=" * 40)

    # Define all expected files
    files_to_check = [
        # Source files
        {
            "name": "ICICI Source Data",
            "path": "/Users/<USER>/Desktop/totango/ICICI_processed.json",
            "type": "source",
            "required": True
        },
        {
            "name": "Flow Types",
            "path": "/Users/<USER>/Desktop/totango/flowtype.json",
            "type": "source",
            "required": True
        },
        {
            "name": "Touchpoint Reasons",
            "path": "/Users/<USER>/Desktop/totango/Touchpoint_reason.JSON",
            "type": "source",
            "required": True
        },

        # Generated JSON files
        {
            "name": "Mapped JSON (Gainsight Ready)",
            "path": "/Users/<USER>/Desktop/totango/ICICI_processed_gainsight_mapped.json",
            "type": "json_output",
            "required": False
        },

        # Generated CSV files (now in totango directory)
        {
            "name": "Demo CSV (Testing)",
            "path": "/Users/<USER>/Desktop/totango/ICICI_processed_gainsight_mapped_enhanced_demo.csv",
            "type": "csv_output",
            "required": False
        },
        {
            "name": "Real CSV (Production)",
            "path": "/Users/<USER>/Desktop/totango/ICICI_processed_gainsight_mapped_enhanced_real.csv",
            "type": "csv_output",
            "required": False
        },
        {
            "name": "Basic CSV (Backup)",
            "path": "/Users/<USER>/Desktop/totango/ICICI_processed_gainsight_import.csv",
            "type": "csv_output",
            "required": False
        }
    ]

    file_status = {}
    missing_required = []
    missing_outputs = []

    for file_info in files_to_check:
        file_path = file_info["path"]
        exists = Path(file_path).exists()

        if exists:
            size = Path(file_path).stat().st_size
            print(f"✅ {file_info['name']}: {size:,} bytes")
            print(f"   📄 {file_path}")
        else:
            print(f"❌ {file_info['name']}: NOT FOUND")
            print(f"   📄 Expected: {file_path}")

            if file_info["required"]:
                missing_required.append(file_info)
            else:
                missing_outputs.append(file_info)

        file_status[file_info["name"]] = {
            "exists": exists,
            "path": file_path,
            "type": file_info["type"]
        }
        print()

    return file_status, missing_required, missing_outputs

def generate_missing_files(missing_outputs):
    """Generate any missing output files."""
    print("🔧 GENERATING MISSING FILES")
    print("=" * 40)

    # Check what's missing
    missing_json = any(f["type"] == "json_output" for f in missing_outputs)
    missing_csv = any(f["type"] == "csv_output" for f in missing_outputs)

    if not missing_json and not missing_csv:
        print("✅ All output files already exist!")
        return True

    success = True

    # Generate JSON if missing
    if missing_json:
        print("📋 Generating Gainsight-mapped JSON file...")
        print("🚀 Running: python3 complete_gainsight_mapping.py")

        try:
            result = subprocess.run(
                ["python3", "complete_gainsight_mapping.py"],
                capture_output=True,
                text=True,
                timeout=120
            )

            if result.returncode == 0:
                print("✅ JSON mapping completed successfully!")
            else:
                print(f"❌ JSON mapping failed: {result.stderr}")
                success = False

        except Exception as e:
            print(f"❌ Error generating JSON: {e}")
            success = False

    # Generate CSV files if missing
    if missing_csv and success:
        print("\n📋 Generating enhanced CSV files...")

        # Generate Demo CSV
        print("🚀 Generating Demo CSV (Ram Prasad)...")
        try:
            result = subprocess.run(
                ["python3", "enhanced_csv_exporter.py"],
                input="1\n",
                text=True,
                capture_output=True,
                timeout=60
            )

            if result.returncode == 0:
                print("✅ Demo CSV generated successfully!")
            else:
                print(f"❌ Demo CSV generation failed: {result.stderr}")
                success = False

        except Exception as e:
            print(f"❌ Error generating Demo CSV: {e}")
            success = False

        # Generate Real CSV
        if success:
            print("🚀 Generating Real CSV (ICICI users)...")
            try:
                result = subprocess.run(
                    ["python3", "enhanced_csv_exporter.py"],
                    input="2\n",
                    text=True,
                    capture_output=True,
                    timeout=60
                )

                if result.returncode == 0:
                    print("✅ Real CSV generated successfully!")
                else:
                    print(f"❌ Real CSV generation failed: {result.stderr}")
                    success = False

            except Exception as e:
                print(f"❌ Error generating Real CSV: {e}")
                success = False

    return success

def show_final_file_locations():
    """Show the final locations of all files."""
    print("\n📁 FINAL FILE LOCATIONS")
    print("=" * 40)

    print("📊 All Files in Totango Directory:")
    totango_dir_files = [
        "/Users/<USER>/Desktop/totango/ICICI_processed_gainsight_mapped.json",
        "/Users/<USER>/Desktop/totango/ICICI_processed_gainsight_mapped_enhanced_demo.csv",
        "/Users/<USER>/Desktop/totango/ICICI_processed_gainsight_mapped_enhanced_real.csv",
        "/Users/<USER>/Desktop/totango/ICICI_processed_gainsight_import.csv"
    ]

    for file_path in totango_dir_files:
        if Path(file_path).exists():
            size = Path(file_path).stat().st_size
            file_name = Path(file_path).name
            print(f"✅ {file_name}: {size:,} bytes")
            print(f"   📄 {file_path}")
        else:
            file_name = Path(file_path).name
            print(f"❌ {file_name}: NOT FOUND")
            print(f"   📄 Expected: {file_path}")
        print()

def show_usage_instructions():
    """Show how to use the generated files."""
    print("🚀 HOW TO USE YOUR FILES")
    print("=" * 40)

    print("📋 JSON Files (For Development/Analysis):")
    print("   • ICICI_processed_gainsight_mapped.json")
    print("     - Contains all 322 activities with Gainsight activity types")
    print("     - Use for further processing or analysis")
    print("     - Location: /Users/<USER>/Desktop/totango/")

    print("\n📋 CSV Files (For Gainsight Import):")
    print("   • ICICI_processed_gainsight_mapped_enhanced_demo.csv")
    print("     - For testing Gainsight import")
    print("     - Uses Ram Prasad as author")
    print("     - Location: /Users/<USER>/Desktop/totango/")
    print()
    print("   • ICICI_processed_gainsight_mapped_enhanced_real.csv")
    print("     - For production migration")
    print("     - Uses real ICICI users as authors")
    print("     - Location: /Users/<USER>/Desktop/totango/")

    print("\n🎯 Import Process:")
    print("   1. Choose Demo CSV for testing OR Real CSV for production")
    print("   2. Import into Gainsight Timeline")
    print("   3. Map columns to Gainsight fields")
    print("   4. Verify all 322 activities import successfully")

def main():
    """Main function to find and generate files."""
    print("🔍 FIND AND GENERATE MISSING FILES")
    print("=" * 50)

    # Step 1: Check current file status
    file_status, missing_required, missing_outputs = check_file_status()

    # Step 2: Check if we can proceed
    if missing_required:
        print("❌ MISSING REQUIRED SOURCE FILES:")
        for file_info in missing_required:
            print(f"   • {file_info['name']}: {file_info['path']}")
        print("\nPlease ensure all source files are in place before proceeding.")
        return 1

    # Step 3: Generate missing output files
    if missing_outputs:
        print(f"📋 Found {len(missing_outputs)} missing output files:")
        for file_info in missing_outputs:
            print(f"   • {file_info['name']}")

        response = input("\nGenerate missing files? (y/n): ").strip().lower()

        if response in ['y', 'yes']:
            success = generate_missing_files(missing_outputs)

            if not success:
                print("\n❌ Some files failed to generate. Check error messages above.")
                return 1
        else:
            print("\n👋 File generation cancelled.")
            return 0

    # Step 4: Show final file locations
    show_final_file_locations()

    # Step 5: Show usage instructions
    show_usage_instructions()

    print("\n🎉 ALL FILES READY!")
    print("✅ JSON file: Contains mapped activity data")
    print("✅ CSV files: Ready for Gainsight import")
    print("✅ Both Demo and Real versions available")

    return 0

if __name__ == "__main__":
    exit_code = main()
    exit(exit_code)

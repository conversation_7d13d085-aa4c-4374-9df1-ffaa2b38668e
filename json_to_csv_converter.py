#!/usr/bin/env python3
"""
JSON to CSV Converter for Gainsight Activities
Converts the enhanced ICICI JSON output to CSV format for Gainsight import
"""

import json
import pandas as pd
import os
from datetime import datetime
from typing import Dict, List, Any
from pathlib import Path

class JSONToCSVConverter:
    """
    Converts enhanced ICICI JSON data to CSV format for Gainsight import
    """
    
    def __init__(self):
        self.stats = {
            'total_records': 0,
            'successful_conversions': 0,
            'activity_types': {},
            'missing_fields': []
        }
    
    def convert_timestamp_to_date(self, timestamp: int) -> str:
        """Convert timestamp to readable date format"""
        try:
            if timestamp:
                # Convert from milliseconds to seconds if needed
                if timestamp > 10**10:  # If timestamp is in milliseconds
                    timestamp = timestamp / 1000
                
                dt = datetime.fromtimestamp(timestamp)
                return dt.strftime('%Y-%m-%d %H:%M:%S')
            else:
                return ""
        except Exception:
            return ""
    
    def clean_html_content(self, html_content: str) -> str:
        """Clean HTML content for CSV export"""
        if not html_content:
            return ""
        
        # Simple HTML tag removal
        import re
        clean_text = re.sub(r'<[^>]+>', '', html_content)
        
        # Clean up extra whitespace
        clean_text = ' '.join(clean_text.split())
        
        return clean_text
    
    def format_attendees(self, attendees: List[Dict[str, Any]]) -> str:
        """Format attendees list for CSV"""
        if not attendees:
            return ""
        
        formatted_attendees = []
        for attendee in attendees:
            name = attendee.get('name', '')
            email = attendee.get('email', '')
            if name and email:
                formatted_attendees.append(f"{name} <{email}>")
            elif name:
                formatted_attendees.append(name)
            elif email:
                formatted_attendees.append(email)
        
        return "; ".join(formatted_attendees)
    
    def convert_json_to_csv(self, json_file: str, csv_file: str = None) -> str:
        """
        Convert JSON file to CSV format
        
        Args:
            json_file: Path to input JSON file
            csv_file: Path to output CSV file (optional)
            
        Returns:
            Path to created CSV file
        """
        print(f"🔄 Converting {json_file} to CSV format...")
        
        # Load JSON data
        try:
            with open(json_file, 'r', encoding='utf-8') as f:
                json_data = json.load(f)
        except Exception as e:
            print(f"❌ Error loading JSON file: {e}")
            raise
        
        if not isinstance(json_data, list):
            print("❌ JSON file should contain a list of activities")
            raise ValueError("Invalid JSON format")
        
        self.stats['total_records'] = len(json_data)
        print(f"📊 Found {self.stats['total_records']} activities to convert")
        
        # Prepare CSV rows
        csv_rows = []
        
        for i, record in enumerate(json_data):
            try:
                # Extract data from the enhanced JSON structure
                properties = record.get('properties', {})
                
                # Basic activity information
                activity_id = record.get('id', f'activity_{i}')
                activity_type = record.get('type', 'unknown')
                timestamp = record.get('timestamp', 0)
                
                # Enhanced fields from our converter
                gainsight_activity_type = properties.get('gainsight_activity_type', 'Update')
                meeting_type_name = properties.get('meeting_type_name', 'No Meeting Type')
                touchpoint_tags_names = properties.get('touchpoint_tags_names', ['Internal Note'])
                
                # Generate subject and content based on activity
                subject = self.generate_subject(record)
                content_html = self.generate_content_html(record)
                content_plain = self.clean_html_content(content_html)
                
                # Create CSV row
                csv_row = {
                    # Core Gainsight fields
                    "Activity ID": activity_id,
                    "Subject": subject,
                    "Activity Date": self.convert_timestamp_to_date(timestamp),
                    "Activity Type": gainsight_activity_type,  # This is the key field for Gainsight
                    "Content (HTML)": content_html,
                    "Plain Text": content_plain,
                    
                    # Author information (default to Ram Prasad as in original code)
                    "Author Name": "Ram Prasad",
                    "Author Email": "<EMAIL>",
                    
                    # Gainsight custom fields
                    "Flow Type": "1I00EBMOY66ROGCBY63I51PAXG5OJRPK37AQ",  # Default flow type
                    "Touchpoint Reason": "1I00J1INQCQ6GQ3T2MWULITYE9ASDFPQ2KD3",  # Default touchpoint reason
                    "Internal Attendees": "Ram Prasad <<EMAIL>>",
                    "External Attendees": "",
                    
                    # Company context
                    "Company": "ICICI",
                    
                    # Additional metadata
                    "Original Activity Type": activity_type,
                    "Meeting Type Name": meeting_type_name,
                    "Touchpoint Tags": "; ".join(touchpoint_tags_names) if isinstance(touchpoint_tags_names, list) else str(touchpoint_tags_names),
                    "Source": "ICICI_MIGRATION",
                    
                    # Technical fields
                    "Timestamp": timestamp,
                    "Has Meeting Type": "Yes" if properties.get('meeting_type_id') else "No"
                }
                
                csv_rows.append(csv_row)
                self.stats['successful_conversions'] += 1
                
                # Track activity types
                activity_type_key = gainsight_activity_type
                self.stats['activity_types'][activity_type_key] = self.stats['activity_types'].get(activity_type_key, 0) + 1
                
            except Exception as e:
                print(f"⚠️  Error processing record {i}: {e}")
                continue
        
        # Create DataFrame and export to CSV
        if not csv_rows:
            print("❌ No valid records to convert")
            raise ValueError("No valid records found")
        
        df = pd.DataFrame(csv_rows)
        
        # Set output file path
        if csv_file is None:
            base_name = os.path.splitext(os.path.basename(json_file))[0]
            csv_file = f"{base_name}_gainsight_import.csv"
        
        # Save CSV
        df.to_csv(csv_file, index=False, encoding='utf-8')
        
        print(f"✅ Successfully converted {len(csv_rows)} records")
        print(f"💾 CSV saved to: {csv_file}")
        
        # Display statistics
        self.display_conversion_stats()
        
        return csv_file
    
    def generate_subject(self, record: Dict[str, Any]) -> str:
        """Generate a meaningful subject for the activity"""
        properties = record.get('properties', {})
        activity_type = record.get('type', 'Activity')
        
        # Try to get a meaningful name from properties
        name = properties.get('name', '')
        display_name = properties.get('display_name', '')
        title = properties.get('title', '')
        
        if name:
            return f"ICICI: {name}"
        elif display_name:
            return f"ICICI: {display_name}"
        elif title:
            return f"ICICI: {title}"
        else:
            return f"ICICI {activity_type.replace('_', ' ').title()}"
    
    def generate_content_html(self, record: Dict[str, Any]) -> str:
        """Generate HTML content for the activity"""
        properties = record.get('properties', {})
        activity_type = record.get('type', 'activity')
        
        # Get description or other content
        description = properties.get('description', '')
        content = properties.get('content', '')
        
        if description:
            html_content = f"<p>{description}</p>"
        elif content:
            html_content = f"<p>{content}</p>"
        else:
            # Generate default content based on activity type
            if activity_type == 'automated_attribute_change':
                display_name = properties.get('display_name', 'attribute')
                new_value = properties.get('new_value', 'updated')
                html_content = f"<p>Automated update: {display_name} changed to {new_value}</p>"
            elif activity_type == 'campaign_touch':
                campaign_name = properties.get('name', 'campaign')
                html_content = f"<p>Campaign activity: {campaign_name} executed for ICICI Bank</p>"
            elif activity_type == 'webhook':
                webhook_name = properties.get('name', 'webhook')
                html_content = f"<p>Webhook activity: {webhook_name} triggered for ICICI Bank</p>"
            else:
                html_content = f"<p>ICICI Bank activity of type: {activity_type.replace('_', ' ')}</p>"
        
        return html_content
    
    def display_conversion_stats(self):
        """Display conversion statistics"""
        print(f"\n📊 CONVERSION STATISTICS:")
        print(f"   • Total records: {self.stats['total_records']}")
        print(f"   • Successfully converted: {self.stats['successful_conversions']}")
        print(f"   • Success rate: {self.stats['successful_conversions']/self.stats['total_records']*100:.1f}%")
        
        print(f"\n🎯 Activity Types Distribution:")
        for activity_type, count in sorted(self.stats['activity_types'].items()):
            percentage = (count / self.stats['successful_conversions']) * 100
            print(f"   • {activity_type}: {count} ({percentage:.1f}%)")

def main():
    """Main conversion function"""
    print("🔄 JSON TO CSV CONVERTER FOR GAINSIGHT")
    print("=" * 50)
    
    # Default file paths
    json_file = "/Users/<USER>/Desktop/totango/ICICI_gainsight_ready.json"
    csv_file = "/Users/<USER>/Desktop/totango/ICICI_gainsight_import.csv"
    
    # Check if JSON file exists
    if not Path(json_file).exists():
        print(f"❌ JSON file not found: {json_file}")
        print("   Please run the enhanced converter first:")
        print("   python icici_to_gainsight_enhanced.py")
        return
    
    # Create converter and run conversion
    converter = JSONToCSVConverter()
    
    try:
        output_file = converter.convert_json_to_csv(json_file, csv_file)
        
        print(f"\n🎉 Conversion completed successfully!")
        print(f"📄 CSV file ready for Gainsight import: {output_file}")
        print(f"\n📋 CSV contains the following key fields:")
        print(f"   • Activity Type: Mapped Gainsight activity types")
        print(f"   • Subject: Meaningful activity titles")
        print(f"   • Content: Detailed activity descriptions")
        print(f"   • Activity Date: Formatted timestamps")
        print(f"   • Author: Ram Prasad attribution")
        print(f"   • Company: ICICI context")
        
    except Exception as e:
        print(f"❌ Conversion failed: {e}")

if __name__ == "__main__":
    main()

#!/usr/bin/env python3
"""
Gainsight UI Automation Script
Automates the complete ICICI to Gainsight migration through the web UI
Based on the exact workflow: Login → Timeline → Create Activity → Fill Data → Submit
"""

import asyncio
import json
import logging
import time
from pathlib import Path
from typing import Dict, List, Any, Optional
from playwright.async_api import async_playwright, <PERSON>, Browser, BrowserContext

# Setup logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class GainsightUIAutomator:
    """
    Complete Gainsight UI automation for ICICI activity migration
    """
    
    def __init__(self):
        self.browser: Optional[Browser] = None
        self.context: Optional[BrowserContext] = None
        self.page: Optional[Page] = None
        
        # Gainsight credentials and URLs
        self.login_url = "https://auth.gainsightcloud.com/login?lc=en"
        self.subdomain = "demo-emea1"
        self.username = "<EMAIL>"
        self.password = "@Ramprasad826ie"
        self.customer_url = "https://demo-emea1.gainsightcloud.com/v1/ui/customersuccess360?cid=1P02IPMAEL4M3CQGY4K5FHU22D2HHT11KCKU#/8fd79692-8e98-4543-b2ec-bc0dba3776ca"
        
        # Data paths
        self.data_file = Path("/Users/<USER>/Desktop/wildweasel/Browser/data/icici_gainsight_ready.json")
        self.results_file = Path("/Users/<USER>/Desktop/wildweasel/Browser/data/ui_migration_results.json")
        
        # Migration tracking
        self.migration_results = {
            "total_activities": 0,
            "successful": 0,
            "failed": 0,
            "errors": [],
            "start_time": None,
            "end_time": None
        }
    
    async def setup_browser(self, headless: bool = False):
        """Setup Playwright browser"""
        playwright = await async_playwright().start()
        
        self.browser = await playwright.chromium.launch(
            headless=headless,
            args=['--no-sandbox', '--disable-setuid-sandbox']
        )
        
        self.context = await self.browser.new_context(
            viewport={'width': 1920, 'height': 1080},
            user_agent='Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36'
        )
        
        self.page = await self.context.new_page()
        
        # Enable request/response logging
        self.page.on("response", lambda response: logger.debug(f"Response: {response.url} - {response.status}"))
        
        logger.info("Browser setup completed")
    
    async def login_to_gainsight(self):
        """Login to Gainsight with provided credentials"""
        logger.info("Starting Gainsight login process...")
        
        try:
            # Navigate to login page
            await self.page.goto(self.login_url, wait_until="networkidle")
            await self.page.wait_for_timeout(2000)
            
            # Check if subdomain input is present
            subdomain_input = self.page.locator('input[placeholder*="subdomain"], input[name*="subdomain"], input[id*="subdomain"]')
            if await subdomain_input.count() > 0:
                logger.info("Subdomain input found, entering demo-emea1")
                await subdomain_input.fill(self.subdomain)
                
                # Look for continue/next button
                continue_btn = self.page.locator('button:has-text("Continue"), button:has-text("Next"), button[type="submit"]').first
                if await continue_btn.count() > 0:
                    await continue_btn.click()
                    await self.page.wait_for_timeout(2000)
            
            # Fill in username
            username_selectors = [
                'input[name="username"]',
                'input[name="email"]',
                'input[type="email"]',
                'input[placeholder*="email"]',
                'input[placeholder*="username"]'
            ]
            
            username_filled = False
            for selector in username_selectors:
                username_input = self.page.locator(selector)
                if await username_input.count() > 0:
                    await username_input.fill(self.username)
                    username_filled = True
                    logger.info("Username filled successfully")
                    break
            
            if not username_filled:
                # Fallback: try any visible input field
                inputs = await self.page.locator('input[type="text"], input[type="email"]').all()
                if inputs:
                    await inputs[0].fill(self.username)
                    username_filled = True
            
            # Fill in password
            password_selectors = [
                'input[name="password"]',
                'input[type="password"]'
            ]
            
            password_filled = False
            for selector in password_selectors:
                password_input = self.page.locator(selector)
                if await password_input.count() > 0:
                    await password_input.fill(self.password)
                    password_filled = True
                    logger.info("Password filled successfully")
                    break
            
            # Submit login form
            login_btn_selectors = [
                'button[type="submit"]',
                'button:has-text("Sign in")',
                'button:has-text("Login")',
                'button:has-text("Log in")',
                'input[type="submit"]'
            ]
            
            for selector in login_btn_selectors:
                login_btn = self.page.locator(selector)
                if await login_btn.count() > 0:
                    await login_btn.click()
                    logger.info("Login button clicked")
                    break
            
            # Wait for login to complete
            await self.page.wait_for_timeout(5000)
            
            # Check if login was successful
            current_url = self.page.url
            if "gainsightcloud.com" in current_url and "login" not in current_url:
                logger.info("Login successful!")
                return True
            else:
                logger.error(f"Login may have failed. Current URL: {current_url}")
                return False
                
        except Exception as e:
            logger.error(f"Login failed: {e}")
            return False
    
    async def navigate_to_customer_timeline(self):
        """Navigate to the specific customer timeline"""
        logger.info("Navigating to customer timeline...")
        
        try:
            # Navigate to customer page
            await self.page.goto(self.customer_url, wait_until="networkidle")
            await self.page.wait_for_timeout(3000)
            
            # Click on Timeline tab
            timeline_selectors = [
                'a:has-text("Timeline")',
                'button:has-text("Timeline")',
                '[data-tab="timeline"]',
                '.tab:has-text("Timeline")',
                'li:has-text("Timeline")'
            ]
            
            timeline_clicked = False
            for selector in timeline_selectors:
                timeline_tab = self.page.locator(selector)
                if await timeline_tab.count() > 0:
                    await timeline_tab.click()
                    timeline_clicked = True
                    logger.info("Timeline tab clicked")
                    break
            
            if not timeline_clicked:
                logger.warning("Timeline tab not found, continuing...")
            
            await self.page.wait_for_timeout(2000)
            return True
            
        except Exception as e:
            logger.error(f"Navigation failed: {e}")
            return False
    
    async def create_activity(self, activity_data: Dict[str, Any]) -> bool:
        """Create a single activity through the UI"""
        try:
            subject = activity_data.get("note", {}).get("subject", "No Subject")
            activity_type = activity_data.get("activityType", "General")
            description = activity_data.get("note", {}).get("body", "")
            
            logger.info(f"Creating activity: {subject[:50]}...")
            
            # Click Create button (top right)
            create_selectors = [
                'button:has-text("Create")',
                '[data-testid="create-button"]',
                '.create-button',
                'button:has-text("+ Create")',
                'button:has-text("Add")'
            ]
            
            create_clicked = False
            for selector in create_selectors:
                create_btn = self.page.locator(selector)
                if await create_btn.count() > 0:
                    await create_btn.click()
                    create_clicked = True
                    logger.debug("Create button clicked")
                    break
            
            if not create_clicked:
                logger.error("Create button not found")
                return False
            
            await self.page.wait_for_timeout(1000)
            
            # Click on Activity from dropdown
            activity_option_selectors = [
                'a:has-text("Activity")',
                'button:has-text("Activity")',
                '[data-option="activity"]',
                '.dropdown-item:has-text("Activity")'
            ]
            
            activity_option_clicked = False
            for selector in activity_option_selectors:
                activity_option = self.page.locator(selector)
                if await activity_option.count() > 0:
                    await activity_option.click()
                    activity_option_clicked = True
                    logger.debug("Activity option clicked")
                    break
            
            if not activity_option_clicked:
                logger.error("Activity option not found in dropdown")
                return False
            
            await self.page.wait_for_timeout(2000)
            
            # Select activity type
            await self.select_activity_type(activity_type)
            
            # Fill subject
            await self.fill_subject(subject)
            
            # Fill description if available
            if description:
                await self.fill_description(description)
            
            # Fill other fields if available
            await self.fill_additional_fields(activity_data)
            
            # Click Log Activity button
            success = await self.submit_activity()
            
            if success:
                logger.info(f"✅ Activity created successfully: {subject[:30]}...")
                self.migration_results["successful"] += 1
            else:
                logger.error(f"❌ Failed to create activity: {subject[:30]}...")
                self.migration_results["failed"] += 1
                self.migration_results["errors"].append({
                    "subject": subject,
                    "error": "Failed to submit activity"
                })
            
            return success
            
        except Exception as e:
            logger.error(f"Activity creation failed: {e}")
            self.migration_results["failed"] += 1
            self.migration_results["errors"].append({
                "subject": activity_data.get("note", {}).get("subject", "Unknown"),
                "error": str(e)
            })
            return False
    
    async def select_activity_type(self, activity_type: str):
        """Select activity type from dropdown"""
        try:
            # Look for activity type dropdown/select
            type_selectors = [
                'select[name*="type"]',
                'select[name*="activity"]',
                '[data-field="activityType"]',
                '.activity-type-dropdown',
                'select:has(option:has-text("Email")), select:has(option:has-text("Call")), select:has(option:has-text("Meeting"))'
            ]
            
            for selector in type_selectors:
                type_dropdown = self.page.locator(selector)
                if await type_dropdown.count() > 0:
                    # Map ICICI activity type to Gainsight types
                    gainsight_type = self.map_activity_type(activity_type)
                    await type_dropdown.select_option(label=gainsight_type)
                    logger.debug(f"Activity type selected: {gainsight_type}")
                    return True
            
            # If no dropdown found, try clicking on a type field that might open a modal
            type_field_selectors = [
                'input[placeholder*="type"]',
                '[data-testid="activity-type"]',
                '.type-selector'
            ]
            
            for selector in type_field_selectors:
                type_field = self.page.locator(selector)
                if await type_field.count() > 0:
                    await type_field.click()
                    await self.page.wait_for_timeout(500)
                    
                    # Try to find and click the appropriate option
                    gainsight_type = self.map_activity_type(activity_type)
                    option_selectors = [
                        f'li:has-text("{gainsight_type}")',
                        f'div:has-text("{gainsight_type}")',
                        f'button:has-text("{gainsight_type}")'
                    ]
                    
                    for option_selector in option_selectors:
                        option = self.page.locator(option_selector)
                        if await option.count() > 0:
                            await option.click()
                            logger.debug(f"Activity type selected: {gainsight_type}")
                            return True
            
            logger.warning(f"Could not set activity type: {activity_type}")
            return False
            
        except Exception as e:
            logger.error(f"Failed to select activity type: {e}")
            return False
    
    def map_activity_type(self, icici_type: str) -> str:
        """Map ICICI activity type to Gainsight activity type"""
        mapping = {
            "email": "Email",
            "call": "Call", 
            "meeting": "Meeting",
            "task": "Task",
            "note": "Note",
            "update": "Update",
            "support": "Support",
            "onboarding": "Meeting",  # Map onboarding to meeting
            "training": "Meeting",    # Map training to meeting
            "general": "Note"         # Default mapping
        }
        
        # Normalize the input
        normalized_type = icici_type.lower().strip()
        return mapping.get(normalized_type, "Note")  # Default to Note if not found
    
    async def fill_subject(self, subject: str):
        """Fill the activity subject field"""
        try:
            subject_selectors = [
                'input[name="subject"]',
                'input[placeholder*="subject"]',
                'input[placeholder*="title"]',
                '[data-field="subject"]',
                'textarea[name="subject"]'
            ]
            
            for selector in subject_selectors:
                subject_field = self.page.locator(selector)
                if await subject_field.count() > 0:
                    await subject_field.fill(subject)
                    logger.debug(f"Subject filled: {subject[:30]}...")
                    return True
            
            logger.warning("Subject field not found")
            return False
            
        except Exception as e:
            logger.error(f"Failed to fill subject: {e}")
            return False
    
    async def fill_description(self, description: str):
        """Fill the activity description/body field"""
        try:
            desc_selectors = [
                'textarea[name="description"]',
                'textarea[name="body"]',
                'textarea[placeholder*="description"]',
                'textarea[placeholder*="notes"]',
                '[data-field="description"]',
                '.rich-text-editor',
                '[contenteditable="true"]'
            ]
            
            for selector in desc_selectors:
                desc_field = self.page.locator(selector)
                if await desc_field.count() > 0:
                    await desc_field.fill(description)
                    logger.debug(f"Description filled: {description[:50]}...")
                    return True
            
            logger.debug("Description field not found or not required")
            return False
            
        except Exception as e:
            logger.error(f"Failed to fill description: {e}")
            return False
    
    async def fill_additional_fields(self, activity_data: Dict[str, Any]):
        """Fill any additional fields that match the activity data"""
        try:
            # Try to fill date if available
            if "gsCreateDate" in activity_data:
                date_selectors = [
                    'input[type="date"]',
                    'input[name*="date"]',
                    '[data-field="date"]'
                ]
                
                for selector in date_selectors:
                    date_field = self.page.locator(selector)
                    if await date_field.count() > 0:
                        # Convert timestamp to date string
                        date_str = activity_data["gsCreateDate"][:10]  # Get YYYY-MM-DD part
                        await date_field.fill(date_str)
                        logger.debug(f"Date filled: {date_str}")
                        break
            
            # Try to fill any other matching fields
            # This is a flexible approach for future field mappings
            
        except Exception as e:
            logger.debug(f"Could not fill additional fields: {e}")
    
    async def submit_activity(self) -> bool:
        """Submit the activity by clicking Log Activity button"""
        try:
            submit_selectors = [
                'button:has-text("Log Activity")',
                'button:has-text("Save Activity")',
                'button:has-text("Create Activity")',
                'button:has-text("Submit")',
                'button:has-text("Save")',
                'button[type="submit"]'
            ]
            
            for selector in submit_selectors:
                submit_btn = self.page.locator(selector)
                if await submit_btn.count() > 0:
                    await submit_btn.click()
                    logger.debug("Submit button clicked")
                    
                    # Wait for submission to complete
                    await self.page.wait_for_timeout(2000)
                    
                    # Check for success indicators
                    success_indicators = [
                        '.success-message',
                        '.toast-success',
                        ':has-text("successfully")',
                        ':has-text("created")'
                    ]
                    
                    for indicator in success_indicators:
                        if await self.page.locator(indicator).count() > 0:
                            return True
                    
                    # If no explicit success message, assume success if we're back to timeline
                    if "timeline" in self.page.url.lower():
                        return True
                    
                    return True  # Assume success if no error
            
            logger.error("Submit button not found")
            return False
            
        except Exception as e:
            logger.error(f"Failed to submit activity: {e}")
            return False
    
    async def run_migration(self, max_activities: int = 5, headless: bool = False):
        """Run the complete UI migration process"""
        print("🚀 GAINSIGHT UI MIGRATION")
        print("=" * 50)
        
        self.migration_results["start_time"] = time.time()
        
        try:
            # Setup browser
            await self.setup_browser(headless=headless)
            
            # Login to Gainsight
            login_success = await self.login_to_gainsight()
            if not login_success:
                print("❌ Login failed. Check credentials and try again.")
                return False
            
            print("✅ Login successful")
            
            # Navigate to customer timeline
            nav_success = await self.navigate_to_customer_timeline()
            if not nav_success:
                print("❌ Navigation to timeline failed")
                return False
            
            print("✅ Navigated to customer timeline")
            
            # Load activity data
            if not self.data_file.exists():
                print(f"❌ Data file not found: {self.data_file}")
                return False
            
            with open(self.data_file, 'r') as f:
                activities = json.load(f)
            
            self.migration_results["total_activities"] = min(len(activities), max_activities)
            
            print(f"📊 Found {len(activities)} activities, processing first {max_activities}")
            
            # Process activities
            for i, activity in enumerate(activities[:max_activities]):
                print(f"\n📝 Processing activity {i+1}/{max_activities}...")
                
                success = await self.create_activity(activity)
                
                if success:
                    print(f"  ✅ Activity {i+1} created successfully")
                else:
                    print(f"  ❌ Activity {i+1} failed")
                
                # Small delay between activities
                await self.page.wait_for_timeout(2000)
            
            self.migration_results["end_time"] = time.time()
            
            # Generate results summary
            self.generate_migration_summary()
            
            return True
            
        except Exception as e:
            logger.error(f"Migration failed: {e}")
            print(f"❌ Migration failed: {e}")
            return False
        
        finally:
            if self.browser:
                await self.browser.close()
    
    def generate_migration_summary(self):
        """Generate and save migration summary"""
        duration = self.migration_results["end_time"] - self.migration_results["start_time"]
        success_rate = (self.migration_results["successful"] / self.migration_results["total_activities"]) * 100 if self.migration_results["total_activities"] > 0 else 0
        
        print(f"\n📋 MIGRATION SUMMARY")
        print(f"=" * 50)
        print(f"  📊 Total activities: {self.migration_results['total_activities']}")
        print(f"  ✅ Successful: {self.migration_results['successful']}")
        print(f"  ❌ Failed: {self.migration_results['failed']}")
        print(f"  📈 Success rate: {success_rate:.1f}%")
        print(f"  ⏱️  Duration: {duration:.1f} seconds")
        
        if self.migration_results["errors"]:
            print(f"\n❌ Errors encountered:")
            for error in self.migration_results["errors"][:5]:  # Show first 5 errors
                print(f"    • {error['subject'][:30]}...: {error['error']}")
        
        # Save detailed results
        with open(self.results_file, 'w') as f:
            json.dump(self.migration_results, f, indent=2)
        
        print(f"\n💾 Detailed results saved to: {self.results_file}")

async def main():
    """Main function to run the UI automation"""
    import argparse
    
    parser = argparse.ArgumentParser(description="Gainsight UI Migration Automation")
    parser.add_argument("--max-activities", type=int, default=5, help="Maximum number of activities to migrate")
    parser.add_argument("--headless", action="store_true", help="Run in headless mode")
    
    args = parser.parse_args()
    
    automator = GainsightUIAutomator()
    success = await automator.run_migration(
        max_activities=args.max_activities,
        headless=args.headless
    )
    
    if success:
        print("\n🎉 Migration completed!")
    else:
        print("\n💥 Migration failed!")

if __name__ == "__main__":
    asyncio.run(main())

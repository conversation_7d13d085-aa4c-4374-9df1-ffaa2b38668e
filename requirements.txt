# Core LLM and Agent Framework
openai>=1.50.0
anthropic>=0.38.0
langchain>=0.3.0
langchain-openai>=0.2.0
langchain-anthropic>=0.2.0
langchain-google-genai>=2.0.0
langgraph>=0.2.0

# Enhanced Browser Automation with Browser-Use
browser-use>=0.2.0
"browser-use[memory]">=0.2.0  # Memory features with mem0ai
playwright>=1.48.0
selenium>=4.26.0

# Persistent Memory with Letta (formerly MemGPT)
letta>=0.6.0
letta-client>=0.6.0

# Memory and Persistent Agents (compatible versions)
# letta-client>=0.1.89  # Use latest stable version (replaced by letta-client above)

# Vector Database and Embeddings
pinecone-client>=5.0.0
faiss-cpu>=1.8.0
chromadb>=0.5.0
sentence-transformers>=3.0.0
qdrant-client>=1.9.0

# Enhanced Memory and AI
mem0ai>=0.1.93  # Advanced memory for browser-use

# Optional Advanced Features
easyocr>=1.7.0
opencv-python>=4.10.0

# Database and Storage
psycopg2-binary>=2.9.0
redis>=5.1.0

# Knowledge Graph Support
networkx>=3.4.0
rdflib>=7.0.0
py2neo>=2023.1.0  # Neo4j integration

# Utilities
python-dotenv>=1.0.0
pydantic>=2.9.0
beautifulsoup4>=4.12.0
requests>=2.32.0
pillow>=10.4.0
numpy>=2.1.0
asyncio-throttle>=1.0.2

# Development and Monitoring
pytest>=8.3.0
black>=24.10.0
flake8>=7.1.0

# Enhanced Performance and Processing
ujson>=5.10.0  # Faster JSON processing
aiohttp>=3.10.0  # Async HTTP client
tenacity>=9.0.0  # Retry logic
backoff>=2.2.0  # Exponential backoff

# Workflow and State Management
langgraph-checkpoint>=1.0.0  # For workflow checkpointing
langgraph-checkpoint-sqlite>=1.0.0  # SQLite checkpointing

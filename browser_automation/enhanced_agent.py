"""
Enhanced Browser automation system with Browser-Use integration and real-time selector optimization

This module integrates Browser-Use as the primary automation layer while maintaining
backward compatibility with the existing selector optimization system.
"""

import asyncio
import json
import logging
from typing import Dict, List, Optional, Any, Tuple
from datetime import datetime
from urllib.parse import urlparse
import os
import sys

# Add the parent directory to Python path for imports
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

logger = logging.getLogger(__name__)

# Browser-Use integration
try:
    from browser_use import Agent as BrowserUseAgent, Controller
    from browser_use.agent.memory import MemoryConfig
    from langchain_openai import ChatOpenAI
    BROWSER_USE_AVAILABLE = True
    logger.info("✅ Browser-Use imported successfully")
except ImportError as e:
    logger.warning(f"Browser-Use not available: {e}")
    BROWSER_USE_AVAILABLE = False

# Letta integration
try:
    from letta_client import Letta, MessageCreate
    LETTA_AVAILABLE = True
except ImportError:
    LETTA_AVAILABLE = False

from config import config

class BrowserUseIntegration:
    """
    Integration layer for Browser-Use with enhanced memory and learning capabilities
    """
    
    def __init__(self, memory_manager=None):
        self.browser_use_enabled = BROWSER_USE_AVAILABLE and config.browser.enable_browser_use
        self.memory_manager = memory_manager
        self.agent = None
        self.controller = None
        self.session_data = {}
        self.learning_buffer = []
        
    async def initialize(self):
        """Initialize Browser-Use agent with enhanced configuration"""
        if not self.browser_use_enabled:
            logger.info("Browser-Use not available or disabled, using legacy system")
            return False
        
        try:
            # Create enhanced controller with custom actions
            self.controller = Controller()
            await self._register_custom_actions()
            
            # Initialize LLM for Browser-Use
            llm = self._get_optimal_llm()
            
            # Configure Browser-Use memory if available
            memory_config = None
            if config.browser.browser_use_enable_memory:
                memory_config = MemoryConfig(
                    llm_instance=llm,
                    agent_id=config.memory.letta_agent_name,
                    memory_interval=config.browser.browser_use_memory_interval,
                    embedder_provider="openai",
                    embedder_model="text-embedding-3-small",
                    embedder_dims=1536,
                    vector_store_provider="qdrant",
                    vector_store_collection_name="browser_use_memories",
                    vector_store_config_override={
                        "host": "localhost",
                        "port": 6333
                    }
                )
            
            # Create Browser-Use agent with enhanced configuration
            # Note: max_steps is not a constructor parameter, it's used in run()
            self.agent = BrowserUseAgent(
                task="",  # Will be set per task
                llm=llm,
                controller=self.controller,
                enable_memory=config.browser.browser_use_enable_memory,
                memory_config=memory_config,
                use_vision=config.browser.browser_use_use_vision,
                save_conversation_path="./data/browser_use_conversations" if config.browser.browser_use_save_conversation else None
            )
            
            logger.info("✅ Browser-Use agent initialized successfully")
            return True
            
        except Exception as e:
            logger.error(f"Failed to initialize Browser-Use: {e}")
            self.browser_use_enabled = False
            return False
    
    def _get_optimal_llm(self):
        """Get the optimal LLM for Browser-Use based on configuration"""
        model_name = config.get_model_for_task("browser_automation")
        
        # Get API configuration for the selected model
        api_config = config.get_api_config_for_model(model_name)
        
        # Use OpenRouter models with working API key
        if api_config["provider"] == "openrouter":
            return ChatOpenAI(
                model=model_name,
                temperature=0.1,
                api_key=api_config["api_key"],
                base_url=api_config["base_url"]
            )
        elif api_config["provider"] == "openai" and config.llm.openai_api_key and "your_" not in config.llm.openai_api_key:
            return ChatOpenAI(
                model=model_name,
                temperature=0.1,
                api_key=api_config["api_key"]
            )
        else:
            # Default to OpenRouter with working API key
            return ChatOpenAI(
                model="meta-llama/llama-3.3-70b-instruct:free",
                temperature=0.1,
                api_key=config.llm.openrouter_api_key,
                base_url=config.llm.openrouter_base_url
            )
    
    async def _register_custom_actions(self):
        """Register custom actions for the Browser-Use controller"""
        
        @self.controller.action('Save important information to memory')
        def save_to_memory(information: str, context: str = "general") -> Any:
            """Save important information to persistent memory"""
            try:
                if self.memory_manager:
                    # Store in memory manager
                    memory_data = {
                        "information": information,
                        "context": context,
                        "timestamp": datetime.now().isoformat(),
                        "source": "browser_use_action"
                    }
                    
                    # Add to learning buffer for later processing
                    self.learning_buffer.append(memory_data)
                    
                    return f"✅ Saved to memory: {information[:100]}..."
                else:
                    return "⚠️ Memory manager not available"
            except Exception as e:
                return f"❌ Failed to save to memory: {e}"
        
        @self.controller.action('Extract structured data from current page')
        def extract_structured_data(data_type: str = "general") -> Any:
            """Extract structured data from the current page"""
            try:
                # This would integrate with the page analysis system
                return f"✅ Extracted {data_type} data from current page"
            except Exception as e:
                return f"❌ Failed to extract data: {e}"
        
        @self.controller.action('Search Google for information')
        def search_google(query: str) -> Any:
            """Search Google for specific information"""
            try:
                # This would integrate with search functionality
                return f"✅ Searched Google for: {query}"
            except Exception as e:
                return f"❌ Failed to search: {e}"
        
        @self.controller.action('Analyze page content for patterns')
        def analyze_page_content(analysis_type: str = "general") -> Any:
            """Analyze current page content for patterns and insights"""
            try:
                # This would integrate with content analysis
                return f"✅ Analyzed page content for {analysis_type} patterns"
            except Exception as e:
                return f"❌ Failed to analyze content: {e}"
        
        logger.info("✅ Custom Browser-Use actions registered")
    
    async def execute_task(self, task_description: str, context: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """Execute a task using Browser-Use with enhanced context"""
        if not self.browser_use_enabled or not self.agent:
            return {"success": False, "error": "Browser-Use not available"}
        
        try:
            # Prepare enhanced task description with context
            enhanced_task = await self._prepare_enhanced_task(task_description, context or {})
            
            # Set the task for the agent
            self.agent.task = enhanced_task
            
            # Execute with Browser-Use
            logger.info(f"🚀 Executing task with Browser-Use: {task_description}")
            
            # Run the agent
            history = await self.agent.run(
                max_steps=config.browser.browser_use_max_steps
            )
            
            # Process results and extract insights
            result = await self._process_browser_use_results(history, task_description)
            
            # Store learning data
            await self._store_learning_data(task_description, result)
            
            return result
            
        except Exception as e:
            logger.error(f"Browser-Use execution failed: {e}")
            return {
                "success": False,
                "error": str(e),
                "execution_method": "browser_use_failed"
            }
    
    async def _prepare_enhanced_task(self, task_description: str, context: Dict[str, Any]) -> str:
        """Prepare enhanced task description with memory context"""
        enhanced_parts = [task_description]
        
        # Add memory context if available
        if self.memory_manager and context.get("memory_context"):
            memory_context = context["memory_context"]
            
            if memory_context.get("user_preferences"):
                enhanced_parts.append(f"\nUser preferences: {json.dumps(memory_context['user_preferences'])}")
            
            if memory_context.get("similar_tasks"):
                enhanced_parts.append(f"\nSimilar past tasks: {len(memory_context['similar_tasks'])} found")
            
            if memory_context.get("relevant_websites"):
                enhanced_parts.append(f"\nKnown websites: {list(memory_context['relevant_websites'].keys())}")
        
        # Add custom actions guidance
        enhanced_parts.append(f"\nAvailable custom actions: {', '.join(config.browser.browser_use_controller_actions)}")
        enhanced_parts.append("\nRemember to save important information using the save_to_memory action.")
        
        return "\n".join(enhanced_parts)
    
    async def _process_browser_use_results(self, history, task_description: str) -> Dict[str, Any]:
        """Process Browser-Use execution results and extract insights"""
        try:
            # Extract basic metrics
            total_steps = len(history.model_actions()) if hasattr(history, 'model_actions') else 0
            urls_visited = history.urls() if hasattr(history, 'urls') else []
            screenshots = history.screenshots() if hasattr(history, 'screenshots') else []
            errors = history.errors() if hasattr(history, 'errors') else []
            
            # Determine success based on completion and errors
            success = total_steps > 0 and len(errors) == 0
            
            result = {
                "success": success,
                "execution_method": "browser_use",
                "steps_taken": total_steps,
                "urls_visited": urls_visited,
                "screenshots_taken": len(screenshots),
                "errors": [str(e) for e in errors],
                "execution_time": 0,  # Would need to be calculated
                "final_url": urls_visited[-1] if urls_visited else None,
                "browser_use_history": {
                    "total_actions": total_steps,
                    "action_types": history.action_names() if hasattr(history, 'action_names') else [],
                    "extracted_content": history.extracted_content() if hasattr(history, 'extracted_content') else []
                }
            }
            
            # Add learning insights
            if success:
                result["strategies"] = self._extract_success_strategies(history)
                result["learned_patterns"] = self._extract_learned_patterns(history)
            
            return result
            
        except Exception as e:
            logger.error(f"Failed to process Browser-Use results: {e}")
            return {
                "success": False,
                "error": f"Result processing failed: {e}",
                "execution_method": "browser_use_failed"
            }
    
    def _extract_success_strategies(self, history) -> List[str]:
        """Extract successful strategies from Browser-Use execution"""
        strategies = []
        
        try:
            if hasattr(history, 'model_actions'):
                actions = history.model_actions()
                
                # Analyze action patterns
                action_types = [action.get('action_type', 'unknown') for action in actions]
                common_patterns = self._find_common_patterns(action_types)
                
                for pattern in common_patterns:
                    strategies.append(f"Effective action sequence: {' -> '.join(pattern)}")
            
            # Add custom action usage
            custom_actions_used = [action for action in config.browser.browser_use_controller_actions 
                                 if action in str(history)]
            
            if custom_actions_used:
                strategies.append(f"Successfully used custom actions: {', '.join(custom_actions_used)}")
            
        except Exception as e:
            logger.debug(f"Could not extract strategies: {e}")
        
        return strategies
    
    def _extract_learned_patterns(self, history) -> List[str]:
        """Extract learned patterns from Browser-Use execution"""
        patterns = []
        
        try:
            # Extract URL patterns
            if hasattr(history, 'urls'):
                urls = history.urls()
                domains = [urlparse(url).netloc for url in urls if url]
                unique_domains = list(set(domains))
                
                if unique_domains:
                    patterns.append(f"Interacted with domains: {', '.join(unique_domains)}")
            
            # Extract action patterns
            if hasattr(history, 'action_names'):
                action_names = history.action_names()
                action_frequency = {}
                
                for action in action_names:
                    action_frequency[action] = action_frequency.get(action, 0) + 1
                
                most_used = max(action_frequency, key=action_frequency.get) if action_frequency else None
                if most_used:
                    patterns.append(f"Most used action: {most_used} ({action_frequency[most_used]} times)")
            
        except Exception as e:
            logger.debug(f"Could not extract patterns: {e}")
        
        return patterns
    
    def _find_common_patterns(self, sequence: List[str], min_length: int = 2) -> List[List[str]]:
        """Find common patterns in action sequences"""
        patterns = []
        
        for length in range(min_length, min(len(sequence) + 1, 5)):
            for i in range(len(sequence) - length + 1):
                pattern = sequence[i:i + length]
                
                # Count occurrences
                count = 0
                for j in range(len(sequence) - length + 1):
                    if sequence[j:j + length] == pattern:
                        count += 1
                
                if count >= 2 and pattern not in patterns:
                    patterns.append(pattern)
        
        return patterns
    
    async def _store_learning_data(self, task_description: str, result: Dict[str, Any]):
        """Store learning data from Browser-Use execution"""
        try:
            if self.memory_manager and result.get("success"):
                # Store successful task result
                await self.memory_manager.store_task_result(task_description, result, True)
                
                # Store any buffered learning data
                for learning_item in self.learning_buffer:
                    # This could be enhanced to store in different memory blocks
                    pass
                
                # Clear the buffer
                self.learning_buffer.clear()
                
                logger.debug(f"Stored learning data for task: {task_description}")
            
        except Exception as e:
            logger.error(f"Failed to store learning data: {e}")

class SelectorCaptureHook:
    """
    Hook that captures selectors discovered by the agent in real-time
    """
    
    def __init__(self):
        self.captured_selectors = []
        self.current_domain = None
        self.current_context = {}
        
    async def capture_successful_action(self, 
                                      action_type: str,
                                      selector_data: Dict[str, Any],
                                      page_context: Dict[str, Any]) -> None:
        """
        Capture a successful action's selector for future optimization
        """
        try:
            # Import here to avoid circular imports
            from .playwright_generator.selector_optimizer import selector_optimizer
            
            # Extract domain from current URL
            current_url = page_context.get("page_url", "")
            if current_url:
                parsed_url = urlparse(current_url)
                domain = parsed_url.netloc.replace("www.", "")
                
                # Prepare context for selector optimization
                context = {
                    "page_title": page_context.get("page_title", ""),
                    "page_url": current_url,
                    "element_text": selector_data.get("element_text", ""),
                    "element_attributes": selector_data.get("element_attributes", {}),
                    "viewport_size": page_context.get("viewport_size", {}),
                    "timestamp": datetime.now().isoformat()
                }
                
                # Capture the selector discovery
                result = await selector_optimizer.capture_agent_selector_discovery(
                    domain=domain,
                    action_type=action_type,
                    selector_data=selector_data,
                    context=context
                )
                
                if result and not result.get("error"):
                    logger.info(f"📝 Captured {action_type} selector for {domain}: "
                              f"Quality={result.get('quality_score', 0):.2f}, "
                              f"Token savings={result.get('estimated_token_savings', 0)}")
                    
                    # Store for session summary
                    self.captured_selectors.append({
                        "action_type": action_type,
                        "domain": domain,
                        "quality_score": result.get("quality_score", 0),
                        "selector": selector_data.get("xpath") or selector_data.get("css_selector", ""),
                        "recommendations": result.get("recommendations", [])
                    })
                
        except Exception as e:
            logger.debug(f"Failed to capture selector: {e}")
    
    def get_session_summary(self) -> Dict[str, Any]:
        """
        Get summary of selectors captured in this session
        """
        if not self.captured_selectors:
            return {"no_selectors_captured": True}
        
        total_selectors = len(self.captured_selectors)
        high_quality_selectors = len([s for s in self.captured_selectors if s["quality_score"] > 0.7])
        domains_learned = len(set(s["domain"] for s in self.captured_selectors))
        
        avg_quality = sum(s["quality_score"] for s in self.captured_selectors) / total_selectors
        estimated_savings = sum(200 * s["quality_score"] for s in self.captured_selectors)
        
        return {
            "total_selectors_captured": total_selectors,
            "high_quality_selectors": high_quality_selectors,
            "domains_learned": domains_learned,
            "average_quality_score": avg_quality,
            "estimated_total_token_savings": int(estimated_savings),
            "action_types_learned": list(set(s["action_type"] for s in self.captured_selectors)),
            "top_recommendations": self._get_top_recommendations()
        }
    
    def _get_top_recommendations(self) -> List[str]:
        """Get most common recommendations from captured selectors"""
        all_recommendations = []
        for selector in self.captured_selectors:
            all_recommendations.extend(selector.get("recommendations", []))
        
        # Count and return top 3 most common recommendations
        from collections import Counter
        common_recommendations = Counter(all_recommendations).most_common(3)
        
        return [rec[0] for rec in common_recommendations]

# Enhance the existing browser automation agent with Browser-Use integration
class EnhancedBrowserAutomationAgent:
    """
    Enhanced browser automation agent with Browser-Use integration as primary layer
    """
    
    def __init__(self, base_agent=None, memory_manager=None):
        self.base_agent = base_agent  # Legacy agent for fallback
        self.memory_manager = memory_manager
        
        # Initialize Browser-Use integration
        self.browser_use = BrowserUseIntegration(memory_manager)
        
        # Legacy selector capture (maintained for compatibility)
        self.selector_hook = SelectorCaptureHook()
        self.enhanced_session_data = {}
        
    async def initialize(self) -> bool:
        """Initialize all components"""
        try:
            # Initialize Browser-Use first
            browser_use_ready = await self.browser_use.initialize()
            
            if browser_use_ready:
                logger.info("✅ Enhanced agent initialized with Browser-Use")
                return True
            else:
                logger.info("⚠️ Browser-Use not available, using legacy agent")
                return self.base_agent is not None
                
        except Exception as e:
            logger.error(f"Failed to initialize enhanced agent: {e}")
            return False
        
    async def execute_with_selector_capture(self,
                                          task_description: str,
                                          context: Optional[Dict[str, Any]] = None,
                                          record_session: bool = True,
                                          max_steps: int = None) -> Dict[str, Any]:
        """
        Execute task with Browser-Use as primary method, fallback to legacy if needed
        """
        
        # Try Browser-Use first
        if self.browser_use.browser_use_enabled:
            try:
                logger.info("🚀 Using Browser-Use for task execution")
                result = await self.browser_use.execute_task(task_description, context)
                
                if result.get("success"):
                    # Enhance result with selector capture data (for compatibility)
                    result["selector_capture"] = self._create_selector_summary(result)
                    result["enhanced"] = True
                    result["execution_method"] = "browser_use"
                    
                    return result
                else:
                    logger.warning("Browser-Use execution failed, trying legacy method")
                    
            except Exception as e:
                logger.error(f"Browser-Use execution error: {e}")
        
        # Fallback to legacy method
        if self.base_agent:
            logger.info("🔄 Falling back to legacy browser automation")
            return await self._execute_legacy_method(task_description, context, record_session, max_steps)
        else:
            return {
                "success": False,
                "error": "No browser automation method available",
                "execution_method": "none_available"
            }
    
    async def _execute_legacy_method(self, task_description: str, context: Optional[Dict[str, Any]], 
                                   record_session: bool, max_steps: int) -> Dict[str, Any]:
        """Execute using the legacy browser automation method"""
        
        # Set up enhanced hooks for legacy system
        original_step_end_hook = getattr(self.base_agent, '_on_step_end_hook', None)
        
        async def enhanced_step_end_hook(step_info: Dict[str, Any]):
            # Call original hook first
            if original_step_end_hook:
                await original_step_end_hook(step_info)
            
            # Capture selector information
            await self._capture_step_selectors(step_info)
        
        # Replace the hook temporarily
        if self.base_agent:
            self.base_agent._on_step_end_hook = enhanced_step_end_hook
        
        try:
            # Execute the task using the base agent
            result = await self.base_agent.execute_task(
                task_description=task_description,
                context=context,
                record_session=record_session,
                max_steps=max_steps
            )
            
            # Enhance the result with selector capture data
            selector_summary = self.selector_hook.get_session_summary()
            result["selector_capture"] = selector_summary
            result["execution_method"] = "legacy_enhanced"
            
            # Generate Playwright script if we captured good selectors
            if selector_summary.get("high_quality_selectors", 0) > 0 and result.get("success"):
                await self._generate_optimized_playwright_script(result, selector_summary)
            
            return result
            
        except Exception as e:
            logger.error(f"Legacy execution failed: {e}")
            return {
                "success": False,
                "error": str(e),
                "execution_method": "legacy_failed"
            }
            
        finally:
            # Restore original hook
            if self.base_agent and original_step_end_hook:
                self.base_agent._on_step_end_hook = original_step_end_hook
    
    def _create_selector_summary(self, browser_use_result: Dict[str, Any]) -> Dict[str, Any]:
        """Create a selector summary from Browser-Use results (for compatibility)"""
        return {
            "total_selectors_captured": browser_use_result.get("steps_taken", 0),
            "high_quality_selectors": browser_use_result.get("steps_taken", 0) // 2,  # Estimate
            "domains_learned": len(set([urlparse(url).netloc for url in browser_use_result.get("urls_visited", [])])),
            "average_quality_score": 0.8 if browser_use_result.get("success") else 0.4,
            "estimated_total_token_savings": browser_use_result.get("steps_taken", 0) * 150,  # Estimate
            "action_types_learned": browser_use_result.get("browser_use_history", {}).get("action_types", []),
            "execution_method": "browser_use"
        }
        
    async def execute_with_selector_capture(self,
                                          task_description: str,
                                          context: Optional[Dict[str, Any]] = None,
                                          record_session: bool = True,
                                          max_steps: int = None) -> Dict[str, Any]:
        """
        Execute task with enhanced selector capture
        """
        # Set up enhanced hooks
        original_step_end_hook = getattr(self.base_agent, '_on_step_end_hook', None)
        
        async def enhanced_step_end_hook(step_info: Dict[str, Any]):
            # Call original hook first
            if original_step_end_hook:
                await original_step_end_hook(step_info)
            
            # Capture selector information
            await self._capture_step_selectors(step_info)
        
        # Replace the hook temporarily
        self.base_agent._on_step_end_hook = enhanced_step_end_hook
        
        try:
            # Execute the task using the base agent
            result = await self.base_agent.execute_task(
                task_description=task_description,
                context=context,
                record_session=record_session,
                max_steps=max_steps
            )
            
            # Enhance the result with selector capture data
            selector_summary = self.selector_hook.get_session_summary()
            result["selector_capture"] = selector_summary
            
            # Generate Playwright script if we captured good selectors
            if selector_summary.get("high_quality_selectors", 0) > 0 and result.get("success"):
                await self._generate_optimized_playwright_script(result, selector_summary)
            
            return result
            
        finally:
            # Restore original hook
            if original_step_end_hook:
                self.base_agent._on_step_end_hook = original_step_end_hook
    
    async def _capture_step_selectors(self, step_info: Dict[str, Any]):
        """Capture selector information from a completed step"""
        try:
            # Extract action type and selector data from step info
            action_type = self._infer_action_type(step_info)
            
            if action_type in ["click", "input", "select", "hover"]:
                # Get current page context
                page_context = await self._get_current_page_context()
                
                # Extract selector data from step info
                selector_data = self._extract_selector_data(step_info)
                
                if selector_data:
                    await self.selector_hook.capture_successful_action(
                        action_type=action_type,
                        selector_data=selector_data,
                        page_context=page_context
                    )
            
        except Exception as e:
            logger.debug(f"Failed to capture step selectors: {e}")
    
    def _infer_action_type(self, step_info: Dict[str, Any]) -> str:
        """Infer action type from step information"""
        completed_action = step_info.get("completed_action", "").lower()
        
        if "click" in completed_action:
            return "click_element_by_index"
        elif "type" in completed_action or "input" in completed_action:
            return "input_text"
        elif "select" in completed_action:
            return "select_dropdown_option"
        elif "hover" in completed_action:
            return "hover_element"
        elif "scroll" in completed_action:
            return "scroll_to_element"
        else:
            return "unknown_action"
    
    def _extract_selector_data(self, step_info: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """Extract selector data from step information"""
        # This would need to be adapted based on how browser-use provides step information
        # For now, simulate the extraction
        
        result = step_info.get("result", {})
        
        if isinstance(result, dict):
            return {
                "xpath": result.get("xpath"),
                "css_selector": result.get("css_selector"),
                "index": result.get("index"),
                "element_text": result.get("text"),
                "element_attributes": result.get("attributes", {}),
                "selector_confidence": result.get("confidence", 0.5)
            }
        
        return None
    
    async def _get_current_page_context(self) -> Dict[str, Any]:
        """Get current page context for selector capture"""
        try:
            current_url = await self.base_agent._get_current_url()
            browser_state = await self.base_agent._get_browser_state()
            
            return {
                "page_url": current_url,
                "page_title": browser_state.get("title", ""),
                "viewport_size": browser_state.get("viewport", {}),
                "timestamp": datetime.now().isoformat()
            }
            
        except Exception as e:
            logger.debug(f"Failed to get page context: {e}")
            return {"timestamp": datetime.now().isoformat()}
    
    async def _generate_optimized_playwright_script(self, result: Dict[str, Any], selector_summary: Dict[str, Any]):
        """Generate optimized Playwright script from captured selectors"""
        try:
            if not result.get("recording_path"):
                return
            
            # Import script generator
            from .playwright_generator.script_generator import PlaywrightScriptGenerator
            
            generator = PlaywrightScriptGenerator()
            
            # Load the recorded session
            with open(result["recording_path"], 'r') as f:
                session_data = json.load(f)
            
            # Generate enhanced script with captured selectors
            enhanced_metadata = await generator.analyze_agent_session(session_data)
            
            if enhanced_metadata and not enhanced_metadata.get("error"):
                # Add selector optimization information
                enhanced_metadata["selector_optimizations"] = selector_summary
                enhanced_metadata["optimization_quality"] = "high" if selector_summary.get("average_quality_score", 0) > 0.7 else "medium"
                
                result["enhanced_playwright_script"] = enhanced_metadata
                
                logger.info(f"🎭 Generated optimized Playwright script: {enhanced_metadata.get('script_path')}")
                logger.info(f"   Token savings potential: {enhanced_metadata.get('estimated_tokens_saved', 0)}")
            
        except Exception as e:
            logger.debug(f"Failed to generate optimized script: {e}")

# Function to create enhanced agent with Browser-Use integration
def create_enhanced_agent(memory_manager=None):
    """Create an enhanced browser automation agent with Browser-Use integration"""
    try:
        # Try to get legacy browser agent for fallback
        legacy_agent = None
        try:
            from . import browser_agent  # Import the global instance
            legacy_agent = browser_agent
        except ImportError:
            logger.warning("Legacy browser agent not available")
        
        # Create enhanced agent with Browser-Use integration
        enhanced_agent = EnhancedBrowserAutomationAgent(
            base_agent=legacy_agent,
            memory_manager=memory_manager
        )
        
        return enhanced_agent
        
    except Exception as e:
        logger.error(f"Failed to create enhanced agent: {e}")
        return None

# Utility functions for easy access with Browser-Use integration
async def execute_task_with_optimization(task_description: str, memory_manager=None, **kwargs) -> Dict[str, Any]:
    """Execute a task with Browser-Use and automatic selector optimization"""
    try:
        enhanced_agent = create_enhanced_agent(memory_manager)
        
        if not enhanced_agent:
            return {"success": False, "error": "Failed to create enhanced agent"}
        
        # Initialize the agent
        initialized = await enhanced_agent.initialize()
        if not initialized:
            return {"success": False, "error": "Failed to initialize enhanced agent"}
        
        # Execute with Browser-Use integration
        return await enhanced_agent.execute_with_selector_capture(task_description, **kwargs)
        
    except Exception as e:
        logger.error(f"Task execution failed: {e}")
        return {"success": False, "error": str(e)}

async def get_domain_selector_library(domain: str) -> Dict[str, Any]:
    """Get generated selector library for a domain (legacy compatibility)"""
    try:
        from .playwright_generator.selector_optimizer import selector_optimizer
        return await selector_optimizer.generate_playwright_selector_library(domain)
    except Exception as e:
        logger.error(f"Failed to get selector library: {e}")
        return {"error": str(e)}

async def get_optimization_statistics() -> Dict[str, Any]:
    """Get current optimization statistics including Browser-Use metrics"""
    try:
        stats = {
            "browser_use_enabled": BROWSER_USE_AVAILABLE and config.browser.enable_browser_use,
            "letta_memory_enabled": LETTA_AVAILABLE and config.memory.letta_enabled,
            "integrations": config.get_integration_status(),
            "timestamp": datetime.now().isoformat()
        }
        
        # Add legacy stats if available
        try:
            from .playwright_generator.selector_optimizer import selector_optimizer
            legacy_stats = await selector_optimizer.get_performance_stats()
            stats["legacy_selector_optimization"] = legacy_stats
        except Exception as e:
            logger.debug(f"Legacy stats not available: {e}")
        
        return stats
        
    except Exception as e:
        logger.error(f"Failed to get optimization stats: {e}")
        return {"error": str(e)}

async def get_browser_use_status() -> Dict[str, Any]:
    """Get Browser-Use integration status and configuration"""
    return {
        "available": BROWSER_USE_AVAILABLE,
        "enabled": config.browser.enable_browser_use,
        "configuration": config.get_browser_use_config() if BROWSER_USE_AVAILABLE else None,
        "letta_integration": {
            "available": LETTA_AVAILABLE,
            "enabled": config.memory.letta_enabled,
            "configuration": config.get_letta_config() if LETTA_AVAILABLE else None
        }
    }

# Enhanced task execution with intelligent method selection
async def execute_intelligent_task(task_description: str, context: Optional[Dict[str, Any]] = None, 
                                 memory_manager=None, prefer_browser_use: bool = True) -> Dict[str, Any]:
    """
    Execute task with intelligent method selection based on:
    - Task complexity
    - Available integrations  
    - Historical success rates
    - Context requirements
    """
    
    # Analyze task to determine best method
    task_analysis = await _analyze_task_requirements(task_description, context or {})
    
    # Select execution method
    method = await _select_execution_method(task_analysis, prefer_browser_use)
    
    # Execute with selected method
    if method == "browser-use":
        return await execute_task_with_optimization(
            task_description, 
            memory_manager=memory_manager,
            context=context
        )
    else:
        # Could add more methods here (direct Playwright, selenium, etc.)
        return await execute_task_with_optimization(
            task_description,
            memory_manager=memory_manager, 
            context=context
        )

async def _analyze_task_requirements(task_description: str, context: Dict[str, Any]) -> Dict[str, Any]:
    """Analyze task to understand requirements and complexity"""
    
    # Simple analysis based on keywords and context
    analysis = {
        "complexity": "medium",
        "requires_vision": any(keyword in task_description.lower() 
                             for keyword in ["screenshot", "image", "visual", "color", "layout"]),
        "requires_forms": any(keyword in task_description.lower() 
                            for keyword in ["form", "input", "submit", "login", "register"]),
        "requires_navigation": any(keyword in task_description.lower() 
                                 for keyword in ["navigate", "go to", "visit", "browse"]),
        "requires_extraction": any(keyword in task_description.lower() 
                                 for keyword in ["extract", "scrape", "collect", "gather"]),
        "domain_known": bool(context.get("memory_context", {}).get("relevant_websites"))
    }
    
    # Determine complexity
    complexity_score = sum([
        analysis["requires_vision"],
        analysis["requires_forms"],
        analysis["requires_navigation"], 
        analysis["requires_extraction"],
        not analysis["domain_known"]
    ])
    
    if complexity_score >= 3:
        analysis["complexity"] = "high"
    elif complexity_score <= 1:
        analysis["complexity"] = "low"
    
    return analysis

async def _select_execution_method(task_analysis: Dict[str, Any], prefer_browser_use: bool) -> str:
    """Select the best execution method based on task analysis"""
    
    # If Browser-Use is available and preferred, use it for most tasks
    if (BROWSER_USE_AVAILABLE and 
        config.browser.enable_browser_use and 
        prefer_browser_use):
        
        # Browser-Use is good for complex tasks with vision requirements
        if (task_analysis["complexity"] in ["medium", "high"] or 
            task_analysis["requires_vision"]):
            return "browser-use"
    
    # Fallback to legacy method
    return "legacy"

"""
🎬 COMPREHENSIVE STEP RECORDER
Records every user action, browser state, and system interaction with high fidelity
"""
import json
import time
import asyncio
import logging
import threading
from datetime import datetime
from typing import Dict, List, Optional, Any, Tuple
from pathlib import Path
import uuid
import base64
import hashlib

from playwright.async_api import Page, Browser
from config import config

logger = logging.getLogger(__name__)

class ComprehensiveStepRecorder:
    """
    🎯 COMPREHENSIVE BROWSER AUTOMATION RECORDER
    
    Records EVERYTHING during browser automation:
    - Every click, type, scroll, navigation
    - Screenshots at every step
    - Network requests and responses
    - Console logs and errors
    - Page source changes
    - Element interactions with precise selectors
    - Timing data for each action
    - Browser state changes (cookies, localStorage, etc.)
    - Video recording (if enabled)
    
    All data is timestamped and cross-referenced for perfect replay capability.
    """
    
    def __init__(self, session_id: Optional[str] = None):
        self.session_id = session_id or f"session_{datetime.now().strftime('%Y%m%d_%H%M%S')}_{uuid.uuid4().hex[:8]}"
        self.recording_active = False
        self.start_time = None
        
        # Storage containers
        self.steps: List[Dict[str, Any]] = []
        self.screenshots: List[Dict[str, Any]] = []
        self.network_events: List[Dict[str, Any]] = []
        self.console_logs: List[Dict[str, Any]] = []
        self.page_sources: List[Dict[str, Any]] = []
        self.browser_states: List[Dict[str, Any]] = []
        self.performance_metrics: List[Dict[str, Any]] = []
        
        # Configuration
        self.config = config.storage
        self.recording_dir = Path(self.config.recordings_dir) / self.session_id
        self.recording_dir.mkdir(parents=True, exist_ok=True)
        
        # Screenshots directory
        self.screenshots_dir = self.recording_dir / "screenshots"
        self.screenshots_dir.mkdir(exist_ok=True)
        
        # Current state tracking
        self.current_page = None
        self.current_url = ""
        self.step_counter = 0
        self.screenshot_counter = 0
        
        # Background tasks
        self.screenshot_task = None
        self.save_task = None
        
        logger.info(f"🎬 Comprehensive recorder initialized: {self.session_id}")
        logger.info(f"📁 Recording directory: {self.recording_dir}")
    
    async def start_recording(self, page: Page, task_description: str = ""):
        """🚀 Start comprehensive recording"""
        try:
            self.recording_active = True
            self.start_time = time.time()
            self.current_page = page
            self.current_url = page.url
            
            # Record initial session metadata
            session_metadata = await self._capture_session_metadata(task_description)
            await self._save_session_metadata(session_metadata)
            
            # Setup event listeners
            await self._setup_event_listeners(page)
            
            # Start background tasks
            if self.config.save_screenshots:
                self.screenshot_task = asyncio.create_task(self._continuous_screenshot_capture())
            
            # Start auto-save task
            self.save_task = asyncio.create_task(self._auto_save_loop())
            
            # Record initial page state
            await self.record_step("session_start", {
                "task_description": task_description,
                "initial_url": self.current_url,
                "session_id": self.session_id,
                "timestamp": datetime.now().isoformat()
            })
            
            logger.info(f"✅ Recording started for session: {self.session_id}")
            return True
            
        except Exception as e:
            logger.error(f"❌ Failed to start recording: {e}")
            return False
    
    async def record_step(self, action_type: str, data: Dict[str, Any], element_info: Optional[Dict[str, Any]] = None):
        """📝 Record a single automation step with comprehensive data"""
        if not self.recording_active:
            return
        
        try:
            self.step_counter += 1
            timestamp = time.time()
            relative_time = timestamp - self.start_time if self.start_time else 0
            
            # Capture current page state
            page_state = await self._capture_page_state() if self.current_page else {}
            
            # Build comprehensive step record
            step_record = {
                "step_id": self.step_counter,
                "session_id": self.session_id,
                "action_type": action_type,
                "timestamp": datetime.fromtimestamp(timestamp).isoformat(),
                "relative_time_seconds": round(relative_time, 3),
                "data": data,
                "element_info": element_info or {},
                "page_state": page_state,
                "url": self.current_url,
                "success": data.get("success", True),
                "error": data.get("error"),
                "execution_time_ms": data.get("execution_time_ms", 0)
            }
            
            # Add screenshot if enabled
            if self.config.save_screenshots and action_type not in ["screenshot", "auto_save"]:
                screenshot_path = await self._take_screenshot(f"step_{self.step_counter}")
                if screenshot_path:
                    step_record["screenshot"] = str(screenshot_path.relative_to(self.recording_dir))
            
            # Add element screenshot if element provided
            if element_info and self.config.save_screenshots:
                element_screenshot = await self._take_element_screenshot(element_info)
                if element_screenshot:
                    step_record["element_screenshot"] = str(element_screenshot.relative_to(self.recording_dir))
            
            # Store the step
            self.steps.append(step_record)
            
            # Log the step
            logger.info(f"📝 Step {self.step_counter}: {action_type} - {data.get('description', 'No description')}")
            
            # Trigger immediate save for important actions
            if action_type in ["click", "input", "navigate", "submit", "error"]:
                await self._save_incremental()
                
        except Exception as e:
            logger.error(f"❌ Failed to record step: {e}")
    
    async def record_network_event(self, event_type: str, request_data: Dict[str, Any]):
        """🌐 Record network request/response"""
        if not self.recording_active or not self.config.record_network_requests:
            return
        
        try:
            network_record = {
                "timestamp": datetime.now().isoformat(),
                "relative_time": time.time() - self.start_time if self.start_time else 0,
                "event_type": event_type,
                "url": request_data.get("url", ""),
                "method": request_data.get("method", ""),
                "status": request_data.get("status"),
                "headers": request_data.get("headers", {}),
                "response_size": request_data.get("response_size", 0),
                "duration_ms": request_data.get("duration_ms", 0)
            }
            
            self.network_events.append(network_record)
            
        except Exception as e:
            logger.error(f"❌ Failed to record network event: {e}")
    
    async def record_console_log(self, log_type: str, message: str, source: str = ""):
        """📢 Record console logs and errors"""
        if not self.recording_active or not self.config.record_console_logs:
            return
        
        try:
            console_record = {
                "timestamp": datetime.now().isoformat(),
                "relative_time": time.time() - self.start_time if self.start_time else 0,
                "type": log_type,
                "message": message,
                "source": source
            }
            
            self.console_logs.append(console_record)
            
        except Exception as e:
            logger.error(f"❌ Failed to record console log: {e}")
    
    async def stop_recording(self, final_status: str = "completed") -> str:
        """🛑 Stop recording and save final data"""
        try:
            if not self.recording_active:
                return ""
            
            self.recording_active = False
            
            # Record final step
            await self.record_step("session_end", {
                "final_status": final_status,
                "total_steps": self.step_counter,
                "total_duration_seconds": time.time() - self.start_time if self.start_time else 0,
                "final_url": self.current_url
            })
            
            # Stop background tasks
            if self.screenshot_task and not self.screenshot_task.done():
                self.screenshot_task.cancel()
            
            if self.save_task and not self.save_task.done():
                self.save_task.cancel()
            
            # Final save
            recording_path = await self._save_complete_recording()
            
            # Generate summary
            summary = await self._generate_recording_summary()
            await self._save_recording_summary(summary)
            
            logger.info(f"✅ Recording completed: {recording_path}")
            logger.info(f"📊 Total steps: {self.step_counter}, Screenshots: {self.screenshot_counter}")
            
            return str(recording_path)
            
        except Exception as e:
            logger.error(f"❌ Failed to stop recording: {e}")
            return ""
    
    async def _capture_session_metadata(self, task_description: str) -> Dict[str, Any]:
        """📋 Capture comprehensive session metadata"""
        try:
            page_info = {}
            if self.current_page:
                page_info = {
                    "url": self.current_page.url,
                    "title": await self.current_page.title(),
                    "viewport": self.current_page.viewport_size,
                    "user_agent": await self.current_page.evaluate("navigator.userAgent")
                }
            
            return {
                "session_id": self.session_id,
                "task_description": task_description,
                "start_time": datetime.now().isoformat(),
                "page_info": page_info,
                "recording_config": {
                    "screenshots_enabled": self.config.save_screenshots,
                    "video_enabled": self.config.save_video,
                    "network_recording": self.config.record_network_requests,
                    "console_recording": self.config.record_console_logs,
                    "quality": self.config.recording_quality
                },
                "system_info": {
                    "python_version": f"{config.__dict__.get('version', 'unknown')}",
                    "browser_automation_version": "2.0.0"
                }
            }
            
        except Exception as e:
            logger.error(f"❌ Failed to capture session metadata: {e}")
            return {"session_id": self.session_id, "error": str(e)}
    
    async def _capture_page_state(self) -> Dict[str, Any]:
        """📊 Capture current page state"""
        if not self.current_page:
            return {}
        
        try:
            # Get page metrics
            state = {
                "url": self.current_page.url,
                "title": await self.current_page.title(),
                "ready_state": await self.current_page.evaluate("document.readyState"),
                "scroll_position": await self.current_page.evaluate("({x: window.scrollX, y: window.scrollY})"),
                "viewport": self.current_page.viewport_size
            }
            
            # Add performance metrics if available
            try:
                performance = await self.current_page.evaluate("""
                    ({
                        loading_time: performance.timing.loadEventEnd - performance.timing.navigationStart,
                        dom_ready: performance.timing.domContentLoadedEventEnd - performance.timing.navigationStart,
                        first_paint: performance.getEntriesByType('paint')[0]?.startTime || 0
                    })
                """)
                state["performance"] = performance
            except:
                pass
            
            return state
            
        except Exception as e:
            logger.error(f"❌ Failed to capture page state: {e}")
            return {"error": str(e)}
    
    async def _take_screenshot(self, name: str) -> Optional[Path]:
        """📸 Take and save screenshot"""
        if not self.current_page or not self.config.save_screenshots:
            return None
        
        try:
            self.screenshot_counter += 1
            screenshot_name = f"{name}.png"
            screenshot_path = self.screenshots_dir / screenshot_name
            
            await self.current_page.screenshot(path=str(screenshot_path), full_page=True)
            
            # Store screenshot metadata
            self.screenshots.append({
                "filename": screenshot_name,
                "path": str(screenshot_path.relative_to(self.recording_dir)),
                "timestamp": datetime.now().isoformat(),
                "step_id": self.step_counter,
                "url": self.current_page.url
            })
            
            return screenshot_path
            
        except Exception as e:
            logger.error(f"❌ Failed to take screenshot: {e}")
            return None
    
    async def _take_element_screenshot(self, element_info: Dict[str, Any]) -> Optional[Path]:
        """📸 Take screenshot of specific element"""
        if not self.current_page or not element_info.get("selector"):
            return None
        
        try:
            selector = element_info["selector"]
            element = await self.current_page.query_selector(selector)
            
            if element:
                screenshot_name = f"element_{self.step_counter}_{hashlib.md5(selector.encode()).hexdigest()[:8]}.png"
                screenshot_path = self.screenshots_dir / screenshot_name
                
                await element.screenshot(path=str(screenshot_path))
                return screenshot_path
                
        except Exception as e:
            logger.debug(f"Could not take element screenshot: {e}")
            
        return None
    
    async def _setup_event_listeners(self, page: Page):
        """🎧 Setup event listeners for automatic recording"""
        try:
            # Network events
            if self.config.record_network_requests:
                page.on("request", lambda req: asyncio.create_task(self.record_network_event("request", {
                    "url": req.url,
                    "method": req.method,
                    "headers": dict(req.headers)
                })))
                
                page.on("response", lambda resp: asyncio.create_task(self.record_network_event("response", {
                    "url": resp.url,
                    "status": resp.status,
                    "headers": dict(resp.headers)
                })))
            
            # Console events
            if self.config.record_console_logs:
                page.on("console", lambda msg: asyncio.create_task(self.record_console_log(
                    msg.type, msg.text, msg.location.get("url", "") if msg.location else ""
                )))
            
            # Page events
            page.on("load", lambda: asyncio.create_task(self.record_step("page_load", {
                "description": "Page fully loaded",
                "url": page.url
            })))
            
            page.on("domcontentloaded", lambda: asyncio.create_task(self.record_step("dom_ready", {
                "description": "DOM content loaded",
                "url": page.url
            })))
            
        except Exception as e:
            logger.error(f"❌ Failed to setup event listeners: {e}")
    
    async def _continuous_screenshot_capture(self):
        """📸 Continuously capture screenshots in background"""
        try:
            while self.recording_active:
                if self.current_page and self.config.save_screenshots:
                    await self._take_screenshot(f"auto_{int(time.time())}")
                
                await asyncio.sleep(self.config.screenshot_interval / 1000)  # Convert ms to seconds
                
        except asyncio.CancelledError:
            logger.info("Screenshot capture task cancelled")
        except Exception as e:
            logger.error(f"❌ Screenshot capture error: {e}")
    
    async def _auto_save_loop(self):
        """💾 Auto-save recording data periodically"""
        try:
            while self.recording_active:
                await asyncio.sleep(30)  # Save every 30 seconds
                await self._save_incremental()
                
        except asyncio.CancelledError:
            logger.info("Auto-save task cancelled")
        except Exception as e:
            logger.error(f"❌ Auto-save error: {e}")
    
    async def _save_incremental(self):
        """💾 Save current recording data incrementally"""
        try:
            incremental_data = {
                "session_id": self.session_id,
                "last_updated": datetime.now().isoformat(),
                "step_count": len(self.steps),
                "screenshot_count": len(self.screenshots),
                "network_events_count": len(self.network_events),
                "console_logs_count": len(self.console_logs),
                "steps": self.steps[-10:] if len(self.steps) > 10 else self.steps  # Last 10 steps
            }
            
            incremental_path = self.recording_dir / "incremental.json"
            with open(incremental_path, 'w') as f:
                json.dump(incremental_data, f, indent=2, default=str)
                
        except Exception as e:
            logger.error(f"❌ Failed to save incremental data: {e}")
    
    async def _save_complete_recording(self) -> Path:
        """💾 Save complete recording data"""
        try:
            complete_recording = {
                "session_metadata": {
                    "session_id": self.session_id,
                    "start_time": datetime.fromtimestamp(self.start_time).isoformat() if self.start_time else None,
                    "end_time": datetime.now().isoformat(),
                    "total_duration_seconds": time.time() - self.start_time if self.start_time else 0,
                    "total_steps": len(self.steps),
                    "recording_quality": self.config.recording_quality
                },
                "steps": self.steps,
                "screenshots": self.screenshots,
                "network_events": self.network_events,
                "console_logs": self.console_logs,
                "page_sources": self.page_sources,
                "browser_states": self.browser_states,
                "performance_metrics": self.performance_metrics,
                "recording_config": {
                    "screenshots_enabled": self.config.save_screenshots,
                    "video_enabled": self.config.save_video,
                    "network_recording": self.config.record_network_requests,
                    "console_recording": self.config.record_console_logs
                }
            }
            
            recording_path = self.recording_dir / "complete_recording.json"
            with open(recording_path, 'w') as f:
                json.dump(complete_recording, f, indent=2, default=str)
            
            # Also save in CSV format if requested
            if self.config.recording_format in ["csv", "both"]:
                await self._save_steps_csv()
            
            return recording_path
            
        except Exception as e:
            logger.error(f"❌ Failed to save complete recording: {e}")
            return self.recording_dir / "error.json"
    
    async def _save_steps_csv(self):
        """💾 Save steps in CSV format"""
        try:
            import pandas as pd
            
            steps_df = pd.DataFrame(self.steps)
            csv_path = self.recording_dir / "steps.csv"
            steps_df.to_csv(csv_path, index=False)
            
        except Exception as e:
            logger.error(f"❌ Failed to save CSV: {e}")
    
    async def _save_session_metadata(self, metadata: Dict[str, Any]):
        """💾 Save session metadata"""
        try:
            metadata_path = self.recording_dir / "session_metadata.json"
            with open(metadata_path, 'w') as f:
                json.dump(metadata, f, indent=2, default=str)
                
        except Exception as e:
            logger.error(f"❌ Failed to save session metadata: {e}")
    
    async def _generate_recording_summary(self) -> Dict[str, Any]:
        """📊 Generate comprehensive recording summary"""
        try:
            total_duration = time.time() - self.start_time if self.start_time else 0
            
            # Analyze step types
            step_types = {}
            successful_steps = 0
            failed_steps = 0
            
            for step in self.steps:
                step_type = step.get("action_type", "unknown")
                step_types[step_type] = step_types.get(step_type, 0) + 1
                
                if step.get("success", True):
                    successful_steps += 1
                else:
                    failed_steps += 1
            
            # Analyze network activity
            network_summary = {
                "total_requests": len(self.network_events),
                "unique_domains": len(set(event.get("url", "").split("//")[-1].split("/")[0] 
                                        for event in self.network_events if event.get("url")))
            }
            
            return {
                "session_id": self.session_id,
                "summary_generated": datetime.now().isoformat(),
                "duration": {
                    "total_seconds": round(total_duration, 2),
                    "formatted": f"{int(total_duration // 60)}m {int(total_duration % 60)}s"
                },
                "steps": {
                    "total": len(self.steps),
                    "successful": successful_steps,
                    "failed": failed_steps,
                    "success_rate": round(successful_steps / len(self.steps) * 100, 1) if self.steps else 0,
                    "types": step_types
                },
                "screenshots": {
                    "total": len(self.screenshots),
                    "average_per_minute": round(len(self.screenshots) / (total_duration / 60), 1) if total_duration > 0 else 0
                },
                "network": network_summary,
                "console_logs": {
                    "total": len(self.console_logs),
                    "by_type": {}
                },
                "recording_quality": self.config.recording_quality,
                "file_sizes": await self._calculate_file_sizes()
            }
            
        except Exception as e:
            logger.error(f"❌ Failed to generate summary: {e}")
            return {"session_id": self.session_id, "error": str(e)}
    
    async def _save_recording_summary(self, summary: Dict[str, Any]):
        """💾 Save recording summary"""
        try:
            summary_path = self.recording_dir / "summary.json"
            with open(summary_path, 'w') as f:
                json.dump(summary, f, indent=2, default=str)
                
        except Exception as e:
            logger.error(f"❌ Failed to save summary: {e}")
    
    async def _calculate_file_sizes(self) -> Dict[str, Any]:
        """📏 Calculate file sizes for recording data"""
        try:
            sizes = {}
            
            for file_path in self.recording_dir.rglob("*"):
                if file_path.is_file():
                    size_mb = file_path.stat().st_size / (1024 * 1024)
                    sizes[file_path.name] = round(size_mb, 2)
            
            total_size = sum(sizes.values())
            
            return {
                "individual_files": sizes,
                "total_mb": round(total_size, 2),
                "screenshots_mb": round(sum(size for name, size in sizes.items() if name.endswith('.png')), 2)
            }
            
        except Exception as e:
            logger.error(f"❌ Failed to calculate file sizes: {e}")
            return {"error": str(e)}
    
    def get_recording_path(self) -> str:
        """📁 Get the recording directory path"""
        return str(self.recording_dir)
    
    def get_session_id(self) -> str:
        """🆔 Get the session ID"""
        return self.session_id
    
    def is_recording(self) -> bool:
        """❓ Check if recording is active"""
        return self.recording_active
    
    def get_stats(self) -> Dict[str, Any]:
        """📊 Get current recording statistics"""
        return {
            "session_id": self.session_id,
            "recording_active": self.recording_active,
            "steps_recorded": len(self.steps),
            "screenshots_taken": len(self.screenshots),
            "network_events": len(self.network_events),
            "console_logs": len(self.console_logs),
            "duration_seconds": time.time() - self.start_time if self.start_time else 0
        }

# Global recorder instance
_current_recorder: Optional[ComprehensiveStepRecorder] = None

def get_current_recorder() -> Optional[ComprehensiveStepRecorder]:
    """Get the current active recorder"""
    return _current_recorder

def set_current_recorder(recorder: ComprehensiveStepRecorder):
    """Set the current active recorder"""
    global _current_recorder
    _current_recorder = recorder

async def start_comprehensive_recording(page: Page, task_description: str = "") -> ComprehensiveStepRecorder:
    """🚀 Start comprehensive recording for a browser automation session"""
    recorder = ComprehensiveStepRecorder()
    success = await recorder.start_recording(page, task_description)
    
    if success:
        set_current_recorder(recorder)
        logger.info(f"✅ Comprehensive recording started: {recorder.get_session_id()}")
        return recorder
    else:
        logger.error("❌ Failed to start comprehensive recording")
        raise RuntimeError("Failed to start recording")

async def stop_comprehensive_recording(final_status: str = "completed") -> str:
    """🛑 Stop the current comprehensive recording"""
    global _current_recorder
    
    if _current_recorder:
        recording_path = await _current_recorder.stop_recording(final_status)
        _current_recorder = None
        return recording_path
    
    return ""

async def record_step(action_type: str, data: Dict[str, Any], element_info: Optional[Dict[str, Any]] = None):
    """📝 Record a step in the current session"""
    if _current_recorder:
        await _current_recorder.record_step(action_type, data, element_info)

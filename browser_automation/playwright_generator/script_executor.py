"""
Playwright Script Executor

Executes generated Playwright scripts and provides performance metrics.
"""

import asyncio
import logging
import json
import subprocess
import tempfile
import os
from typing import Dict, List, Optional, Any
from datetime import datetime
from pathlib import Path

from playwright.async_api import async_playwright, <PERSON><PERSON><PERSON>, <PERSON>rowserContext, Page

logger = logging.getLogger(__name__)

class PlaywrightScriptExecutor:
    """
    Executes generated Playwright scripts with monitoring and optimization
    """
    
    def __init__(self):
        self.browser = None
        self.context = None
        self.page = None
        self.execution_stats = {}
        
    async def execute_script(self, script_path: str, parameters: Dict[str, Any] = None) -> Dict[str, Any]:
        """
        Execute a generated Playwright script
        """
        start_time = datetime.now()
        
        try:
            # Validate script exists
            if not os.path.exists(script_path):
                raise FileNotFoundError(f"Script not found: {script_path}")
            
            # Read script content
            with open(script_path, 'r') as f:
                script_content = f.read()
            
            # Execute using node.js for JavaScript scripts
            if script_path.endswith('.js'):
                result = await self._execute_javascript_script(script_path, parameters or {})
            else:
                # Execute as Python script
                result = await self._execute_python_script(script_content, parameters or {})
            
            execution_time = (datetime.now() - start_time).total_seconds()
            
            return {
                "success": True,
                "execution_time": execution_time,
                "result": result,
                "tokens_saved": self._estimate_tokens_saved_by_execution(),
                "performance_metrics": {
                    "steps_executed": result.get("steps_executed", 0),
                    "errors_encountered": result.get("errors", 0),
                    "pages_visited": result.get("pages_visited", 0)
                }
            }
            
        except Exception as e:
            execution_time = (datetime.now() - start_time).total_seconds()
            logger.error(f"Script execution failed: {e}")
            
            return {
                "success": False,
                "error": str(e),
                "execution_time": execution_time,
                "tokens_saved": 0
            }
    
    async def _execute_javascript_script(self, script_path: str, parameters: Dict[str, Any]) -> Dict[str, Any]:
        """
        Execute JavaScript Playwright script using node.js
        """
        try:
            # Create temporary parameter file if needed
            param_file = None
            if parameters:
                param_file = tempfile.NamedTemporaryFile(mode='w', suffix='.json', delete=False)
                json.dump(parameters, param_file)
                param_file.close()
            
            # Prepare command
            cmd = ['npx', 'playwright', 'test', script_path, '--reporter=json']
            
            if param_file:
                # Modify script to load parameters
                modified_script = await self._inject_parameters(script_path, param_file.name)
                cmd = ['npx', 'playwright', 'test', modified_script, '--reporter=json']
            
            # Execute script
            process = await asyncio.create_subprocess_exec(
                *cmd,
                stdout=asyncio.subprocess.PIPE,
                stderr=asyncio.subprocess.PIPE,
                cwd=os.path.dirname(script_path)
            )
            
            stdout, stderr = await process.communicate()
            
            # Cleanup temp file
            if param_file and os.path.exists(param_file.name):
                os.unlink(param_file.name)
            
            # Parse results
            if process.returncode == 0:
                try:
                    result_data = json.loads(stdout.decode())
                    return {
                        "steps_executed": len(result_data.get("tests", [])),
                        "pages_visited": 1,  # Estimate
                        "playwright_output": result_data
                    }
                except json.JSONDecodeError:
                    return {
                        "steps_executed": 1,
                        "pages_visited": 1,
                        "raw_output": stdout.decode()
                    }
            else:
                raise Exception(f"Script failed with return code {process.returncode}: {stderr.decode()}")
                
        except Exception as e:
            logger.error(f"JavaScript execution failed: {e}")
            raise
    
    async def _execute_python_script(self, script_content: str, parameters: Dict[str, Any]) -> Dict[str, Any]:
        """
        Execute Python Playwright script
        """
        try:
            async with async_playwright() as p:
                # Launch browser
                browser = await p.chromium.launch(headless=True)
                context = await browser.new_context()
                page = await context.new_page()
                
                steps_executed = 0
                pages_visited = set()
                
                # Create execution environment
                exec_globals = {
                    'page': page,
                    'context': context,
                    'browser': browser,
                    'parameters': parameters,
                    'asyncio': asyncio,
                    'steps_executed': 0,
                }
                
                # Execute script
                exec(script_content, exec_globals)
                
                # Wait for any pending operations
                await asyncio.sleep(1)
                
                # Collect metrics
                final_url = page.url
                pages_visited.add(final_url)
                
                # Cleanup
                await browser.close()
                
                return {
                    "steps_executed": exec_globals.get('steps_executed', 1),
                    "pages_visited": len(pages_visited),
                    "final_url": final_url
                }
                
        except Exception as e:
            logger.error(f"Python execution failed: {e}")
            raise
    
    async def _inject_parameters(self, script_path: str, param_file: str) -> str:
        """
        Create a modified script that loads parameters from file
        """
        try:
            with open(script_path, 'r') as f:
                script_content = f.read()
            
            # Create modified script
            param_injection = f"""
// Load parameters from file
const fs = require('fs');
const parameters = JSON.parse(fs.readFileSync('{param_file}', 'utf8'));
"""
            
            modified_content = param_injection + script_content
            
            # Save to temporary file
            temp_script = tempfile.NamedTemporaryFile(mode='w', suffix='.js', delete=False)
            temp_script.write(modified_content)
            temp_script.close()
            
            return temp_script.name
            
        except Exception as e:
            logger.error(f"Parameter injection failed: {e}")
            return script_path
    
    def _estimate_tokens_saved_by_execution(self) -> int:
        """
        Estimate tokens saved by direct execution vs LLM calls
        """
        # Conservative estimate: Each step would have required 200-400 tokens with LLM
        # Direct execution uses 0 tokens
        return 300  # Average per execution
    
    async def execute_with_retry(self, script_path: str, parameters: Dict[str, Any] = None, max_retries: int = 3) -> Dict[str, Any]:
        """
        Execute script with retry logic for resilience
        """
        last_error = None
        
        for attempt in range(max_retries):
            try:
                logger.info(f"Executing script (attempt {attempt + 1}/{max_retries}): {script_path}")
                
                result = await self.execute_script(script_path, parameters)
                
                if result["success"]:
                    if attempt > 0:
                        logger.info(f"Script succeeded on attempt {attempt + 1}")
                    return result
                else:
                    last_error = result.get("error", "Unknown error")
                    
            except Exception as e:
                last_error = str(e)
                logger.warning(f"Attempt {attempt + 1} failed: {e}")
                
                if attempt < max_retries - 1:
                    # Wait before retry
                    await asyncio.sleep(2 ** attempt)  # Exponential backoff
        
        return {
            "success": False,
            "error": f"Failed after {max_retries} attempts: {last_error}",
            "tokens_saved": 0
        }
    
    async def batch_execute(self, scripts: List[str], parameters: Dict[str, Any] = None) -> Dict[str, Any]:
        """
        Execute multiple scripts in batch
        """
        results = []
        total_tokens_saved = 0
        successful_executions = 0
        
        start_time = datetime.now()
        
        for script_path in scripts:
            try:
                result = await self.execute_script(script_path, parameters)
                results.append({
                    "script": script_path,
                    "result": result
                })
                
                if result["success"]:
                    successful_executions += 1
                    total_tokens_saved += result.get("tokens_saved", 0)
                    
            except Exception as e:
                results.append({
                    "script": script_path,
                    "result": {"success": False, "error": str(e)}
                })
        
        total_time = (datetime.now() - start_time).total_seconds()
        
        return {
            "total_scripts": len(scripts),
            "successful_executions": successful_executions,
            "total_execution_time": total_time,
            "total_tokens_saved": total_tokens_saved,
            "results": results,
            "success_rate": successful_executions / len(scripts) if scripts else 0
        }
    
    async def benchmark_performance(self, script_path: str, iterations: int = 5) -> Dict[str, Any]:
        """
        Benchmark script performance over multiple runs
        """
        execution_times = []
        successful_runs = 0
        
        for i in range(iterations):
            try:
                result = await self.execute_script(script_path)
                
                if result["success"]:
                    execution_times.append(result["execution_time"])
                    successful_runs += 1
                    
            except Exception as e:
                logger.warning(f"Benchmark iteration {i + 1} failed: {e}")
        
        if execution_times:
            avg_time = sum(execution_times) / len(execution_times)
            min_time = min(execution_times)
            max_time = max(execution_times)
            
            return {
                "iterations": iterations,
                "successful_runs": successful_runs,
                "success_rate": successful_runs / iterations,
                "average_execution_time": avg_time,
                "min_execution_time": min_time,
                "max_execution_time": max_time,
                "consistency_score": 1 - (max_time - min_time) / avg_time if avg_time > 0 else 0
            }
        else:
            return {
                "iterations": iterations,
                "successful_runs": 0,
                "success_rate": 0,
                "error": "No successful executions"
            }
    
    async def cleanup(self):
        """Cleanup resources"""
        try:
            if self.page:
                await self.page.close()
            if self.context:
                await self.context.close()
            if self.browser:
                await self.browser.close()
            logger.info("Script executor cleanup completed")
        except Exception as e:
            logger.error(f"Error during cleanup: {e}")

"""
Playwright Script Generator

Converts successful browser automation agent actions into reusable Playwright scripts.
This enables faster execution of common patterns without requiring LLM calls.
"""

import json
import logging
import asyncio
from typing import Dict, List, Optional, Any, Union, Tuple
from datetime import datetime
from pathlib import Path
import hashlib
import re

from playwright.async_api import async_playwright, <PERSON><PERSON><PERSON>, BrowserContext, Page

logger = logging.getLogger(__name__)

class PlaywrightScriptGenerator:
    """
    Main class for generating Playwright scripts from agent actions
    """
    
    def __init__(self, scripts_dir: str = "data/playwright_scripts"):
        self.scripts_dir = Path(scripts_dir)
        self.scripts_dir.mkdir(parents=True, exist_ok=True)
        self.generated_scripts = {}
        self.action_patterns = {}
        self.selector_library = {}
        
    async def analyze_agent_session(self, session_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Analyze a successful agent session and extract patterns for Playwright conversion
        """
        try:
            actions = session_data.get("actions", [])
            session_id = session_data.get("session_id", "unknown")
            
            logger.info(f"Analyzing session {session_id} with {len(actions)} actions")
            
            # Extract action patterns
            patterns = await self._extract_action_patterns(actions)
            
            # Generate Playwright script
            script_content = await self._generate_playwright_script(patterns, session_id)
            
            # Save script
            script_path = self.scripts_dir / f"{session_id}.js"
            await self._save_script(script_path, script_content)
            
            # Store metadata
            metadata = {
                "session_id": session_id,
                "original_actions": len(actions),
                "script_path": str(script_path),
                "patterns_extracted": len(patterns),
                "generated_at": datetime.now().isoformat(),
                "selectors_found": len(self._extract_selectors_from_patterns(patterns)),
                "estimated_tokens_saved": self._estimate_tokens_saved(actions)
            }
            
            self.generated_scripts[session_id] = metadata
            return metadata
            
        except Exception as e:
            logger.error(f"Failed to analyze session: {e}")
            return {"error": str(e)}
    
    async def _extract_action_patterns(self, actions: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """
        Extract actionable patterns from agent actions
        """
        patterns = []
        
        for action in actions:
            action_type = action.get("action_type", "unknown")
            details = action.get("details", {})
            
            pattern = {
                "type": action_type,
                "timestamp": action.get("timestamp"),
                "playwright_equivalent": None,
                "selectors": [],
                "parameters": {}
            }
            
            # Convert different action types to Playwright equivalents
            if action_type == "open_tab":
                pattern.update({
                    "playwright_equivalent": "page.goto",
                    "parameters": {"url": details.get("url", "")},
                    "code_template": "await page.goto('{url}');"
                })
                
            elif action_type == "click_element_by_index":
                # Extract selector information
                index = details.get("index", 0)
                xpath = details.get("xpath")
                
                if xpath:
                    selector = xpath
                    code_template = f"await page.click('{selector}');"
                else:
                    code_template = f"await page.locator(':nth-child({index + 1})').click();"
                
                pattern.update({
                    "playwright_equivalent": "page.click",
                    "parameters": {"index": index, "xpath": xpath, "selector": xpath or f":nth-child({index + 1})"},
                    "selectors": [xpath] if xpath else [],
                    "code_template": code_template
                })
                
            elif action_type == "input_text":
                index = details.get("index", 0)
                text = details.get("text", "")
                xpath = details.get("xpath")
                
                if xpath:
                    selector = xpath
                    code_template = f"await page.fill('{selector}', '{text}');"
                else:
                    code_template = f"await page.fill(':nth-child({index + 1})', '{text}');"
                
                pattern.update({
                    "playwright_equivalent": "page.fill",
                    "parameters": {"index": index, "text": text, "xpath": xpath, "selector": xpath or f":nth-child({index + 1})"},
                    "selectors": [xpath] if xpath else [],
                    "code_template": code_template
                })
                
            elif action_type == "extract_content":
                goal = details.get("goal", "")
                include_links = details.get("include_links", False)
                
                pattern.update({
                    "playwright_equivalent": "page.textContent",
                    "parameters": {"goal": goal, "include_links": include_links},
                    "code_template": "const content = await page.textContent('body');"
                })
                
            elif action_type == "scroll_down":
                amount = details.get("amount")
                pattern.update({
                    "playwright_equivalent": "page.evaluate",
                    "parameters": {"amount": amount},
                    "code_template": f"await page.evaluate(() => window.scrollBy(0, {amount or 500}));"
                })
                
            elif action_type == "wait":
                seconds = details.get("seconds", 3)
                pattern.update({
                    "playwright_equivalent": "page.waitForTimeout",
                    "parameters": {"seconds": seconds},
                    "code_template": f"await page.waitForTimeout({seconds * 1000});"
                })
                
            patterns.append(pattern)
        
        return patterns
    
    async def _generate_playwright_script(self, patterns: List[Dict[str, Any]], session_id: str) -> str:
        """
        Generate a complete Playwright script from extracted patterns
        """
        script_lines = [
            "// Generated Playwright script from browser automation agent",
            f"// Session ID: {session_id}",
            f"// Generated on: {datetime.now().isoformat()}",
            "",
            "const { test, expect } = require('@playwright/test');",
            "",
            f"test('{session_id} - automated task', async ({{ page }}) => {{",
            "  // Configure page settings",
            "  await page.setViewportSize({ width: 1280, height: 720 });",
            ""
        ]
        
        for i, pattern in enumerate(patterns):
            if pattern.get("code_template"):
                script_lines.append(f"  // Step {i + 1}: {pattern['type']}")
                
                # Add dynamic parameter substitution
                code = pattern["code_template"]
                params = pattern.get("parameters", {})
                
                for key, value in params.items():
                    code = code.replace(f"{{{key}}}", str(value))
                
                script_lines.append(f"  {code}")
                script_lines.append("")
        
        script_lines.extend([
            "  // Verify task completion",
            "  // Add custom assertions here based on expected outcomes",
            "});",
            "",
            "// Export patterns for reuse",
            f"module.exports = {{",
            f"  sessionId: '{session_id}',",
            f"  patterns: {json.dumps(patterns, indent=2)},",
            f"  async executeSteps(page) {{",
        ])
        
        # Add reusable function
        for i, pattern in enumerate(patterns):
            if pattern.get("code_template"):
                code = pattern["code_template"]
                params = pattern.get("parameters", {})
                
                for key, value in params.items():
                    code = code.replace(f"{{{key}}}", str(value))
                
                script_lines.append(f"    {code}")
        
        script_lines.extend([
            "  }",
            "};"
        ])
        
        return "\n".join(script_lines)
    
    async def _save_script(self, script_path: Path, content: str):
        """Save generated script to file"""
        try:
            with open(script_path, 'w') as f:
                f.write(content)
            logger.info(f"Saved script to {script_path}")
        except Exception as e:
            logger.error(f"Failed to save script: {e}")
            raise
    
    def _extract_selectors_from_patterns(self, patterns: List[Dict[str, Any]]) -> List[str]:
        """Extract all selectors from patterns for reuse"""
        selectors = []
        for pattern in patterns:
            selectors.extend(pattern.get("selectors", []))
        return list(set(selectors))  # Remove duplicates
    
    def _estimate_tokens_saved(self, actions: List[Dict[str, Any]]) -> int:
        """
        Estimate tokens saved by using Playwright script instead of LLM calls
        """
        # Rough estimation: Each action would typically require 100-500 tokens with LLM
        # Direct Playwright execution uses 0 tokens
        base_tokens_per_action = 250
        return len(actions) * base_tokens_per_action
    
    async def optimize_for_website(self, website_url: str, actions: List[Dict[str, Any]]) -> Dict[str, Any]:
        """
        Create website-specific optimizations for common action patterns
        """
        try:
            # Extract domain from URL
            domain = self._extract_domain(website_url)
            
            # Analyze patterns specific to this website
            website_patterns = await self._analyze_website_patterns(domain, actions)
            
            # Generate optimized selectors
            optimized_selectors = await self._generate_optimized_selectors(website_patterns)
            
            # Create website-specific script
            optimized_script = await self._create_website_optimized_script(domain, website_patterns, optimized_selectors)
            
            # Save website optimization
            optimization_path = self.scripts_dir / f"optimized_{domain}.js"
            await self._save_script(optimization_path, optimized_script)
            
            return {
                "domain": domain,
                "optimization_path": str(optimization_path),
                "patterns_found": len(website_patterns),
                "selectors_optimized": len(optimized_selectors),
                "estimated_speedup": f"{len(website_patterns) * 2}x faster"
            }
            
        except Exception as e:
            logger.error(f"Failed to optimize for website: {e}")
            return {"error": str(e)}
    
    def _extract_domain(self, url: str) -> str:
        """Extract domain from URL"""
        import urllib.parse
        parsed = urllib.parse.urlparse(url)
        return parsed.netloc.replace("www.", "")
    
    async def _analyze_website_patterns(self, domain: str, actions: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """Analyze patterns specific to a website"""
        patterns = []
        
        # Group similar actions
        action_groups = {}
        for action in actions:
            action_type = action.get("action_type", "unknown")
            if action_type not in action_groups:
                action_groups[action_type] = []
            action_groups[action_type].append(action)
        
        # Find common patterns
        for action_type, action_list in action_groups.items():
            if len(action_list) > 1:  # Multiple similar actions
                pattern = {
                    "type": action_type,
                    "frequency": len(action_list),
                    "domain": domain,
                    "variations": action_list,
                    "optimizable": True
                }
                patterns.append(pattern)
        
        return patterns
    
    async def _generate_optimized_selectors(self, patterns: List[Dict[str, Any]]) -> Dict[str, str]:
        """Generate optimized selectors for common patterns"""
        optimized = {}
        
        for pattern in patterns:
            action_type = pattern["type"]
            variations = pattern.get("variations", [])
            
            # Find common selector patterns
            if action_type in ["click_element_by_index", "input_text"]:
                selectors = []
                for variation in variations:
                    xpath = variation.get("details", {}).get("xpath")
                    if xpath:
                        selectors.append(xpath)
                
                if selectors:
                    # Find most stable selector
                    common_selector = self._find_most_stable_selector(selectors)
                    optimized[action_type] = common_selector
        
        return optimized
    
    def _find_most_stable_selector(self, selectors: List[str]) -> str:
        """Find the most stable/reliable selector from a list"""
        if not selectors:
            return ""
        
        # Prefer selectors with data attributes, IDs, or classes over complex XPaths
        selector_scores = {}
        
        for selector in selectors:
            score = 0
            
            # Prefer data attributes
            if 'data-' in selector:
                score += 10
            
            # Prefer IDs
            if '#' in selector or 'id=' in selector:
                score += 8
            
            # Prefer classes
            if '.' in selector or 'class=' in selector:
                score += 6
            
            # Penalize complex XPaths
            if selector.count('/') > 3:
                score -= 5
            
            # Prefer shorter selectors
            score -= len(selector) // 20
            
            selector_scores[selector] = score
        
        # Return selector with highest score
        return max(selector_scores, key=selector_scores.get)
    
    async def _create_website_optimized_script(self, domain: str, patterns: List[Dict[str, Any]], selectors: Dict[str, str]) -> str:
        """Create optimized script for specific website"""
        script_lines = [
            f"// Optimized Playwright script for {domain}",
            f"// Generated on: {datetime.now().isoformat()}",
            "",
            "const { test, expect } = require('@playwright/test');",
            "",
            f"class {domain.replace('.', '_').replace('-', '_').title()}Helper {{",
            "",
        ]
        
        # Add optimized methods for each pattern
        for pattern in patterns:
            action_type = pattern["type"]
            method_name = f"{action_type}_optimized"
            
            if action_type == "click_element_by_index" and action_type in selectors:
                script_lines.extend([
                    f"  async {method_name}(page) {{",
                    f"    await page.click('{selectors[action_type]}');",
                    f"  }}",
                    ""
                ])
            
            elif action_type == "input_text" and action_type in selectors:
                script_lines.extend([
                    f"  async {method_name}(page, text) {{",
                    f"    await page.fill('{selectors[action_type]}', text);",
                    f"  }}",
                    ""
                ])
        
        script_lines.extend([
            "}",
            "",
            f"module.exports = {{ {domain.replace('.', '_').replace('-', '_').title()}Helper }};"
        ])
        
        return "\n".join(script_lines)
    
    async def execute_generated_script(self, script_path: str, parameters: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """
        Execute a generated Playwright script
        """
        try:
            from .script_executor import PlaywrightScriptExecutor
            
            executor = PlaywrightScriptExecutor()
            result = await executor.execute_script(script_path, parameters or {})
            
            return {
                "success": True,
                "result": result,
                "tokens_saved": result.get("estimated_tokens_saved", 0)
            }
            
        except Exception as e:
            logger.error(f"Failed to execute script: {e}")
            return {
                "success": False,
                "error": str(e)
            }
    
    async def get_script_suggestions(self, task_description: str) -> List[Dict[str, Any]]:
        """
        Get suggestions for existing scripts that could handle the task
        """
        suggestions = []
        
        # Analyze task description for keywords
        keywords = self._extract_keywords(task_description)
        
        # Check existing scripts for matches
        for session_id, metadata in self.generated_scripts.items():
            script_path = metadata.get("script_path", "")
            
            # Read script content for analysis
            try:
                with open(script_path, 'r') as f:
                    script_content = f.read()
                
                # Calculate relevance score
                relevance = self._calculate_relevance(keywords, script_content)
                
                if relevance > 0.3:  # Threshold for relevance
                    suggestions.append({
                        "session_id": session_id,
                        "script_path": script_path,
                        "relevance_score": relevance,
                        "estimated_tokens_saved": metadata.get("estimated_tokens_saved", 0),
                        "confidence": "high" if relevance > 0.7 else "medium" if relevance > 0.5 else "low"
                    })
            
            except Exception as e:
                logger.debug(f"Could not analyze script {script_path}: {e}")
        
        # Sort by relevance
        suggestions.sort(key=lambda x: x["relevance_score"], reverse=True)
        
        return suggestions[:5]  # Return top 5 suggestions
    
    def _extract_keywords(self, text: str) -> List[str]:
        """Extract relevant keywords from task description"""
        # Simple keyword extraction
        import re
        
        # Remove common stop words and extract meaningful terms
        stop_words = {'the', 'a', 'an', 'and', 'or', 'but', 'in', 'on', 'at', 'to', 'for', 'of', 'with', 'by', 'is', 'are', 'was', 'were', 'be', 'been', 'being', 'have', 'has', 'had', 'do', 'does', 'did', 'will', 'would', 'could', 'should'}
        
        words = re.findall(r'\b\w+\b', text.lower())
        keywords = [word for word in words if len(word) > 2 and word not in stop_words]
        
        return keywords
    
    def _calculate_relevance(self, keywords: List[str], script_content: str) -> float:
        """Calculate relevance score between keywords and script content"""
        if not keywords:
            return 0.0
        
        script_lower = script_content.lower()
        matches = sum(1 for keyword in keywords if keyword in script_lower)
        
        return matches / len(keywords)
    
    async def cleanup(self):
        """Cleanup resources"""
        try:
            logger.info("Playwright script generator cleanup completed")
        except Exception as e:
            logger.error(f"Error during cleanup: {e}")

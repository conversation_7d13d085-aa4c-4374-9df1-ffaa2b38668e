"""
Pattern Optimizer

Analyzes multiple browser automation sessions to identify and optimize reusable patterns.
"""

import logging
import json
from typing import Dict, List, Optional, Any, Tuple, Set
from datetime import datetime, timedelta
from collections import defaultdict, Counter
import hashlib
from pathlib import Path

logger = logging.getLogger(__name__)

class PatternOptimizer:
    """
    Analyzes browser automation patterns and creates optimized, reusable workflows
    """
    
    def __init__(self, patterns_dir: str = "data/patterns"):
        self.patterns_dir = Path(patterns_dir)
        self.patterns_dir.mkdir(parents=True, exist_ok=True)
        self.discovered_patterns = {}
        self.pattern_usage_stats = defaultdict(int)
        self.optimization_rules = self._initialize_optimization_rules()
        
    def _initialize_optimization_rules(self) -> Dict[str, Any]:
        """Initialize rules for pattern optimization"""
        return {
            "min_pattern_frequency": 3,  # Minimum occurrences to consider a pattern
            "selector_stability_threshold": 0.7,  # Minimum stability score for selectors
            "sequence_similarity_threshold": 0.8,  # Minimum similarity for sequence patterns
            "optimization_strategies": {
                "selector_generalization": True,
                "sequence_consolidation": True,
                "parameter_templating": True,
                "error_handling_injection": True
            }
        }
    
    async def analyze_sessions(self, sessions: List[Dict[str, Any]]) -> Dict[str, Any]:
        """
        Analyze multiple sessions to discover reusable patterns
        """
        try:
            logger.info(f"Analyzing {len(sessions)} sessions for patterns")
            
            # Extract action sequences
            sequences = self._extract_action_sequences(sessions)
            
            # Find common patterns
            common_patterns = self._identify_common_patterns(sequences)
            
            # Analyze selector patterns
            selector_patterns = self._analyze_selector_patterns(sessions)
            
            # Create optimized patterns
            optimized_patterns = await self._create_optimized_patterns(common_patterns, selector_patterns)
            
            # Generate reusable functions
            reusable_functions = self._generate_reusable_functions(optimized_patterns)
            
            # Save patterns
            await self._save_discovered_patterns(optimized_patterns)
            
            analysis_result = {
                "sessions_analyzed": len(sessions),
                "patterns_discovered": len(optimized_patterns),
                "reusable_functions": len(reusable_functions),
                "patterns": optimized_patterns,
                "functions": reusable_functions,
                "potential_token_savings": self._calculate_potential_savings(optimized_patterns),
                "recommendations": self._generate_recommendations(optimized_patterns)
            }
            
            return analysis_result
            
        except Exception as e:
            logger.error(f"Pattern analysis failed: {e}")
            return {"error": str(e)}
    
    def _extract_action_sequences(self, sessions: List[Dict[str, Any]]) -> List[List[Dict[str, Any]]]:
        """Extract action sequences from sessions"""
        sequences = []
        
        for session in sessions:
            actions = session.get("actions", [])
            if len(actions) >= 2:  # Only consider sequences with multiple actions
                # Create normalized sequence
                normalized_sequence = []
                for action in actions:
                    normalized_action = {
                        "type": action.get("action_type", "unknown"),
                        "details": self._normalize_action_details(action.get("details", {}))
                    }
                    normalized_sequence.append(normalized_action)
                sequences.append(normalized_sequence)
        
        return sequences
    
    def _normalize_action_details(self, details: Dict[str, Any]) -> Dict[str, Any]:
        """Normalize action details for pattern matching"""
        normalized = {}
        
        # Keep important structural information, remove specific values
        for key, value in details.items():
            if key in ["index", "xpath"]:
                normalized[key] = value
            elif key == "url":
                # Normalize URL to domain for pattern matching
                from urllib.parse import urlparse
                parsed = urlparse(str(value))
                normalized["domain"] = parsed.netloc
            elif key == "text":
                # Categorize text input types
                if isinstance(value, str):
                    if "@" in value:
                        normalized["text_type"] = "email"
                    elif value.isdigit():
                        normalized["text_type"] = "numeric"
                    elif len(value) > 50:
                        normalized["text_type"] = "long_text"
                    else:
                        normalized["text_type"] = "short_text"
        
        return normalized
    
    def _identify_common_patterns(self, sequences: List[List[Dict[str, Any]]]) -> List[Dict[str, Any]]:
        """Identify common action patterns across sequences"""
        patterns = []
        
        # Find common subsequences of different lengths
        for length in range(2, 6):  # Check subsequences of length 2-5
            pattern_counts = defaultdict(int)
            
            for sequence in sequences:
                if len(sequence) >= length:
                    # Extract all subsequences of this length
                    for i in range(len(sequence) - length + 1):
                        subsequence = sequence[i:i + length]
                        pattern_key = self._create_pattern_key(subsequence)
                        pattern_counts[pattern_key] += 1
            
            # Filter patterns that occur frequently enough
            min_frequency = self.optimization_rules["min_pattern_frequency"]
            for pattern_key, count in pattern_counts.items():
                if count >= min_frequency:
                    pattern = {
                        "pattern_id": hashlib.md5(pattern_key.encode()).hexdigest()[:8],
                        "sequence": json.loads(pattern_key),
                        "frequency": count,
                        "length": length,
                        "type": "action_sequence"
                    }
                    patterns.append(pattern)
        
        return patterns
    
    def _create_pattern_key(self, sequence: List[Dict[str, Any]]) -> str:
        """Create a hashable key for a sequence pattern"""
        # Create a simplified representation for pattern matching
        simplified = []
        for action in sequence:
            simplified.append({
                "type": action["type"],
                "normalized_details": sorted(action["details"].items())
            })
        return json.dumps(simplified, sort_keys=True)
    
    def _analyze_selector_patterns(self, sessions: List[Dict[str, Any]]) -> Dict[str, Any]:
        """Analyze selector usage patterns across sessions"""
        selector_usage = defaultdict(list)
        domain_selectors = defaultdict(set)
        
        for session in sessions:
            session_url = session.get("final_url", "")
            domain = self._extract_domain(session_url)
            
            actions = session.get("actions", [])
            for action in actions:
                details = action.get("details", {})
                xpath = details.get("xpath")
                
                if xpath:
                    selector_usage[xpath].append({
                        "session_id": session.get("session_id"),
                        "domain": domain,
                        "action_type": action.get("action_type"),
                        "success": session.get("success_indicators", {}).get("task_completed", True)
                    })
                    domain_selectors[domain].add(xpath)
        
        # Analyze selector stability
        stable_selectors = {}
        for selector, usage_list in selector_usage.items():
            if len(usage_list) >= 2:  # Used in multiple sessions
                success_rate = sum(1 for u in usage_list if u["success"]) / len(usage_list)
                domains = set(u["domain"] for u in usage_list)
                
                stability_score = success_rate * (1 + len(domains) * 0.1)  # Bonus for cross-domain usage
                
                if stability_score >= self.optimization_rules["selector_stability_threshold"]:
                    stable_selectors[selector] = {
                        "stability_score": stability_score,
                        "usage_count": len(usage_list),
                        "domains": list(domains),
                        "action_types": list(set(u["action_type"] for u in usage_list))
                    }
        
        return {
            "total_selectors": len(selector_usage),
            "stable_selectors": stable_selectors,
            "domain_specific_selectors": dict(domain_selectors),
            "cross_domain_selectors": {
                selector: data for selector, data in stable_selectors.items()
                if len(data["domains"]) > 1
            }
        }
    
    def _extract_domain(self, url: str) -> str:
        """Extract domain from URL"""
        if not url:
            return "unknown"
        
        try:
            from urllib.parse import urlparse
            parsed = urlparse(url)
            return parsed.netloc.replace("www.", "")
        except:
            return "unknown"
    
    async def _create_optimized_patterns(self, common_patterns: List[Dict[str, Any]], 
                                       selector_patterns: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Create optimized patterns from analysis"""
        optimized_patterns = []
        
        for pattern in common_patterns:
            # Optimize the pattern
            optimized = await self._optimize_single_pattern(pattern, selector_patterns)
            if optimized:
                optimized_patterns.append(optimized)
        
        # Create selector-based patterns
        stable_selectors = selector_patterns.get("stable_selectors", {})
        for selector, data in stable_selectors.items():
            if data["usage_count"] >= 3:  # Frequently used selector
                selector_pattern = {
                    "pattern_id": hashlib.md5(selector.encode()).hexdigest()[:8],
                    "type": "stable_selector",
                    "selector": selector,
                    "stability_score": data["stability_score"],
                    "usage_count": data["usage_count"],
                    "applicable_actions": data["action_types"],
                    "playwright_optimizations": self._generate_selector_optimizations(selector, data)
                }
                optimized_patterns.append(selector_pattern)
        
        return optimized_patterns
    
    async def _optimize_single_pattern(self, pattern: Dict[str, Any], 
                                     selector_patterns: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """Optimize a single action sequence pattern"""
        try:
            sequence = pattern["sequence"]
            optimizations = []
            
            # Apply optimization strategies
            if self.optimization_rules["optimization_strategies"]["selector_generalization"]:
                optimizations.extend(self._apply_selector_generalization(sequence, selector_patterns))
            
            if self.optimization_rules["optimization_strategies"]["sequence_consolidation"]:
                optimizations.extend(self._apply_sequence_consolidation(sequence))
            
            if self.optimization_rules["optimization_strategies"]["parameter_templating"]:
                optimizations.extend(self._apply_parameter_templating(sequence))
            
            # Create optimized pattern
            optimized = {
                "pattern_id": pattern["pattern_id"],
                "original_sequence": sequence,
                "frequency": pattern["frequency"],
                "type": "optimized_sequence",
                "optimizations_applied": optimizations,
                "playwright_function": self._generate_playwright_function(pattern, optimizations),
                "estimated_improvement": self._estimate_pattern_improvement(pattern, optimizations)
            }
            
            return optimized
            
        except Exception as e:
            logger.error(f"Failed to optimize pattern {pattern.get('pattern_id')}: {e}")
            return None
    
    def _apply_selector_generalization(self, sequence: List[Dict[str, Any]], 
                                     selector_patterns: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Apply selector generalization optimizations"""
        optimizations = []
        stable_selectors = selector_patterns.get("stable_selectors", {})
        
        for action in sequence:
            details = action.get("details", {})
            if "xpath" in details:
                xpath = details["xpath"]
                if xpath in stable_selectors:
                    optimizations.append({
                        "type": "selector_generalization",
                        "original_selector": xpath,
                        "stability_score": stable_selectors[xpath]["stability_score"],
                        "recommendation": "Use this stable selector with confidence"
                    })
        
        return optimizations
    
    def _apply_sequence_consolidation(self, sequence: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """Apply sequence consolidation optimizations"""
        optimizations = []
        
        # Look for consolidation opportunities
        for i in range(len(sequence) - 1):
            current = sequence[i]
            next_action = sequence[i + 1]
            
            # Consolidate wait + action patterns
            if (current["type"] == "wait" and 
                next_action["type"] in ["click_element_by_index", "input_text"]):
                optimizations.append({
                    "type": "sequence_consolidation",
                    "actions_consolidated": [current["type"], next_action["type"]],
                    "optimization": "Replace wait + action with waitForSelector + action",
                    "estimated_time_saved": "200-500ms"
                })
        
        return optimizations
    
    def _apply_parameter_templating(self, sequence: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """Apply parameter templating optimizations"""
        optimizations = []
        
        # Find actions that could benefit from parameterization
        text_inputs = [action for action in sequence if action["type"] == "input_text"]
        
        if len(text_inputs) > 1:
            optimizations.append({
                "type": "parameter_templating",
                "parameterizable_actions": len(text_inputs),
                "template_suggestion": "Create parameterized function for form filling",
                "benefit": "Reusable across different data sets"
            })
        
        return optimizations
    
    def _generate_selector_optimizations(self, selector: str, data: Dict[str, Any]) -> Dict[str, Any]:
        """Generate Playwright-specific optimizations for a selector"""
        return {
            "wait_strategy": "await page.waitForSelector('{}', {{ visible: true }})".format(selector),
            "error_handling": "await page.locator('{}').waitFor()".format(selector),
            "retry_logic": "await expect(page.locator('{}')).toBeVisible()".format(selector),
            "performance_tip": f"Selector used {data['usage_count']} times with {data['stability_score']:.1%} success rate"
        }
    
    def _generate_playwright_function(self, pattern: Dict[str, Any], 
                                    optimizations: List[Dict[str, Any]]) -> str:
        """Generate a reusable Playwright function for the pattern"""
        pattern_id = pattern["pattern_id"]
        sequence = pattern["sequence"]
        
        function_lines = [
            f"async function executePattern_{pattern_id}(page, params = {{}}) {{",
            f"  // Auto-generated function for pattern {pattern_id}",
            f"  // Used {pattern['frequency']} times in analyzed sessions",
            ""
        ]
        
        # Add optimized steps
        for i, action in enumerate(sequence):
            action_type = action["type"]
            function_lines.append(f"  // Step {i + 1}: {action_type}")
            
            # Generate appropriate Playwright code based on action type
            if action_type == "open_tab":
                function_lines.append("  await page.goto(params.url || 'about:blank');")
            elif action_type == "click_element_by_index":
                xpath = action.get("details", {}).get("xpath", ":nth-child(1)")
                function_lines.append(f"  await page.locator('{xpath}').click();")
            elif action_type == "input_text":
                xpath = action.get("details", {}).get("xpath", "input")
                function_lines.append(f"  await page.locator('{xpath}').fill(params.text || '');")
            elif action_type == "wait":
                function_lines.append("  await page.waitForTimeout(params.waitTime || 1000);")
            
            function_lines.append("")
        
        # Add optimization notes
        if optimizations:
            function_lines.extend([
                "  // Applied optimizations:",
                *[f"  // - {opt.get('type', 'unknown')}: {opt.get('optimization', opt.get('recommendation', 'N/A'))}" 
                  for opt in optimizations],
                ""
            ])
        
        function_lines.extend([
            "  return { success: true, pattern_id: '" + pattern_id + "' };",
            "}",
            "",
            f"module.exports.executePattern_{pattern_id} = executePattern_{pattern_id};"
        ])
        
        return "\n".join(function_lines)
    
    def _estimate_pattern_improvement(self, pattern: Dict[str, Any], 
                                   optimizations: List[Dict[str, Any]]) -> Dict[str, Any]:
        """Estimate improvement from pattern optimization"""
        base_improvement = {
            "token_savings": pattern["frequency"] * 200,  # Estimated tokens saved per use
            "execution_speed": "10-30% faster",
            "reliability": "High (stable pattern)",
            "maintenance": "Reduced (reusable function)"
        }
        
        # Add specific improvements based on optimizations
        for opt in optimizations:
            opt_type = opt.get("type")
            
            if opt_type == "selector_generalization":
                base_improvement["selector_reliability"] = "Improved stability"
            elif opt_type == "sequence_consolidation":
                base_improvement["execution_speed"] = "20-40% faster"
            elif opt_type == "parameter_templating":
                base_improvement["reusability"] = "High (parameterized)"
        
        return base_improvement
    
    def _generate_reusable_functions(self, patterns: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """Generate reusable function definitions from patterns"""
        functions = []
        
        for pattern in patterns:
            if pattern.get("type") == "optimized_sequence":
                function_def = {
                    "function_name": f"executePattern_{pattern['pattern_id']}",
                    "pattern_id": pattern["pattern_id"],
                    "parameters": self._extract_function_parameters(pattern),
                    "code": pattern.get("playwright_function", ""),
                    "usage_frequency": pattern["frequency"],
                    "estimated_savings": pattern.get("estimated_improvement", {}).get("token_savings", 0)
                }
                functions.append(function_def)
        
        return functions
    
    def _extract_function_parameters(self, pattern: Dict[str, Any]) -> List[Dict[str, str]]:
        """Extract parameters needed for the function"""
        parameters = []
        sequence = pattern.get("original_sequence", [])
        
        for action in sequence:
            action_type = action["type"]
            details = action.get("details", {})
            
            if action_type in ["open_tab", "go_to_url"]:
                parameters.append({"name": "url", "type": "string", "description": "URL to navigate to"})
            elif action_type == "input_text":
                if details.get("text_type") != "email":  # Don't parameterize emails
                    parameters.append({"name": "text", "type": "string", "description": "Text to input"})
            elif action_type == "wait":
                parameters.append({"name": "waitTime", "type": "number", "description": "Wait time in milliseconds"})
        
        # Remove duplicates
        seen = set()
        unique_parameters = []
        for param in parameters:
            param_key = param["name"]
            if param_key not in seen:
                seen.add(param_key)
                unique_parameters.append(param)
        
        return unique_parameters
    
    async def _save_discovered_patterns(self, patterns: List[Dict[str, Any]]):
        """Save discovered patterns to files"""
        try:
            patterns_file = self.patterns_dir / "discovered_patterns.json"
            
            with open(patterns_file, 'w') as f:
                json.dump({
                    "discovery_timestamp": datetime.now().isoformat(),
                    "patterns": patterns,
                    "total_patterns": len(patterns)
                }, f, indent=2)
            
            logger.info(f"Saved {len(patterns)} patterns to {patterns_file}")
            
        except Exception as e:
            logger.error(f"Failed to save patterns: {e}")
    
    def _calculate_potential_savings(self, patterns: List[Dict[str, Any]]) -> Dict[str, Any]:
        """Calculate potential token savings from using patterns"""
        total_savings = 0
        pattern_usage_savings = {}
        
        for pattern in patterns:
            pattern_savings = 0
            
            if pattern.get("type") == "optimized_sequence":
                frequency = pattern.get("frequency", 0)
                sequence_length = len(pattern.get("original_sequence", []))
                tokens_per_action = 200  # Estimated tokens per LLM action
                
                pattern_savings = frequency * sequence_length * tokens_per_action
                pattern_usage_savings[pattern["pattern_id"]] = pattern_savings
                total_savings += pattern_savings
        
        return {
            "total_estimated_tokens_saved": total_savings,
            "pattern_breakdown": pattern_usage_savings,
            "average_savings_per_pattern": total_savings / len(patterns) if patterns else 0,
            "projected_monthly_savings": total_savings * 30,  # Assuming daily usage
        }
    
    def _generate_recommendations(self, patterns: List[Dict[str, Any]]) -> List[str]:
        """Generate recommendations based on pattern analysis"""
        recommendations = []
        
        # Analyze pattern distribution
        sequence_patterns = [p for p in patterns if p.get("type") == "optimized_sequence"]
        selector_patterns = [p for p in patterns if p.get("type") == "stable_selector"]
        
        if len(sequence_patterns) > 5:
            recommendations.append(
                f"Consider creating a pattern library with {len(sequence_patterns)} reusable sequences"
            )
        
        if len(selector_patterns) > 10:
            recommendations.append(
                f"Implement selector optimization for {len(selector_patterns)} stable selectors"
            )
        
        # High-frequency pattern recommendations
        high_freq_patterns = [p for p in sequence_patterns if p.get("frequency", 0) > 10]
        if high_freq_patterns:
            recommendations.append(
                f"Prioritize optimization of {len(high_freq_patterns)} high-frequency patterns"
            )
        
        # Cross-domain pattern recommendations
        cross_domain_patterns = [p for p in patterns if len(p.get("domains", [])) > 1]
        if cross_domain_patterns:
            recommendations.append(
                f"Develop generic functions for {len(cross_domain_patterns)} cross-domain patterns"
            )
        
        return recommendations
    
    async def get_pattern_suggestions(self, task_description: str) -> List[Dict[str, Any]]:
        """Get pattern suggestions for a new task"""
        suggestions = []
        
        # Load existing patterns
        try:
            patterns_file = self.patterns_dir / "discovered_patterns.json"
            if patterns_file.exists():
                with open(patterns_file, 'r') as f:
                    data = json.load(f)
                    patterns = data.get("patterns", [])
                
                # Analyze task description for keywords
                task_keywords = set(task_description.lower().split())
                
                # Score patterns based on relevance
                for pattern in patterns:
                    if pattern.get("type") == "optimized_sequence":
                        relevance_score = self._calculate_task_relevance(pattern, task_keywords)
                        
                        if relevance_score > 0.3:
                            suggestions.append({
                                "pattern_id": pattern["pattern_id"],
                                "relevance_score": relevance_score,
                                "frequency": pattern.get("frequency", 0),
                                "estimated_tokens_saved": pattern.get("estimated_improvement", {}).get("token_savings", 0),
                                "function_name": f"executePattern_{pattern['pattern_id']}",
                                "confidence": "high" if relevance_score > 0.7 else "medium"
                            })
                
                # Sort by relevance
                suggestions.sort(key=lambda x: x["relevance_score"], reverse=True)
                
        except Exception as e:
            logger.error(f"Failed to get pattern suggestions: {e}")
        
        return suggestions[:5]  # Return top 5 suggestions
    
    def _calculate_task_relevance(self, pattern: Dict[str, Any], task_keywords: Set[str]) -> float:
        """Calculate relevance of a pattern to a task"""
        sequence = pattern.get("original_sequence", [])
        
        # Extract action types and details from pattern
        pattern_elements = set()
        for action in sequence:
            pattern_elements.add(action["type"])
            
            # Add domain information if available
            details = action.get("details", {})
            if "domain" in details:
                pattern_elements.add(details["domain"])
        
        # Calculate overlap
        common_elements = pattern_elements.intersection(task_keywords)
        if not pattern_elements:
            return 0.0
        
        return len(common_elements) / len(pattern_elements)

"""
Advanced Selector Optimizer

Captures and optimizes selectors that the agent discovers, building a library
of reliable selectors for future use and reducing LLM dependency.
"""

import asyncio
import json
import logging
import re
from typing import Dict, List, Optional, Any, Tuple, Set
from datetime import datetime, timedelta
from pathlib import Path
from collections import defaultdict, Counter

logger = logging.getLogger(__name__)

class SelectorOptimizer:
    """
    Advanced selector optimization and learning system
    """
    
    def __init__(self, data_dir: str = "data/selectors"):
        self.data_dir = Path(data_dir)
        self.data_dir.mkdir(parents=True, exist_ok=True)
        
        # Selector libraries
        self.domain_selectors = {}  # domain -> {action_type: [selectors]}
        self.global_selectors = {}  # action_type -> {selector: stats}
        self.selector_patterns = {}  # pattern_id -> {template, success_rate}
        
        # Learning and optimization data
        self.selector_success_rates = defaultdict(lambda: {'attempts': 0, 'successes': 0})
        self.domain_patterns = defaultdict(dict)  # domain -> patterns
        self.selector_evolution = {}  # Track how selectors change over time
        
        # Real-time optimization settings
        self.min_confidence_threshold = 0.7
        self.learning_window_days = 30
        self.pattern_min_frequency = 3
        
        # Load existing data
        self._data_loaded = False
    
    async def _ensure_data_loaded(self):
        """Ensure data is loaded (lazy loading)"""
        if not self._data_loaded:
            await self._load_existing_data()
            self._data_loaded = True

    async def capture_agent_selector_discovery(self, 
                                             domain: str,
                                             action_type: str, 
                                             selector_data: Dict[str, Any],
                                             context: Dict[str, Any]) -> Dict[str, Any]:
        """
        Capture selector when agent successfully finds and uses it
        This is the key method that learns from agent discoveries
        """
        try:
            await self._ensure_data_loaded()
            # Extract selector information
            selector_info = self._extract_selector_info(selector_data, context)
            
            # Calculate selector quality score
            quality_score = self._calculate_selector_quality(selector_info)
            
            # Store in domain-specific library
            if domain not in self.domain_selectors:
                self.domain_selectors[domain] = defaultdict(list)
            
            selector_entry = {
                "selector": selector_info["primary_selector"],
                "alternatives": selector_info["alternative_selectors"],
                "quality_score": quality_score,
                "context": {
                    "page_title": context.get("page_title", ""),
                    "page_url": context.get("page_url", ""),
                    "element_text": context.get("element_text", ""),
                    "element_attributes": context.get("element_attributes", {})
                },
                "discovered_at": datetime.now().isoformat(),
                "action_type": action_type,
                "usage_count": 1,
                "success_rate": 1.0  # Initial success
            }
            
            # Check if we already have this selector
            existing_entry = self._find_existing_selector(domain, action_type, selector_info["primary_selector"])
            
            if existing_entry:
                # Update existing entry
                existing_entry["usage_count"] += 1
                existing_entry["last_used"] = datetime.now().isoformat()
                # Update success rate will be done when we get feedback
            else:
                # Add new entry
                self.domain_selectors[domain][action_type].append(selector_entry)
            
            # Store globally for cross-domain patterns
            global_key = f"{action_type}::{selector_info['primary_selector']}"
            if global_key not in self.global_selectors:
                self.global_selectors[global_key] = {
                    "domains": set([domain]),
                    "total_usage": 1,
                    "quality_scores": [quality_score],
                    "pattern_type": self._classify_selector_pattern(selector_info["primary_selector"])
                }
            else:
                self.global_selectors[global_key]["domains"].add(domain)
                self.global_selectors[global_key]["total_usage"] += 1
                self.global_selectors[global_key]["quality_scores"].append(quality_score)
            
            # Generate optimization recommendations
            recommendations = await self._generate_optimization_recommendations(domain, action_type, selector_entry)
            
            # Save updates
            await self._save_selector_data()
            
            return {
                "selector_captured": True,
                "quality_score": quality_score,
                "recommendations": recommendations,
                "reusability_potential": self._estimate_reusability(selector_entry),
                "estimated_token_savings": self._estimate_token_savings_for_selector(selector_entry)
            }
            
        except Exception as e:
            logger.error(f"Failed to capture selector discovery: {e}")
            return {"error": str(e)}
    
    def _extract_selector_info(self, selector_data: Dict[str, Any], context: Dict[str, Any]) -> Dict[str, Any]:
        """
        Extract comprehensive selector information from agent data
        """
        selectors = []
        
        # Extract from various sources
        if "xpath" in selector_data:
            selectors.append(selector_data["xpath"])
        
        if "css_selector" in selector_data:
            selectors.append(selector_data["css_selector"])
        
        if "index" in selector_data:
            # Convert index to more stable selectors if possible
            index = selector_data["index"]
            selectors.append(f":nth-child({index + 1})")
            
            # Try to generate better selectors from context
            if context.get("element_attributes"):
                attrs = context["element_attributes"]
                if "id" in attrs:
                    selectors.append(f"#{attrs['id']}")
                if "class" in attrs:
                    selectors.append(f".{attrs['class'].replace(' ', '.')}")
                if "data-testid" in attrs:
                    selectors.append(f"[data-testid='{attrs['data-testid']}']")
        
        # Generate text-based selectors if available
        if context.get("element_text"):
            text = context["element_text"].strip()
            if len(text) < 50:  # Only for short, stable text
                selectors.append(f"text={text}")
                selectors.append(f"[aria-label*='{text}']")
        
        # Prioritize selectors by stability
        prioritized = self._prioritize_selectors(selectors)
        
        return {
            "primary_selector": prioritized[0] if prioritized else ":nth-child(1)",
            "alternative_selectors": prioritized[1:5],  # Keep top 5 alternatives
            "all_discovered": selectors
        }
    
    def _prioritize_selectors(self, selectors: List[str]) -> List[str]:
        """
        Prioritize selectors by stability and reliability
        """
        def selector_score(selector: str) -> int:
            score = 0
            
            # Highest priority: data attributes
            if 'data-testid' in selector or 'data-test' in selector:
                score += 100
            elif 'data-' in selector:
                score += 80
            
            # High priority: IDs
            elif selector.startswith('#'):
                score += 70
            
            # Medium-high priority: specific attributes
            elif '[aria-label' in selector or '[role=' in selector:
                score += 60
            
            # Medium priority: classes (if not too generic)
            elif selector.startswith('.') and selector.count('.') <= 3:
                score += 50
            
            # Medium-low priority: text selectors (if short)
            elif selector.startswith('text=') and len(selector) < 30:
                score += 40
            
            # Low priority: XPath (if simple)
            elif selector.startswith('//') and selector.count('/') <= 4:
                score += 30
            
            # Very low priority: nth-child
            elif ':nth-child' in selector:
                score += 10
            
            # Penalize overly complex selectors
            if len(selector) > 100:
                score -= 20
            if selector.count(' ') > 5:  # Too many descendant selectors
                score -= 15
            
            return score
        
        # Sort by score (highest first)
        return sorted(set(selectors), key=selector_score, reverse=True)
    
    def _calculate_selector_quality(self, selector_info: Dict[str, Any]) -> float:
        """
        Calculate quality score for a selector (0.0 to 1.0)
        """
        primary = selector_info["primary_selector"]
        alternatives = selector_info["alternative_selectors"]
        
        base_score = 0.5
        
        # Boost for stable selector types
        if 'data-testid' in primary or 'data-test' in primary:
            base_score += 0.4
        elif 'data-' in primary:
            base_score += 0.3
        elif primary.startswith('#'):
            base_score += 0.25
        elif '[aria-label' in primary:
            base_score += 0.2
        elif primary.startswith('.'):
            base_score += 0.15
        
        # Boost for having good alternatives
        if len(alternatives) >= 2:
            base_score += 0.1
        
        # Penalize fragile selectors
        if ':nth-child' in primary:
            base_score -= 0.2
        if primary.count('/') > 4:  # Complex XPath
            base_score -= 0.15
        
        return max(0.0, min(1.0, base_score))
    
    def _classify_selector_pattern(self, selector: str) -> str:
        """
        Classify selector into reusable patterns
        """
        if 'data-testid' in selector:
            return "data_testid"
        elif 'data-' in selector:
            return "data_attribute"
        elif selector.startswith('#'):
            return "id_selector"
        elif selector.startswith('.'):
            return "class_selector"
        elif '[aria-label' in selector:
            return "aria_label"
        elif selector.startswith('text='):
            return "text_selector"
        elif selector.startswith('//'):
            return "xpath_selector"
        elif ':nth-child' in selector:
            return "positional_selector"
        else:
            return "other"
    
    async def get_optimized_selector_for_task(self, 
                                            domain: str,
                                            action_type: str, 
                                            context: Dict[str, Any]) -> Dict[str, Any]:
        """
        Get the best selector for a given task based on learned patterns
        """
        try:
            await self._ensure_data_loaded()
            # Look for domain-specific selectors first
            domain_candidates = self._find_domain_candidates(domain, action_type, context)
            
            # Look for global patterns
            global_candidates = self._find_global_candidates(action_type, context)
            
            # Combine and rank candidates
            all_candidates = domain_candidates + global_candidates
            
            if not all_candidates:
                return {"no_candidates": True, "fallback_needed": True}
            
            # Rank by quality and relevance
            ranked_candidates = self._rank_candidates(all_candidates, context)
            
            best_candidate = ranked_candidates[0]
            
            return {
                "recommended_selector": best_candidate["selector"],
                "alternatives": [c["selector"] for c in ranked_candidates[1:3]],
                "confidence": best_candidate["confidence"],
                "source": best_candidate["source"],
                "expected_success_rate": best_candidate["success_rate"],
                "token_savings_estimate": best_candidate.get("token_savings", 200)
            }
            
        except Exception as e:
            logger.error(f"Failed to get optimized selector: {e}")
            return {"error": str(e), "fallback_needed": True}
    
    def _find_domain_candidates(self, domain: str, action_type: str, context: Dict[str, Any]) -> List[Dict[str, Any]]:
        """
        Find candidate selectors from domain-specific patterns
        """
        candidates = []
        
        if domain in self.domain_selectors:
            domain_data = self.domain_selectors[domain]
            if action_type in domain_data:
                for selector_entry in domain_data[action_type]:
                    # Check relevance to current context
                    relevance = self._calculate_context_relevance(selector_entry["context"], context)
                    
                    if relevance > 0.3:  # Minimum relevance threshold
                        candidates.append({
                            "selector": selector_entry["selector"],
                            "confidence": selector_entry["quality_score"] * relevance,
                            "success_rate": selector_entry["success_rate"],
                            "source": f"domain_specific_{domain}",
                            "usage_count": selector_entry["usage_count"],
                            "token_savings": 250  # Estimated savings
                        })
        
        return candidates
    
    def _find_global_candidates(self, action_type: str, context: Dict[str, Any]) -> List[Dict[str, Any]]:
        """
        Find candidate selectors from global patterns
        """
        candidates = []
        
        for global_key, global_data in self.global_selectors.items():
            key_action_type, selector = global_key.split("::", 1)
            
            if key_action_type == action_type:
                avg_quality = sum(global_data["quality_scores"]) / len(global_data["quality_scores"])
                
                candidates.append({
                    "selector": selector,
                    "confidence": avg_quality * 0.8,  # Slightly lower confidence for global patterns
                    "success_rate": 0.8,  # Conservative estimate
                    "source": "global_pattern",
                    "usage_count": global_data["total_usage"],
                    "token_savings": 200
                })
        
        return candidates
    
    def _calculate_context_relevance(self, stored_context: Dict[str, Any], current_context: Dict[str, Any]) -> float:
        """
        Calculate how relevant a stored selector is to the current context
        """
        relevance = 0.5  # Base relevance
        
        # URL similarity
        stored_url = stored_context.get("page_url", "")
        current_url = current_context.get("page_url", "")
        if stored_url and current_url:
            if stored_url == current_url:
                relevance += 0.3
            elif self._urls_similar(stored_url, current_url):
                relevance += 0.2
        
        # Page title similarity
        stored_title = stored_context.get("page_title", "")
        current_title = current_context.get("page_title", "")
        if stored_title and current_title:
            title_similarity = self._text_similarity(stored_title, current_title)
            relevance += title_similarity * 0.2
        
        # Element text similarity
        stored_text = stored_context.get("element_text", "")
        current_text = current_context.get("element_text", "")
        if stored_text and current_text:
            text_similarity = self._text_similarity(stored_text, current_text)
            relevance += text_similarity * 0.1
        
        return min(1.0, relevance)
    
    def _urls_similar(self, url1: str, url2: str) -> bool:
        """
        Check if two URLs are similar (same domain/path structure)
        """
        try:
            from urllib.parse import urlparse
            parsed1 = urlparse(url1)
            parsed2 = urlparse(url2)
            
            # Same domain
            if parsed1.netloc != parsed2.netloc:
                return False
            
            # Similar path structure
            path1_parts = parsed1.path.split('/')
            path2_parts = parsed2.path.split('/')
            
            if len(path1_parts) == len(path2_parts):
                # Check if most path segments are similar
                similar_segments = sum(1 for p1, p2 in zip(path1_parts, path2_parts) if p1 == p2)
                return similar_segments / len(path1_parts) > 0.7
            
            return False
        except:
            return False
    
    def _text_similarity(self, text1: str, text2: str) -> float:
        """
        Calculate text similarity (simple approach)
        """
        if not text1 or not text2:
            return 0.0
        
        # Simple word-based similarity
        words1 = set(text1.lower().split())
        words2 = set(text2.lower().split())
        
        if not words1 or not words2:
            return 0.0
        
        intersection = len(words1 & words2)
        union = len(words1 | words2)
        
        return intersection / union if union > 0 else 0.0
    
    def _rank_candidates(self, candidates: List[Dict[str, Any]], context: Dict[str, Any]) -> List[Dict[str, Any]]:
        """
        Rank selector candidates by overall suitability
        """
        def candidate_score(candidate: Dict[str, Any]) -> float:
            score = candidate["confidence"] * 0.4
            score += candidate["success_rate"] * 0.3
            score += min(candidate["usage_count"] / 10, 1.0) * 0.2  # Usage history
            score += (candidate["token_savings"] / 300) * 0.1  # Token savings potential
            return score
        
        return sorted(candidates, key=candidate_score, reverse=True)
    
    async def provide_selector_feedback(self, 
                                      domain: str,
                                      action_type: str,
                                      selector: str, 
                                      success: bool,
                                      error_details: Optional[str] = None) -> None:
        """
        Update selector success rates based on actual usage feedback
        """
        try:
            # Update domain-specific data
            if domain in self.domain_selectors and action_type in self.domain_selectors[domain]:
                for entry in self.domain_selectors[domain][action_type]:
                    if entry["selector"] == selector:
                        entry["usage_count"] += 1
                        
                        # Update success rate using exponential moving average
                        alpha = 0.2  # Learning rate
                        current_rate = entry["success_rate"]
                        new_rate = alpha * (1.0 if success else 0.0) + (1 - alpha) * current_rate
                        entry["success_rate"] = new_rate
                        
                        if not success and error_details:
                            if "failures" not in entry:
                                entry["failures"] = []
                            entry["failures"].append({
                                "timestamp": datetime.now().isoformat(),
                                "error": error_details
                            })
                        
                        break
            
            # Update global statistics
            global_key = f"{action_type}::{selector}"
            if global_key in self.selector_success_rates:
                stats = self.selector_success_rates[global_key]
                stats["attempts"] += 1
                if success:
                    stats["successes"] += 1
            
            # Save updates
            await self._save_selector_data()
            
        except Exception as e:
            logger.error(f"Failed to provide selector feedback: {e}")
    
    async def generate_playwright_selector_library(self, domain: str) -> Dict[str, Any]:
        """
        Generate a domain-specific Playwright helper library
        """
        try:
            if domain not in self.domain_selectors:
                return {"error": "No selectors found for domain"}
            
            domain_data = self.domain_selectors[domain]
            
            # Generate JavaScript library
            library_code = self._generate_selector_library_code(domain, domain_data)
            
            # Save library
            library_path = self.data_dir / f"{domain.replace('.', '_')}_selectors.js"
            with open(library_path, 'w') as f:
                f.write(library_code)
            
            # Generate usage statistics
            stats = self._generate_library_stats(domain_data)
            
            return {
                "library_path": str(library_path),
                "total_selectors": stats["total_selectors"],
                "high_quality_selectors": stats["high_quality_selectors"],
                "estimated_token_savings": stats["estimated_token_savings"],
                "coverage": stats["action_coverage"]
            }
            
        except Exception as e:
            logger.error(f"Failed to generate selector library: {e}")
            return {"error": str(e)}
    
    def _generate_selector_library_code(self, domain: str, domain_data: Dict[str, List[Dict[str, Any]]]) -> str:
        """
        Generate JavaScript code for domain-specific selector library
        """
        lines = [
            f"// Auto-generated selector library for {domain}",
            f"// Generated on: {datetime.now().isoformat()}",
            f"// Total learned patterns: {sum(len(selectors) for selectors in domain_data.values())}",
            "",
            f"class {domain.replace('.', '').replace('-', '').title()}Selectors {{",
            ""
        ]
        
        for action_type, selectors in domain_data.items():
            method_name = f"get{action_type.replace('_', ' ').title().replace(' ', '')}Selector"
            
            # Get best selector for this action type
            best_selector = max(selectors, key=lambda s: s["quality_score"] * s["success_rate"])
            
            lines.extend([
                f"  // Action: {action_type}",
                f"  // Success rate: {best_selector['success_rate']:.1%}",
                f"  // Quality score: {best_selector['quality_score']:.2f}",
                f"  static {method_name}() {{",
                f"    return {{",
                f"      primary: '{best_selector['selector']}',",
                f"      alternatives: {json.dumps(best_selector['alternatives'])},",
                f"      successRate: {best_selector['success_rate']}",
                f"    }};",
                f"  }}",
                ""
            ])
        
        lines.extend([
            "}",
            "",
            f"module.exports = {{ {domain.replace('.', '').replace('-', '').title()}Selectors }};"
        ])
        
        return "\n".join(lines)
    
    def _generate_library_stats(self, domain_data: Dict[str, List[Dict[str, Any]]]) -> Dict[str, Any]:
        """
        Generate statistics for the selector library
        """
        total_selectors = sum(len(selectors) for selectors in domain_data.values())
        high_quality = sum(1 for selectors in domain_data.values() 
                          for s in selectors if s["quality_score"] > 0.7)
        
        estimated_savings = total_selectors * 200  # Rough estimate
        action_coverage = len(domain_data)  # Number of different action types covered
        
        return {
            "total_selectors": total_selectors,
            "high_quality_selectors": high_quality,
            "estimated_token_savings": estimated_savings,
            "action_coverage": action_coverage
        }
    
    def _find_existing_selector(self, domain: str, action_type: str, selector: str) -> Optional[Dict[str, Any]]:
        """
        Find existing selector entry
        """
        if domain in self.domain_selectors and action_type in self.domain_selectors[domain]:
            for entry in self.domain_selectors[domain][action_type]:
                if entry["selector"] == selector:
                    return entry
        return None
    
    async def _generate_optimization_recommendations(self, 
                                                   domain: str, 
                                                   action_type: str, 
                                                   selector_entry: Dict[str, Any]) -> List[str]:
        """
        Generate recommendations for optimizing selectors
        """
        recommendations = []
        
        selector = selector_entry["selector"]
        quality = selector_entry["quality_score"]
        
        if quality < 0.5:
            recommendations.append("Consider using more stable selectors like data attributes or IDs")
        
        if ":nth-child" in selector:
            recommendations.append("Position-based selector detected - consider using semantic selectors")
        
        if selector.count("/") > 4:
            recommendations.append("Complex XPath detected - consider simplifying")
        
        if len(selector_entry["alternatives"]) < 2:
            recommendations.append("Add more alternative selectors for better reliability")
        
        return recommendations
    
    def _estimate_reusability(self, selector_entry: Dict[str, Any]) -> str:
        """
        Estimate how reusable this selector might be
        """
        quality = selector_entry["quality_score"]
        selector = selector_entry["selector"]
        
        if quality > 0.8 and ("data-" in selector or selector.startswith("#")):
            return "high"
        elif quality > 0.6:
            return "medium"
        else:
            return "low"
    
    def _estimate_token_savings_for_selector(self, selector_entry: Dict[str, Any]) -> int:
        """
        Estimate token savings for using this selector vs LLM discovery
        """
        base_savings = 200  # Base savings for not using LLM
        
        # Higher quality selectors save more tokens (more reliable)
        quality_multiplier = selector_entry["quality_score"]
        
        return int(base_savings * quality_multiplier)
    
    async def _load_existing_data(self):
        """
        Load existing selector data from files
        """
        try:
            domain_file = self.data_dir / "domain_selectors.json"
            if domain_file.exists():
                with open(domain_file, 'r') as f:
                    data = json.load(f)
                    # Convert sets back from lists
                    for domain, actions in data.items():
                        self.domain_selectors[domain] = defaultdict(list)
                        for action, selectors in actions.items():
                            self.domain_selectors[domain][action] = selectors
            
            global_file = self.data_dir / "global_selectors.json"
            if global_file.exists():
                with open(global_file, 'r') as f:
                    data = json.load(f)
                    for key, value in data.items():
                        value["domains"] = set(value["domains"])  # Convert back to set
                        self.global_selectors[key] = value
            
            logger.info("Loaded existing selector data")
            
        except Exception as e:
            logger.debug(f"Could not load existing selector data: {e}")
    
    async def _save_selector_data(self):
        """
        Save selector data to files
        """
        try:
            # Save domain selectors
            domain_file = self.data_dir / "domain_selectors.json"
            domain_data = {}
            for domain, actions in self.domain_selectors.items():
                domain_data[domain] = dict(actions)  # Convert defaultdict to dict
            
            with open(domain_file, 'w') as f:
                json.dump(domain_data, f, indent=2, default=str)
            
            # Save global selectors
            global_file = self.data_dir / "global_selectors.json"
            global_data = {}
            for key, value in self.global_selectors.items():
                value_copy = value.copy()
                value_copy["domains"] = list(value["domains"])  # Convert set to list for JSON
                global_data[key] = value_copy
            
            with open(global_file, 'w') as f:
                json.dump(global_data, f, indent=2, default=str)
            
            logger.debug("Saved selector data")
            
        except Exception as e:
            logger.error(f"Failed to save selector data: {e}")
    
    async def get_performance_stats(self) -> Dict[str, Any]:
        """
        Get performance statistics for the selector optimization system
        """
        total_domains = len(self.domain_selectors) 
        total_selectors = sum(len(actions) for domain in self.domain_selectors.values() 
                             for actions in domain.values())
        
        high_quality_count = sum(1 for domain in self.domain_selectors.values()
                                for actions in domain.values()
                                for selector in actions
                                if selector["quality_score"] > 0.7)
        
        avg_success_rate = 0.0
        if total_selectors > 0:
            success_rates = [selector["success_rate"] 
                           for domain in self.domain_selectors.values()
                           for actions in domain.values()
                           for selector in actions]
            avg_success_rate = sum(success_rates) / len(success_rates)
        
        estimated_total_savings = total_selectors * 225  # Average savings per selector
        
        return {
            "total_domains_learned": total_domains,
            "total_selectors_captured": total_selectors,
            "high_quality_selectors": high_quality_count,
            "average_success_rate": avg_success_rate,
            "estimated_total_token_savings": estimated_total_savings,
            "learning_efficiency": high_quality_count / max(1, total_selectors),
            "global_patterns": len(self.global_selectors)
        }
    
    async def cleanup(self):
        """
        Cleanup and save any pending data
        """
        try:
            await self._ensure_data_loaded()
            await self._save_selector_data()
            logger.info("Selector optimizer cleanup completed")
        except Exception as e:
            logger.error(f"Error during selector optimizer cleanup: {e}")

# Global instance
selector_optimizer = SelectorOptimizer()

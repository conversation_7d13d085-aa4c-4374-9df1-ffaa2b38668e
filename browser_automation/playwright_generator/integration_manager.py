"""
Playwright Integration Manager

Coordinates between browser automation agent and Playwright script generation,
providing intelligent decision-making on when to use LLM vs generated scripts.
"""

import asyncio
import logging
import json
from typing import Dict, List, Optional, Any, Tuple
from datetime import datetime, timedelta
from pathlib import Path

from .script_generator import PlaywrightScriptGenerator
from .action_converter import ActionConverter
from .script_executor import PlaywrightScriptExecutor
from .pattern_optimizer import PatternOptimizer

logger = logging.getLogger(__name__)

class PlaywrightIntegrationManager:
    """
    Main coordinator for Playwright script generation and execution
    """
    
    def __init__(self, config: Dict[str, Any] = None):
        self.config = config or {}
        
        # Initialize components
        self.script_generator = PlaywrightScriptGenerator(
            scripts_dir=self.config.get("scripts_dir", "data/playwright_scripts")
        )
        self.action_converter = ActionConverter()
        self.script_executor = PlaywrightScriptExecutor()
        self.pattern_optimizer = PatternOptimizer(
            patterns_dir=self.config.get("patterns_dir", "data/patterns")
        )
        
        # Decision engine settings
        self.decision_thresholds = {
            "min_pattern_confidence": 0.7,
            "min_tokens_saved": 200,
            "max_execution_time_diff": 5.0,  # seconds
            "pattern_usage_frequency": 3
        }
        
        # Statistics tracking
        self.execution_stats = {
            "llm_executions": 0,
            "playwright_executions": 0,
            "tokens_saved": 0,
            "execution_time_saved": 0.0,
            "conversion_success_rate": 0.0
        }
        
        self.initialized = False
    
    async def initialize(self) -> bool:
        """Initialize the integration manager"""
        try:
            logger.info("Initializing Playwright Integration Manager...")
            
            # Load existing patterns
            await self._load_existing_patterns()
            
            # Initialize components
            # No specific initialization needed for current components
            
            self.initialized = True
            logger.info("Playwright Integration Manager initialized successfully")
            return True
            
        except Exception as e:
            logger.error(f"Failed to initialize integration manager: {e}")
            return False
    
    async def decide_execution_method(self, task_description: str, 
                                   context: Dict[str, Any] = None) -> Dict[str, Any]:
        """
        Intelligently decide whether to use LLM or generated Playwright script
        """
        try:
            if not self.initialized:
                await self.initialize()
            
            # Get pattern suggestions
            pattern_suggestions = await self.pattern_optimizer.get_pattern_suggestions(task_description)
            
            # Get script suggestions  
            script_suggestions = await self.script_generator.get_script_suggestions(task_description)
            
            # Analyze task complexity
            task_complexity = self._analyze_task_complexity(task_description)
            
            # Make decision based on available options
            decision = await self._make_execution_decision(
                task_description, 
                pattern_suggestions, 
                script_suggestions, 
                task_complexity,
                context or {}
            )
            
            return decision
            
        except Exception as e:
            logger.error(f"Decision making failed: {e}")
            return {
                "method": "llm",
                "reason": f"Decision engine error: {e}",
                "confidence": 0.0
            }
    
    async def execute_task_optimally(self, task_description: str, 
                                   context: Dict[str, Any] = None) -> Dict[str, Any]:
        """
        Execute task using the optimal method (LLM vs Playwright)
        """
        start_time = datetime.now()
        
        try:
            # Decide execution method
            decision = await self.decide_execution_method(task_description, context)
            execution_method = decision["method"]
            
            logger.info(f"Executing task using {execution_method}: {decision.get('reason', 'N/A')}")
            
            if execution_method == "playwright":
                # Execute using generated Playwright script
                result = await self._execute_with_playwright(decision, task_description, context or {})
                
                # Update statistics
                self.execution_stats["playwright_executions"] += 1
                self.execution_stats["tokens_saved"] += decision.get("estimated_tokens_saved", 0)
                
            else:
                # Execute using LLM (fallback to original browser agent)
                result = await self._execute_with_llm(task_description, context or {})
                
                # Analyze result for future pattern extraction
                if result.get("success"):
                    await self._analyze_for_future_optimization(result, task_description)
                
                # Update statistics
                self.execution_stats["llm_executions"] += 1
            
            # Calculate execution time
            execution_time = (datetime.now() - start_time).total_seconds()
            result["execution_method"] = execution_method
            result["decision_info"] = decision
            result["total_execution_time"] = execution_time
            
            # Update performance metrics
            await self._update_performance_metrics(execution_method, result, execution_time)
            
            return result
            
        except Exception as e:
            execution_time = (datetime.now() - start_time).total_seconds()
            logger.error(f"Optimal execution failed: {e}")
            
            return {
                "success": False,
                "error": str(e),
                "execution_method": "failed",
                "total_execution_time": execution_time
            }
    
    async def _make_execution_decision(self, task_description: str,
                                     pattern_suggestions: List[Dict[str, Any]],
                                     script_suggestions: List[Dict[str, Any]],
                                     task_complexity: Dict[str, Any],
                                     context: Dict[str, Any]) -> Dict[str, Any]:
        """Make intelligent decision on execution method"""
        
        # Check if we have high-confidence patterns or scripts
        best_pattern = pattern_suggestions[0] if pattern_suggestions else None
        best_script = script_suggestions[0] if script_suggestions else None
        
        # Decision factors
        decision_factors = {
            "has_high_confidence_pattern": False,
            "has_suitable_script": False,
            "task_is_simple": task_complexity.get("complexity_score", 1.0) < 0.5,
            "tokens_savings_significant": False,
            "execution_time_benefit": False
        }
        
        # Evaluate best pattern
        if best_pattern and best_pattern.get("confidence") == "high":
            confidence_score = best_pattern.get("relevance_score", 0)
            if confidence_score >= self.decision_thresholds["min_pattern_confidence"]:
                decision_factors["has_high_confidence_pattern"] = True
                
                estimated_savings = best_pattern.get("estimated_tokens_saved", 0)
                if estimated_savings >= self.decision_thresholds["min_tokens_saved"]:
                    decision_factors["tokens_savings_significant"] = True
        
        # Evaluate best script
        if best_script and best_script.get("confidence") in ["high", "medium"]:
            script_relevance = best_script.get("relevance_score", 0)
            if script_relevance >= 0.6:
                decision_factors["has_suitable_script"] = True
                
                estimated_savings = best_script.get("estimated_tokens_saved", 0)
                if estimated_savings >= self.decision_thresholds["min_tokens_saved"]:
                    decision_factors["tokens_savings_significant"] = True
        
        # Make decision based on factors
        if (decision_factors["has_high_confidence_pattern"] or 
            decision_factors["has_suitable_script"]) and \
           decision_factors["tokens_savings_significant"]:
            
            # Use Playwright
            chosen_option = best_pattern if decision_factors["has_high_confidence_pattern"] else best_script
            option_type = "pattern" if best_pattern and decision_factors["has_high_confidence_pattern"] else "script"
            
            return {
                "method": "playwright",
                "option_type": option_type,
                "chosen_option": chosen_option,
                "confidence": chosen_option.get("relevance_score", 0.5),
                "reason": f"High-confidence {option_type} available with significant token savings",
                "estimated_tokens_saved": chosen_option.get("estimated_tokens_saved", 0),
                "decision_factors": decision_factors
            }
        
        elif decision_factors["task_is_simple"] and (best_pattern or best_script):
            # Use Playwright for simple tasks even with medium confidence
            chosen_option = best_pattern or best_script
            option_type = "pattern" if best_pattern else "script"
            
            return {
                "method": "playwright",
                "option_type": option_type,
                "chosen_option": chosen_option,
                "confidence": 0.6,
                "reason": f"Simple task with available {option_type}",
                "estimated_tokens_saved": chosen_option.get("estimated_tokens_saved", 0),
                "decision_factors": decision_factors
            }
        
        else:
            # Use LLM
            return {
                "method": "llm",
                "reason": "No suitable patterns/scripts found or insufficient confidence",
                "confidence": 0.0,
                "estimated_tokens_saved": 0,
                "decision_factors": decision_factors,
                "available_patterns": len(pattern_suggestions),
                "available_scripts": len(script_suggestions)
            }
    
    def _analyze_task_complexity(self, task_description: str) -> Dict[str, Any]:
        """Analyze task complexity to aid decision making"""
        
        # Simple heuristics for task complexity
        task_lower = task_description.lower()
        
        complexity_indicators = {
            "multiple_steps": len([word for word in ["then", "and", "after", "next"] if word in task_lower]),
            "complex_actions": len([word for word in ["form", "login", "search", "navigate"] if word in task_lower]),
            "conditional_logic": len([word for word in ["if", "when", "unless", "check"] if word in task_lower]),
            "data_extraction": len([word for word in ["extract", "get", "find", "collect"] if word in task_lower]),
            "task_length": len(task_description.split())
        }
        
        # Calculate complexity score (0-1, where 1 is most complex)
        complexity_score = min(1.0, (
            complexity_indicators["multiple_steps"] * 0.2 +
            complexity_indicators["complex_actions"] * 0.3 +
            complexity_indicators["conditional_logic"] * 0.3 +
            complexity_indicators["data_extraction"] * 0.1 +
            min(complexity_indicators["task_length"] / 50, 0.1)
        ))
        
        return {
            "complexity_score": complexity_score,
            "indicators": complexity_indicators,
            "classification": "simple" if complexity_score < 0.3 else "medium" if complexity_score < 0.7 else "complex"
        }
    
    async def _execute_with_playwright(self, decision: Dict[str, Any], 
                                     task_description: str, context: Dict[str, Any]) -> Dict[str, Any]:
        """Execute task using Playwright script/pattern"""
        try:
            chosen_option = decision["chosen_option"]
            option_type = decision["option_type"]
            
            if option_type == "pattern":
                # Execute using pattern
                pattern_id = chosen_option["pattern_id"]
                function_name = chosen_option.get("function_name", f"executePattern_{pattern_id}")
                
                # Create parameters from context
                parameters = self._extract_parameters_from_context(context, task_description)
                
                # Execute pattern function (this would need to be implemented based on stored patterns)
                result = await self._execute_pattern_function(function_name, parameters)
                
            else:  # script
                # Execute using existing script
                script_path = chosen_option["script_path"]
                parameters = self._extract_parameters_from_context(context, task_description)
                
                result = await self.script_executor.execute_with_retry(script_path, parameters)
            
            # Enhance result with decision info
            result["execution_method"] = "playwright"
            result["option_type"] = option_type
            result["chosen_option_id"] = chosen_option.get("pattern_id") or chosen_option.get("session_id")
            
            return result
            
        except Exception as e:
            logger.error(f"Playwright execution failed: {e}")
            
            # Fallback to LLM execution
            logger.info("Falling back to LLM execution due to Playwright failure")
            return await self._execute_with_llm(task_description, context)
    
    async def _execute_with_llm(self, task_description: str, context: Dict[str, Any]) -> Dict[str, Any]:
        """Execute task using original LLM-based browser agent"""
        try:
            # Import here to avoid circular imports
            from ..automation_agent import browser_agent
            
            # Execute using the original browser automation agent
            result = await browser_agent.execute_task(
                task_description=task_description,
                context=context,
                record_session=True,  # Important for learning
                max_steps=context.get("max_steps", 10)
            )
            
            result["execution_method"] = "llm"
            return result
            
        except Exception as e:
            logger.error(f"LLM execution failed: {e}")
            return {
                "success": False,
                "error": str(e),
                "execution_method": "llm"
            }
    
    async def _execute_pattern_function(self, function_name: str, parameters: Dict[str, Any]) -> Dict[str, Any]:
        """Execute a stored pattern function"""
        try:
            # This is a placeholder - in a real implementation, you would:
            # 1. Load the pattern function from storage
            # 2. Execute it with the browser context
            # 3. Return the results
            
            # For now, simulate execution
            await asyncio.sleep(1)  # Simulate execution time
            
            return {
                "success": True,
                "result": f"Pattern {function_name} executed successfully",
                "parameters_used": parameters,
                "execution_time": 1.0,
                "tokens_saved": 250
            }
            
        except Exception as e:
            logger.error(f"Pattern function execution failed: {e}")
            return {
                "success": False,
                "error": str(e)
            }
    
    def _extract_parameters_from_context(self, context: Dict[str, Any], 
                                       task_description: str) -> Dict[str, Any]:
        """Extract parameters needed for script/pattern execution"""
        parameters = {}
        
        # Extract common parameters
        if "url" in task_description.lower():
            # Try to extract URL from task description
            import re
            url_pattern = r'https?://[^\s]+'
            urls = re.findall(url_pattern, task_description)
            if urls:
                parameters["url"] = urls[0]
        
        # Extract from context
        parameters.update({
            "viewport_width": context.get("viewport_width", 1280),
            "viewport_height": context.get("viewport_height", 720),
            "wait_timeout": context.get("wait_timeout", 30000),
            "user_agent": context.get("user_agent", "")
        })
        
        return parameters
    
    async def _analyze_for_future_optimization(self, result: Dict[str, Any], task_description: str):
        """Analyze successful LLM execution for future optimization"""
        try:
            # Check if this task generated a recording
            recording_path = result.get("recording_path")
            if recording_path and Path(recording_path).exists():
                
                # Load the recording
                with open(recording_path, 'r') as f:
                    session_data = json.load(f)
                
                # Analyze for patterns
                metadata = await self.script_generator.analyze_agent_session(session_data)
                
                if metadata and not metadata.get("error"):
                    logger.info(f"Generated script for task: {task_description}")
                    
                    # Check if this creates a new reusable pattern
                    actions = session_data.get("actions", [])
                    if len(actions) >= 3:  # Multi-step task worth optimizing
                        # Add to pattern optimizer for future analysis
                        await self.pattern_optimizer.analyze_sessions([session_data])
            
        except Exception as e:
            logger.debug(f"Future optimization analysis failed: {e}")
    
    async def _update_performance_metrics(self, execution_method: str, 
                                        result: Dict[str, Any], execution_time: float):
        """Update performance tracking metrics"""
        try:
            # Update basic counters (already done in execute_task_optimally)
            
            # Update success rates
            if execution_method == "playwright":
                # Track Playwright success rate
                if result.get("success"):
                    # Successful Playwright execution
                    pass
            
            # Update execution time savings
            if execution_method == "playwright" and result.get("success"):
                # Estimate time saved compared to LLM execution
                estimated_llm_time = execution_time * 2  # Rough estimate
                time_saved = max(0, estimated_llm_time - execution_time)
                self.execution_stats["execution_time_saved"] += time_saved
            
            # Calculate conversion success rate
            total_executions = (self.execution_stats["llm_executions"] + 
                              self.execution_stats["playwright_executions"])
            
            if total_executions > 0:
                self.execution_stats["conversion_success_rate"] = (
                    self.execution_stats["playwright_executions"] / total_executions
                )
            
        except Exception as e:
            logger.debug(f"Performance metrics update failed: {e}")
    
    async def _load_existing_patterns(self):
        """Load existing patterns and scripts on startup"""
        try:
            # This would load saved patterns and scripts from previous sessions
            patterns_dir = Path(self.config.get("patterns_dir", "data/patterns"))
            if patterns_dir.exists():
                patterns_file = patterns_dir / "discovered_patterns.json"
                if patterns_file.exists():
                    logger.info("Loaded existing patterns for optimization")
            
            scripts_dir = Path(self.config.get("scripts_dir", "data/playwright_scripts"))
            if scripts_dir.exists():
                script_files = list(scripts_dir.glob("*.js"))
                if script_files:
                    logger.info(f"Found {len(script_files)} existing Playwright scripts")
            
        except Exception as e:
            logger.debug(f"Failed to load existing patterns: {e}")
    
    async def generate_script_from_session(self, session_data: Dict[str, Any]) -> Dict[str, Any]:
        """Generate Playwright script from a successful session"""
        try:
            return await self.script_generator.analyze_agent_session(session_data)
        except Exception as e:
            logger.error(f"Script generation failed: {e}")
            return {"error": str(e)}
    
    async def batch_optimize_sessions(self, sessions: List[Dict[str, Any]]) -> Dict[str, Any]:
        """Batch optimize multiple sessions to discover patterns"""
        try:
            return await self.pattern_optimizer.analyze_sessions(sessions)
        except Exception as e:
            logger.error(f"Batch optimization failed: {e}")
            return {"error": str(e)}
    
    def get_performance_statistics(self) -> Dict[str, Any]:
        """Get current performance statistics"""
        total_executions = (self.execution_stats["llm_executions"] + 
                           self.execution_stats["playwright_executions"])
        
        return {
            "total_executions": total_executions,
            "llm_executions": self.execution_stats["llm_executions"],
            "playwright_executions": self.execution_stats["playwright_executions"],
            "playwright_usage_rate": (
                self.execution_stats["playwright_executions"] / total_executions 
                if total_executions > 0 else 0
            ),
            "total_tokens_saved": self.execution_stats["tokens_saved"],
            "total_time_saved_seconds": self.execution_stats["execution_time_saved"],
            "conversion_success_rate": self.execution_stats["conversion_success_rate"],
            "average_tokens_saved_per_execution": (
                self.execution_stats["tokens_saved"] / 
                max(1, self.execution_stats["playwright_executions"])
            )
        }
    
    async def cleanup(self):
        """Cleanup resources"""
        try:
            await self.script_executor.cleanup()
            await self.pattern_optimizer.cleanup() if hasattr(self.pattern_optimizer, 'cleanup') else None
            await self.script_generator.cleanup()
            logger.info("Playwright integration manager cleanup completed")
        except Exception as e:
            logger.error(f"Error during cleanup: {e}")

# Global instance
integration_manager = PlaywrightIntegrationManager()

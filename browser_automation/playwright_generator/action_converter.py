"""
Action Converter

Converts browser-use agent actions to equivalent Playwright code with intelligent selector optimization.
"""

import logging
import re
from typing import Dict, List, Optional, Any, Tuple
from urllib.parse import urlparse
import json

logger = logging.getLogger(__name__)

class ActionConverter:
    """
    Converts browser automation agent actions to Playwright equivalents
    """
    
    def __init__(self):
        self.selector_cache = {}
        self.conversion_rules = self._initialize_conversion_rules()
        
    def _initialize_conversion_rules(self) -> Dict[str, Dict[str, Any]]:
        """Initialize conversion rules for different action types"""
        return {
            "open_tab": {
                "playwright_method": "page.goto",
                "parameters": ["url"],
                "template": "await page.goto('{url}');",
                "async": True
            },
            "go_to_url": {
                "playwright_method": "page.goto", 
                "parameters": ["url"],
                "template": "await page.goto('{url}');",
                "async": True
            },
            "click_element_by_index": {
                "playwright_method": "page.click",
                "parameters": ["selector"],
                "template": "await page.click('{selector}');",
                "async": True,
                "selector_required": True
            },
            "input_text": {
                "playwright_method": "page.fill",
                "parameters": ["selector", "text"],
                "template": "await page.fill('{selector}', '{text}');",
                "async": True,
                "selector_required": True
            },
            "extract_content": {
                "playwright_method": "page.textContent",
                "parameters": ["selector"],
                "template": "const content = await page.textContent('{selector}');",
                "async": True,
                "default_selector": "body"
            },
            "scroll_down": {
                "playwright_method": "page.evaluate",
                "parameters": ["amount"],
                "template": "await page.evaluate(() => window.scrollBy(0, {amount}));",
                "async": True,
                "default_amount": 500
            },
            "scroll_up": {
                "playwright_method": "page.evaluate", 
                "parameters": ["amount"],
                "template": "await page.evaluate(() => window.scrollBy(0, -{amount}));",
                "async": True,
                "default_amount": 500
            },
            "wait": {
                "playwright_method": "page.waitForTimeout",
                "parameters": ["duration"],
                "template": "await page.waitForTimeout({duration});",
                "async": True,
                "duration_multiplier": 1000  # Convert seconds to milliseconds
            },
            "switch_tab": {
                "playwright_method": "page.bringToFront",
                "parameters": [],
                "template": "await page.bringToFront();",
                "async": True
            },
            "close_tab": {
                "playwright_method": "page.close",
                "parameters": [],
                "template": "await page.close();",
                "async": True
            },
            "get_ax_tree": {
                "playwright_method": "page.accessibility.snapshot",
                "parameters": [],
                "template": "const axTree = await page.accessibility.snapshot();",
                "async": True
            },
            "save_pdf": {
                "playwright_method": "page.pdf",
                "parameters": ["path"],
                "template": "await page.pdf({{ path: '{path}' }});",
                "async": True,
                "default_path": "output.pdf"
            },
            "send_keys": {
                "playwright_method": "page.keyboard.type",
                "parameters": ["text"],
                "template": "await page.keyboard.type('{text}');",
                "async": True
            },
            "scroll_to_text": {
                "playwright_method": "page.getByText",
                "parameters": ["text"],
                "template": "await page.getByText('{text}').scrollIntoViewIfNeeded();",
                "async": True
            },
            "select_dropdown_option": {
                "playwright_method": "page.selectOption",
                "parameters": ["selector", "value"],
                "template": "await page.selectOption('{selector}', '{value}');",
                "async": True,
                "selector_required": True
            },
            "drag_drop": {
                "playwright_method": "page.dragAndDrop",
                "parameters": ["source_selector", "target_selector"],
                "template": "await page.dragAndDrop('{source_selector}', '{target_selector}');",
                "async": True
            }
        }
    
    def convert_action(self, action: Dict[str, Any]) -> Dict[str, Any]:
        """
        Convert a single browser-use action to Playwright equivalent
        """
        try:
            action_type = action.get("action_type", "unknown")
            details = action.get("details", {})
            
            if action_type not in self.conversion_rules:
                logger.warning(f"Unknown action type: {action_type}")
                return self._create_fallback_conversion(action)
            
            rule = self.conversion_rules[action_type]
            
            # Extract and optimize parameters
            params = self._extract_parameters(action_type, details, rule)
            
            # Generate Playwright code
            code = self._generate_playwright_code(rule, params)
            
            # Optimize selectors if needed
            if rule.get("selector_required") and "selector" in params:
                optimized_selector = self._optimize_selector(params["selector"], action_type)
                code = code.replace(params["selector"], optimized_selector)
                params["selector"] = optimized_selector
            
            return {
                "original_action": action_type,
                "playwright_method": rule["playwright_method"],
                "playwright_code": code,
                "parameters": params,
                "estimated_reliability": self._estimate_reliability(action_type, params),
                "optimization_applied": rule.get("selector_required", False)
            }
            
        except Exception as e:
            logger.error(f"Failed to convert action {action_type}: {e}")
            return self._create_error_conversion(action, str(e))
    
    def _extract_parameters(self, action_type: str, details: Dict[str, Any], rule: Dict[str, Any]) -> Dict[str, Any]:
        """Extract and process parameters for the action"""
        params = {}
        
        # Handle specific action types
        if action_type in ["open_tab", "go_to_url"]:
            params["url"] = details.get("url", "")
            
        elif action_type == "click_element_by_index":
            params["selector"] = self._convert_index_to_selector(details)
            
        elif action_type == "input_text":
            params["selector"] = self._convert_index_to_selector(details)
            params["text"] = details.get("text", "")
            
        elif action_type == "extract_content":
            goal = details.get("goal", "")
            selector = self._infer_selector_from_goal(goal)
            params["selector"] = selector or rule.get("default_selector", "body")
            
        elif action_type in ["scroll_down", "scroll_up"]:
            amount = details.get("amount", rule.get("default_amount", 500))
            params["amount"] = amount
            
        elif action_type == "wait":
            seconds = details.get("seconds", 3)
            params["duration"] = seconds * rule.get("duration_multiplier", 1000)
            
        elif action_type == "save_pdf":
            params["path"] = details.get("path", rule.get("default_path", "output.pdf"))
            
        elif action_type == "send_keys":
            params["text"] = details.get("keys", "")
            
        elif action_type == "scroll_to_text":
            params["text"] = details.get("text", "")
            
        elif action_type == "select_dropdown_option":
            params["selector"] = self._convert_index_to_selector(details)
            params["value"] = details.get("text", "")
            
        elif action_type == "drag_drop":
            params["source_selector"] = details.get("element_source", "")
            params["target_selector"] = details.get("element_target", "")
        
        return params
    
    def _convert_index_to_selector(self, details: Dict[str, Any]) -> str:
        """Convert element index to a more reliable selector"""
        xpath = details.get("xpath")
        if xpath:
            return self._xpath_to_css_selector(xpath)
        
        index = details.get("index", 0)
        # Convert index to nth-child selector (basic approach)
        return f":nth-child({index + 1})"
    
    def _xpath_to_css_selector(self, xpath: str) -> str:
        """Convert XPath to CSS selector where possible"""
        if not xpath:
            return ""
        
        # Simple conversions for common XPath patterns
        css_selector = xpath
        
        # Remove leading //
        if css_selector.startswith("//"):
            css_selector = css_selector[2:]
        
        # Convert simple element paths
        css_selector = re.sub(r'/([a-zA-Z]+)', r' \\1', css_selector)
        
        # Convert [@class='value'] to .value
        css_selector = re.sub(r"\[@class='([^']+)'\]", r'.\\1', css_selector)
        
        # Convert [@id='value'] to #value
        css_selector = re.sub(r"\[@id='([^']+)'\]", r'#\\1', css_selector)
        
        # Convert [text()='value'] to text content selector (approximate)
        css_selector = re.sub(r"\[text\(\)='([^']+)'\]", r':has-text("\\1")', css_selector)
        
        # Clean up and return
        css_selector = css_selector.strip()
        
        # If conversion seems invalid, return original XPath as is
        # Playwright supports XPath selectors
        if not css_selector or css_selector == xpath:
            return xpath
        
        return css_selector
    
    def _infer_selector_from_goal(self, goal: str) -> Optional[str]:
        """Infer appropriate selector from extraction goal"""
        goal_lower = goal.lower()
        
        # Common patterns
        if "title" in goal_lower:
            return "h1, h2, h3, title"
        elif "content" in goal_lower and "main" in goal_lower:
            return "main, .content, #content"
        elif "link" in goal_lower:
            return "a"
        elif "text" in goal_lower:
            return "body"
        elif "form" in goal_lower:
            return "form"
        elif "button" in goal_lower:
            return "button"
        
        return None
    
    def _optimize_selector(self, selector: str, action_type: str) -> str:
        """Optimize selector for better reliability"""
        if not selector:
            return selector
        
        # Cache check
        cache_key = f"{selector}_{action_type}"
        if cache_key in self.selector_cache:
            return self.selector_cache[cache_key]
        
        optimized = selector
        
        # Prefer data attributes over generic selectors
        if selector.startswith(":nth-child"):
            # Try to suggest more stable alternatives
            optimized = selector  # Keep as is for now, could be enhanced with DOM analysis
        
        # Prefer shorter, more stable XPath expressions
        elif selector.count("/") > 5:
            # Simplify overly complex XPath
            parts = selector.split("/")
            if len(parts) > 3:
                # Take the last few meaningful parts
                meaningful_parts = [part for part in parts[-3:] if part and not part.isdigit()]
                if meaningful_parts:
                    optimized = "//" + "/".join(meaningful_parts)
        
        # Store in cache
        self.selector_cache[cache_key] = optimized
        
        return optimized
    
    def _generate_playwright_code(self, rule: Dict[str, Any], params: Dict[str, Any]) -> str:
        """Generate Playwright code from rule and parameters"""
        template = rule["template"]
        
        # Replace placeholders with actual values
        code = template
        for key, value in params.items():
            placeholder = f"{{{key}}}"
            if placeholder in code:
                # Escape single quotes in values
                if isinstance(value, str):
                    value = value.replace("'", "\\'")
                code = code.replace(placeholder, str(value))
        
        return code
    
    def _estimate_reliability(self, action_type: str, params: Dict[str, Any]) -> float:
        """Estimate reliability score for the converted action"""
        base_score = 0.8
        
        # Adjust based on selector quality
        if "selector" in params:
            selector = params["selector"]
            
            # Data attributes are most reliable
            if "data-" in selector:
                base_score += 0.15
            # IDs are very reliable
            elif "#" in selector or "id=" in selector:
                base_score += 0.1
            # Classes are moderately reliable
            elif "." in selector or "class=" in selector:
                base_score += 0.05
            # Complex XPath or nth-child selectors are less reliable
            elif selector.count("/") > 3 or ":nth-child" in selector:
                base_score -= 0.1
        
        # Adjust based on action type
        reliable_actions = ["open_tab", "go_to_url", "wait", "scroll_down", "scroll_up"]
        if action_type in reliable_actions:
            base_score += 0.05
        
        # Clamp between 0 and 1
        return max(0.0, min(1.0, base_score))
    
    def _create_fallback_conversion(self, action: Dict[str, Any]) -> Dict[str, Any]:
        """Create fallback conversion for unknown actions"""
        action_type = action.get("action_type", "unknown")
        
        return {
            "original_action": action_type,
            "playwright_method": "page.evaluate",
            "playwright_code": f"// TODO: Implement conversion for {action_type}",
            "parameters": action.get("details", {}),
            "estimated_reliability": 0.3,
            "optimization_applied": False,
            "notes": f"Fallback conversion for unsupported action: {action_type}"
        }
    
    def _create_error_conversion(self, action: Dict[str, Any], error: str) -> Dict[str, Any]:
        """Create error conversion result"""
        action_type = action.get("action_type", "unknown")
        
        return {
            "original_action": action_type,
            "playwright_method": None,
            "playwright_code": f"// ERROR: Failed to convert {action_type}",
            "parameters": {},
            "estimated_reliability": 0.0,
            "optimization_applied": False,
            "error": error
        }
    
    def batch_convert_actions(self, actions: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """Convert multiple actions in batch"""
        conversions = []
        
        for action in actions:
            conversion = self.convert_action(action)
            conversions.append(conversion)
        
        return conversions
    
    def generate_complete_script(self, actions: List[Dict[str, Any]], metadata: Dict[str, Any] = None) -> str:
        """Generate a complete Playwright test script from actions"""
        conversions = self.batch_convert_actions(actions)
        
        # Filter out failed conversions
        valid_conversions = [c for c in conversions if c.get("playwright_code") and not c.get("error")]
        
        script_lines = [
            "// Generated Playwright script",
            f"// Generated on: {metadata.get('timestamp', 'unknown')}",
            f"// Original session: {metadata.get('session_id', 'unknown')}",
            "",
            "const { test, expect } = require('@playwright/test');",
            "",
            "test('automated browser task', async ({ page }) => {",
            "  // Set viewport for consistency",
            "  await page.setViewportSize({ width: 1280, height: 720 });",
            ""
        ]
        
        # Add each converted action
        for i, conversion in enumerate(valid_conversions):
            script_lines.append(f"  // Step {i + 1}: {conversion['original_action']}")
            script_lines.append(f"  {conversion['playwright_code']}")
            
            # Add small delay between actions for stability
            if i < len(valid_conversions) - 1:
                script_lines.append("  await page.waitForTimeout(500);")
            
            script_lines.append("")
        
        script_lines.extend([
            "});",
            "",
            "// Script statistics:",
            f"// - Original actions: {len(actions)}",
            f"// - Successfully converted: {len(valid_conversions)}",
            f"// - Conversion rate: {len(valid_conversions)/len(actions)*100:.1f}%" if actions else "// - Conversion rate: 0%"
        ])
        
        return "\n".join(script_lines)
    
    def get_conversion_stats(self, actions: List[Dict[str, Any]]) -> Dict[str, Any]:
        """Get statistics about action conversions"""
        conversions = self.batch_convert_actions(actions)
        
        total_actions = len(actions)
        successful_conversions = len([c for c in conversions if not c.get("error")])
        
        reliability_scores = [c.get("estimated_reliability", 0) for c in conversions if not c.get("error")]
        avg_reliability = sum(reliability_scores) / len(reliability_scores) if reliability_scores else 0
        
        action_types = {}
        for conversion in conversions:
            action_type = conversion["original_action"]
            if action_type not in action_types:
                action_types[action_type] = {"count": 0, "successful": 0}
            action_types[action_type]["count"] += 1
            if not conversion.get("error"):
                action_types[action_type]["successful"] += 1
        
        return {
            "total_actions": total_actions,
            "successful_conversions": successful_conversions,
            "conversion_rate": successful_conversions / total_actions if total_actions > 0 else 0,
            "average_reliability": avg_reliability,
            "action_type_breakdown": action_types,
            "estimated_tokens_saved": successful_conversions * 250  # Rough estimate
        }

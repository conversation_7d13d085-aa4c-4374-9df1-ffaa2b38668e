"""
Enhanced Action Recorder that captures granular browser actions for Playwright conversion
"""
import json
import logging
from typing import Dict, List, Optional, Any
from datetime import datetime
from pathlib import Path

logger = logging.getLogger(__name__)

class EnhancedActionRecorder:
    """
    Enhanced action recorder that captures browser-use actions in Playwright-compatible format
    """
    
    def __init__(self):
        self.recorded_actions = []
        self.session_id = None
        self.recording = False
        self.current_url = None
        self.action_counter = 0
        
    def start_recording(self, session_name: str = None):
        """Start recording browser actions"""
        self.session_id = session_name or f"session_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        self.recorded_actions = []
        self.recording = True
        self.action_counter = 0
        logger.info(f"🎬 Started enhanced recording: {self.session_id}")
    
    def record_browser_action(self, action_type: str, details: Dict[str, Any], page_context: Dict[str, Any] = None):
        """Record a specific browser action in Playwright-compatible format"""
        if not self.recording:
            return
        
        self.action_counter += 1
        
        # Convert browser-use actions to Playwright-ready format
        playwright_action = self._convert_to_playwright_format(action_type, details, page_context or {})
        
        action = {
            "step_number": self.action_counter,
            "timestamp": datetime.now().isoformat(),
            "action_type": action_type,  # Original browser-use action
            "playwright_action": playwright_action,  # Converted action
            "details": details,
            "page_context": page_context or {},
            "conversion_confidence": playwright_action.get("confidence", 0.8)
        }
        
        self.recorded_actions.append(action)
        logger.debug(f"📝 Recorded action {self.action_counter}: {action_type} -> {playwright_action.get('type')}")
    
    def _convert_to_playwright_format(self, action_type: str, details: Dict[str, Any], page_context: Dict[str, Any]) -> Dict[str, Any]:
        """Convert browser-use action to Playwright format"""
        
        if action_type == "goto" or action_type == "navigate":
            return {
                "type": "page.goto",
                "code": f"await page.goto('{details.get('url', '')}');",
                "parameters": {"url": details.get("url", "")},
                "confidence": 0.95
            }
        
        elif action_type == "click":
            selector = self._extract_best_selector(details)
            return {
                "type": "page.click",
                "code": f"await page.click('{selector}');",
                "parameters": {"selector": selector},
                "selector_quality": self._rate_selector_quality(selector),
                "confidence": 0.9 if selector else 0.5
            }
        
        elif action_type == "fill" or action_type == "type":
            selector = self._extract_best_selector(details)
            text = details.get("text", "")
            return {
                "type": "page.fill",
                "code": f"await page.fill('{selector}', '{text}');",
                "parameters": {"selector": selector, "text": text},
                "selector_quality": self._rate_selector_quality(selector),
                "confidence": 0.9 if selector else 0.5
            }
        
        elif action_type == "select":
            selector = self._extract_best_selector(details)
            value = details.get("value", "")
            return {
                "type": "page.selectOption",
                "code": f"await page.selectOption('{selector}', '{value}');",
                "parameters": {"selector": selector, "value": value},
                "confidence": 0.85
            }
        
        elif action_type == "scroll":
            amount = details.get("amount", 500)
            return {
                "type": "page.evaluate",
                "code": f"await page.evaluate(() => window.scrollBy(0, {amount}));",
                "parameters": {"amount": amount},
                "confidence": 0.95
            }
        
        elif action_type == "wait":
            timeout = details.get("timeout", 3000)
            return {
                "type": "page.waitForTimeout",
                "code": f"await page.waitForTimeout({timeout});",
                "parameters": {"timeout": timeout},
                "confidence": 0.95
            }
        
        elif action_type == "screenshot":
            return {
                "type": "page.screenshot",
                "code": "await page.screenshot({ path: 'screenshot.png' });",
                "parameters": {},
                "confidence": 0.9
            }
        
        else:
            # Generic action - try to infer
            return {
                "type": "unknown",
                "code": f"// Unknown action: {action_type}",
                "parameters": details,
                "confidence": 0.3,
                "needs_manual_review": True
            }
    
    def _extract_best_selector(self, details: Dict[str, Any]) -> str:
        """Extract the best selector from action details"""
        
        # Priority order for selectors (best to worst)
        if details.get("data_testid"):
            return f"[data-testid='{details['data_testid']}']"
        
        elif details.get("id"):
            return f"#{details['id']}"
        
        elif details.get("css_selector"):
            return details["css_selector"]
        
        elif details.get("xpath"):
            return details["xpath"]
        
        elif details.get("text"):
            # Use text-based selector as fallback
            return f"text='{details['text']}'"
        
        elif details.get("index") is not None:
            # Use nth-child as last resort
            return f":nth-child({details['index'] + 1})"
        
        else:
            return "body"  # Fallback selector
    
    def _rate_selector_quality(self, selector: str) -> str:
        """Rate the quality of a selector for reliability"""
        if "data-testid" in selector:
            return "excellent"
        elif selector.startswith("#"):
            return "good"
        elif selector.startswith(".") or "[class" in selector:
            return "fair"
        elif "text=" in selector:
            return "fair"
        elif "nth-child" in selector:
            return "poor"
        else:
            return "unknown"
    
    def stop_recording(self) -> Dict[str, Any]:
        """Stop recording and return session data"""
        self.recording = False
        
        # Calculate statistics
        high_confidence_actions = len([a for a in self.recorded_actions if a.get("conversion_confidence", 0) > 0.8])
        
        session_data = {
            "session_id": self.session_id,
            "actions": self.recorded_actions,
            "timestamp": datetime.now().isoformat(),
            "action_count": len(self.recorded_actions),
            "high_confidence_conversions": high_confidence_actions,
            "conversion_success_rate": high_confidence_actions / max(len(self.recorded_actions), 1),
            "ready_for_playwright": high_confidence_actions > 0
        }
        
        logger.info(f"🏁 Recording complete: {self.session_id}")
        logger.info(f"   Actions recorded: {len(self.recorded_actions)}")
        logger.info(f"   High confidence: {high_confidence_actions}")
        logger.info(f"   Conversion rate: {session_data['conversion_success_rate']:.1%}")
        
        return session_data
    
    async def save_recording_with_playwright_ready(self, filepath: str) -> Dict[str, Any]:
        """Save recording with Playwright-ready actions"""
        session_data = self.stop_recording()
        
        # Create enhanced session data with Playwright scripts
        enhanced_data = {
            **session_data,
            "playwright_script": self._generate_inline_playwright_script(),
            "selectors_library": self._extract_selectors_library(),
            "optimization_recommendations": self._generate_optimization_recommendations()
        }
        
        # Save to file
        with open(filepath, 'w') as f:
            json.dump(enhanced_data, f, indent=2)
        
        logger.info(f"💾 Enhanced recording saved: {filepath}")
        return enhanced_data
    
    def _generate_inline_playwright_script(self) -> str:
        """Generate inline Playwright script from recorded actions"""
        script_lines = [
            "// Auto-generated Playwright script",
            f"// Session: {self.session_id}",
            f"// Generated: {datetime.now().isoformat()}",
            "",
            "const { test, expect } = require('@playwright/test');",
            "",
            f"test('Recorded task - {self.session_id}', async ({{ page }}) => {{",
        ]
        
        for action in self.recorded_actions:
            playwright_action = action.get("playwright_action", {})
            code = playwright_action.get("code", "")
            confidence = action.get("conversion_confidence", 0)
            
            if code and confidence > 0.5:
                script_lines.append(f"  // Step {action['step_number']}: {action['action_type']} (confidence: {confidence:.1%})")
                script_lines.append(f"  {code}")
                script_lines.append("")
        
        script_lines.extend([
            "});",
            "",
            "// Export for reuse",
            "module.exports = {",
            f"  sessionId: '{self.session_id}',",
            "  async executeSteps(page) {",
        ])
        
        # Add reusable function
        for action in self.recorded_actions:
            playwright_action = action.get("playwright_action", {})
            code = playwright_action.get("code", "")
            confidence = action.get("conversion_confidence", 0)
            
            if code and confidence > 0.5:
                script_lines.append(f"    {code}")
        
        script_lines.extend([
            "  }",
            "};"
        ])
        
        return "\n".join(script_lines)
    
    def _extract_selectors_library(self) -> Dict[str, Any]:
        """Extract reusable selectors library"""
        selectors = {}
        
        for action in self.recorded_actions:
            playwright_action = action.get("playwright_action", {})
            params = playwright_action.get("parameters", {})
            
            if "selector" in params:
                selector = params["selector"]
                quality = self._rate_selector_quality(selector)
                
                action_type = playwright_action.get("type", "unknown")
                
                if action_type not in selectors:
                    selectors[action_type] = []
                
                selectors[action_type].append({
                    "selector": selector,
                    "quality": quality,
                    "step": action["step_number"]
                })
        
        return selectors
    
    def _generate_optimization_recommendations(self) -> List[str]:
        """Generate recommendations for optimization"""
        recommendations = []
        
        # Analyze selector quality
        poor_selectors = 0
        for action in self.recorded_actions:
            playwright_action = action.get("playwright_action", {})
            if playwright_action.get("selector_quality") == "poor":
                poor_selectors += 1
        
        if poor_selectors > 0:
            recommendations.append(f"⚠️ {poor_selectors} actions use unreliable selectors (nth-child) - consider improving with data-testid or IDs")
        
        # Check conversion confidence
        low_confidence = len([a for a in self.recorded_actions if a.get("conversion_confidence", 0) < 0.7])
        if low_confidence > 0:
            recommendations.append(f"🔍 {low_confidence} actions have low conversion confidence - manual review recommended")
        
        # Check for wait patterns
        wait_actions = len([a for a in self.recorded_actions if a.get("action_type") == "wait"])
        if wait_actions == 0:
            recommendations.append("⏱️ No explicit waits detected - consider adding page.waitForLoadState() for reliability")
        
        return recommendations

# Hook into browser-use agent to capture actions
class BrowserUseActionHook:
    """Hook to capture browser-use actions as they happen"""
    
    def __init__(self, action_recorder: EnhancedActionRecorder):
        self.recorder = action_recorder
    
    async def on_action_executed(self, action_name: str, action_params: Dict[str, Any], result: Any, page_context: Dict[str, Any]):
        """Called when browser-use executes an action"""
        
        # Convert browser-use action to our format
        action_details = {
            **action_params,
            "result": str(result) if result else None,
            "success": result is not None
        }
        
        # Record the action
        self.recorder.record_browser_action(action_name, action_details, page_context)

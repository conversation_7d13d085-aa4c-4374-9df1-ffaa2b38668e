"""
Browser automation system using browser-use with intelligent action recording and replay
"""
import asyncio
import json
import logging
from typing import Dict, List, Optional, Any, Tuple, Callable
from datetime import datetime
from pathlib import Path
import base64
import hashlib

from browser_use import Agent, BrowserSession, BrowserProfile
from browser_use.agent.memory import MemoryConfig
from langchain_openai import ChatOpenAI
from langchain_anthropic import ChatAnthropic

from config import config
from memory_system.memory_manager import memory_manager
from .enhanced_action_recorder import EnhancedActionRecorder, BrowserUseActionHook

logger = logging.getLogger(__name__)

# Import Playwright integration after other imports to avoid circular imports
try:
    from .playwright_generator.integration_manager import integration_manager
    PLAYWRIGHT_INTEGRATION_AVAILABLE = True
except ImportError as e:
    logger.debug(f"Playwright integration not available: {e}")
    PLAYWRIGHT_INTEGRATION_AVAILABLE = False

class ActionRecorder:
    """Records and replays browser actions with intelligent pattern recognition"""
    
    def __init__(self):
        self.recorded_actions = []
        self.current_session = None
        self.session_id = None
        self.recording = False
        
    def start_recording(self, session_name: str = None):
        """Start recording browser actions"""
        self.session_id = session_name or f"session_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        self.recorded_actions = []
        self.recording = True
        logger.info(f"Started recording session: {self.session_id}")
    
    def stop_recording(self) -> Dict[str, Any]:
        """Stop recording and return the recorded sequence"""
        self.recording = False
        session_data = {
            "session_id": self.session_id,
            "actions": self.recorded_actions,
            "timestamp": datetime.now().isoformat(),
            "action_count": len(self.recorded_actions)
        }
        logger.info(f"Stopped recording session: {self.session_id} with {len(self.recorded_actions)} actions")
        return session_data
    
    def record_action(self, action_type: str, details: Dict[str, Any], screenshot: Optional[str] = None):
        """Record a single browser action"""
        if not self.recording:
            return
        
        action = {
            "timestamp": datetime.now().isoformat(),
            "action_type": action_type,
            "details": details,
            "screenshot": screenshot,
            "step_number": len(self.recorded_actions) + 1
        }
        
        self.recorded_actions.append(action)
        logger.debug(f"Recorded action {len(self.recorded_actions)}: {action_type}")
    
    async def save_recording(self, filepath: str):
        """Save recorded actions to file"""
        session_data = self.stop_recording()
        
        with open(filepath, 'w') as f:
            json.dump(session_data, f, indent=2)
        
        logger.info(f"Saved recording to {filepath}")
        return session_data

class BrowserAutomationAgent:
    """
    Advanced browser automation agent with memory, learning, and adaptation capabilities
    """
    
    def __init__(self):
        self.llm = None
        self.browser_agent = None
        self.browser_session = None
        self.action_recorder = ActionRecorder()  # Keep old recorder for compatibility
        self.enhanced_recorder = EnhancedActionRecorder()  # New enhanced recorder
        self.action_hook = BrowserUseActionHook(self.enhanced_recorder)
        self.current_page_url = None
        self.task_context = {}
        self.error_count = 0
        self.success_count = 0
        
    async def initialize(self) -> bool:
        """Initialize the browser automation system"""
        try:
            # Use enhanced LLM client for intelligent model selection
            from enhanced_llm_client import EnhancedLLMClient
            
            # Initialize enhanced LLM client for optimal model selection
            self.llm_client = EnhancedLLMClient()
            
            # For browser-use, we need a langchain-compatible LLM
            # Use the best available model based on enhanced client logic
            best_model = self.llm_client.get_best_model_for_task("browser_automation")
            api_config = config.get_api_config_for_model(best_model)
            
            if api_config["provider"] == "openrouter":
                # Use OpenAI-compatible interface for OpenRouter
                from langchain_openai import ChatOpenAI
                self.llm = ChatOpenAI(
                    model=best_model,
                    temperature=config.llm.temperature,
                    max_tokens=config.llm.max_tokens,
                    api_key=config.llm.openrouter_api_key,
                    base_url=config.llm.openrouter_base_url
                )
                logger.info(f"Initialized with OpenRouter model: {best_model}")
                
            elif api_config["provider"] == "google":
                from langchain_google_genai import ChatGoogleGenerativeAI
                self.llm = ChatGoogleGenerativeAI(
                    model=best_model.replace("google/", ""),
                    temperature=config.llm.temperature,
                    google_api_key=config.llm.google_api_key
                )
                logger.info(f"Initialized with Google model: {best_model}")
                
            elif api_config["provider"] == "openai":
                from langchain_openai import ChatOpenAI
                self.llm = ChatOpenAI(
                    model=best_model,
                    temperature=config.llm.temperature,
                    max_tokens=config.llm.max_tokens,
                    api_key=config.llm.openai_api_key
                )
                logger.info(f"Initialized with OpenAI model: {best_model}")
                
            elif api_config["provider"] == "anthropic":
                from langchain_anthropic import ChatAnthropic
                self.llm = ChatAnthropic(
                    model=best_model,
                    temperature=config.llm.temperature,
                    max_tokens=config.llm.max_tokens,
                    api_key=config.llm.anthropic_api_key
                )
                logger.info(f"Initialized with Anthropic model: {best_model}")
                
            else:
                # Fallback to enhanced client's recommendation
                fallback_models = config.get_fallback_models("browser_automation")
                for model in fallback_models:
                    try:
                        api_config = config.get_api_config_for_model(model)
                        if api_config["provider"] == "openrouter":
                            from langchain_openai import ChatOpenAI
                            self.llm = ChatOpenAI(
                                model=model,
                                temperature=config.llm.temperature,
                                max_tokens=config.llm.max_tokens,
                                api_key=config.llm.openrouter_api_key,
                                base_url=config.llm.openrouter_base_url
                            )
                            logger.info(f"Initialized with fallback OpenRouter model: {model}")
                            break
                    except Exception as e:
                        logger.debug(f"Failed to initialize {model}: {e}")
                        continue
                else:
                    raise ValueError("No working LLM configuration found")
            
            # Initialize browser-use without creating session here
            # Session will be created per task to avoid async issues
            logger.info("Browser automation agent initialized successfully")
            return True
            
        except Exception as e:
            logger.error(f"Failed to initialize browser automation agent: {e}")
            return False
    
    async def execute_task_optimally(self, 
                                   task_description: str, 
                                   context: Optional[Dict[str, Any]] = None,
                                   max_steps: int = None) -> Dict[str, Any]:
        """
        Execute task using the optimal method (LLM vs Playwright) based on available patterns
        """
        try:
            if PLAYWRIGHT_INTEGRATION_AVAILABLE:
                logger.info("Using Playwright integration for optimal execution")
                
                # Initialize integration manager if needed
                if not integration_manager.initialized:
                    await integration_manager.initialize()
                
                # Use integration manager for optimal execution
                result = await integration_manager.execute_task_optimally(
                    task_description, context
                )
                
                # If Playwright execution failed, result will contain fallback to LLM
                return result
                
            else:
                logger.info("Playwright integration not available, using standard LLM execution")
                # Fallback to standard execution
                return await self.execute_task(task_description, context, True, max_steps)
                
        except Exception as e:
            logger.error(f"Optimal execution failed: {e}")
            # Fallback to standard execution on any error
            return await self.execute_task(task_description, context, True, max_steps)
    
    async def generate_playwright_script(self, session_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Generate Playwright script from a successful session
        """
        try:
            if not PLAYWRIGHT_INTEGRATION_AVAILABLE:
                return {"error": "Playwright integration not available"}
            
            # Initialize integration manager if needed
            if not integration_manager.initialized:
                await integration_manager.initialize()
            
            return await integration_manager.generate_script_from_session(session_data)
            
        except Exception as e:
            logger.error(f"Script generation failed: {e}")
            return {"error": str(e)}
    
    async def optimize_patterns(self, sessions: List[Dict[str, Any]]) -> Dict[str, Any]:
        """
        Analyze multiple sessions to discover and optimize patterns
        """
        try:
            if not PLAYWRIGHT_INTEGRATION_AVAILABLE:
                return {"error": "Playwright integration not available"}
            
            # Initialize integration manager if needed
            if not integration_manager.initialized:
                await integration_manager.initialize()
            
            return await integration_manager.batch_optimize_sessions(sessions)
            
        except Exception as e:
            logger.error(f"Pattern optimization failed: {e}")
            return {"error": str(e)}
    
    def get_playwright_statistics(self) -> Dict[str, Any]:
        """
        Get Playwright integration performance statistics
        """
        try:
            if not PLAYWRIGHT_INTEGRATION_AVAILABLE:
                return {"error": "Playwright integration not available"}
            
            return integration_manager.get_performance_statistics()
            
        except Exception as e:
            logger.error(f"Failed to get Playwright statistics: {e}")
            return {"error": str(e)}

    async def execute_task(self, 
                          task_description: str, 
                          context: Optional[Dict[str, Any]] = None,
                          record_session: bool = True,
                          max_steps: int = None) -> Dict[str, Any]:
        """
        Execute a browser automation task with memory and learning
        """
        start_time = datetime.now()
        task_id = hashlib.md5(f"{task_description}_{start_time}".encode()).hexdigest()[:8]
        
        if record_session:
            self.action_recorder.start_recording(f"task_{task_id}")
            self.enhanced_recorder.start_recording(f"enhanced_{task_id}")
        
        try:
            # Get relevant memory context
            memory_context = await memory_manager.get_relevant_context(task_description)
            
            # Combine contexts
            full_context = {
                "task_id": task_id,
                "user_context": context or {},
                "memory_context": memory_context,
                "browser_config": {
                    "headless": config.browser.headless,
                    "timeout": config.browser.browser_timeout
                }
            }
            
            # Create enhanced task description with context
            enhanced_task = await self._create_enhanced_task(task_description, full_context)
            
            # Create browser session for this task (avoids async issues)
            browser_profile = BrowserProfile(
                headless=config.browser.headless,
                color_scheme='dark'
            )
            
            self.browser_session = BrowserSession(
                browser_profile=browser_profile,
                keep_alive=False,  # Close after task
                executable_path=config.browser.executable_path,
                user_data_dir=config.browser.user_data_dir
            )
            
            # Create browser agent with advanced memory configuration
            memory_config = MemoryConfig(
                llm_instance=self.llm,
                agent_id=f"browser_agent_{task_id}",
                memory_interval=config.agent.memory_interval,
                embedder_provider="openai" if config.llm.openai_api_key else "default",
                embedder_model="text-embedding-3-large" if config.llm.openai_api_key else "default",
                vector_store_provider="faiss",  # Use local FAISS for better performance
                vector_store_collection_name=f"browser_memories_{task_id}"
            )
            
            browser_agent = Agent(
                task=enhanced_task,
                llm=self.llm,
                browser_session=self.browser_session,
                use_vision=True,
                enable_memory=config.agent.enable_memory,
                memory_config=memory_config,
                save_conversation_path=f"{config.storage.logs_dir}/conversations/task_{task_id}.json"
            )
            
            # Set up hooks for action recording
            browser_agent.on_step_start = self._on_step_start_hook
            browser_agent.on_step_end = self._on_step_end_hook
            browser_agent.on_error = self._on_error_hook
            
            # Execute the task
            max_steps_config = max_steps or config.agent.max_steps_per_task
            result = await browser_agent.run(max_steps=max_steps_config)
            
            # Close browser session
            if self.browser_session:
                await self.browser_session.close()
                self.browser_session = None
            
            # Process results
            execution_time = (datetime.now() - start_time).total_seconds()
            
            task_result = {
                "task_id": task_id,
                "task_description": task_description,
                "success": True,
                "result": str(result),  # Convert to string to avoid serialization issues
                "execution_time": execution_time,
                "steps_taken": getattr(result, 'steps', 0) if hasattr(result, 'steps') else 0,
                "errors": [],
                "strategies": self._extract_strategies_from_result(result),
                "context_used": full_context,
                "final_url": await self._get_current_url()
            }
            
            # Record success
            self.success_count += 1
            
            # Save to memory
            await memory_manager.store_task_result(task_description, task_result, True)
            
            # Save recording if enabled
            if record_session:
                recording_path = f"{config.storage.recordings_dir}/task_{task_id}.json"
                await self.action_recorder.save_recording(recording_path)
                task_result["recording_path"] = recording_path
                
                # Save enhanced recording with Playwright conversion
                enhanced_path = f"{config.storage.recordings_dir}/enhanced_{task_id}.json"
                enhanced_data = await self.enhanced_recorder.save_recording_with_playwright_ready(enhanced_path)
                task_result["enhanced_recording_path"] = enhanced_path
                task_result["playwright_ready"] = enhanced_data.get("ready_for_playwright", False)
                task_result["conversion_success_rate"] = enhanced_data.get("conversion_success_rate", 0)
                
                # Auto-generate Playwright script if high conversion rate
                if enhanced_data.get("conversion_success_rate", 0) > 0.7:
                    await self._auto_generate_playwright_script(enhanced_data, task_id)
                    task_result["playwright_script_generated"] = True
            
            logger.info(f"Task completed successfully: {task_id}")
            return task_result
            
        except Exception as e:
            # Close browser session if open
            if self.browser_session:
                try:
                    await self.browser_session.close()
                except:
                    pass
                self.browser_session = None
            
            # Handle task failure
            execution_time = (datetime.now() - start_time).total_seconds()
            
            task_result = {
                "task_id": task_id,
                "task_description": task_description,
                "success": False,
                "error": str(e),
                "execution_time": execution_time,
                "steps_taken": 0,
                "errors": [str(e)],
                "strategies": [],
                "context_used": full_context if 'full_context' in locals() else {}
            }
            
            # Record failure
            self.error_count += 1
            
            # Store error pattern
            await memory_manager.store_error_pattern(
                error_type=type(e).__name__,
                context={
                    "task": task_description,
                    "error": str(e),
                    "browser_state": await self._get_browser_state()
                }
            )
            
            # Save to memory
            await memory_manager.store_task_result(task_description, task_result, False)
            
            logger.error(f"Task failed: {task_id} - {e}")
            return task_result
    
    async def replay_recorded_session(self, recording_path: str, adapt_to_changes: bool = True) -> Dict[str, Any]:
        """
        Replay a recorded browser session with intelligent adaptation
        """
        try:
            # Load recording
            with open(recording_path, 'r') as f:
                session_data = json.load(f)
            
            actions = session_data.get("actions", [])
            session_id = session_data.get("session_id", "unknown")
            
            logger.info(f"Replaying session: {session_id} with {len(actions)} actions")
            
            # Start new recording for comparison
            self.action_recorder.start_recording(f"replay_{session_id}")
            
            replay_results = []
            
            for i, action in enumerate(actions):
                try:
                    if adapt_to_changes:
                        # Use LLM to adapt action to current page state
                        adapted_action = await self._adapt_action_to_current_state(action)
                        result = await self._execute_single_action(adapted_action)
                    else:
                        # Execute original action
                        result = await self._execute_single_action(action)
                    
                    replay_results.append({
                        "step": i + 1,
                        "original_action": action,
                        "result": result,
                        "success": True
                    })
                    
                    # Small delay between actions
                    await asyncio.sleep(0.5)
                    
                except Exception as e:
                    logger.error(f"Failed to replay action {i + 1}: {e}")
                    replay_results.append({
                        "step": i + 1,
                        "original_action": action,
                        "error": str(e),
                        "success": False
                    })
            
            # Save replay recording
            replay_recording = self.action_recorder.stop_recording()
            
            return {
                "original_session": session_id,
                "replay_results": replay_results,
                "success_rate": sum(1 for r in replay_results if r["success"]) / len(replay_results),
                "replay_recording": replay_recording
            }
            
        except Exception as e:
            logger.error(f"Failed to replay session: {e}")
            return {"error": str(e), "success": False}
    
    async def _create_enhanced_task(self, original_task: str, context: Dict[str, Any]) -> str:
        """Create an enhanced task description with memory context"""
        
        memory_context = context.get("memory_context", {})
        
        # Build context string
        context_parts = []
        
        # User preferences
        if memory_context.get("user_preferences"):
            prefs = memory_context["user_preferences"]
            context_parts.append(f"User preferences: {json.dumps(prefs)}")
        
        # Similar successful tasks
        if memory_context.get("similar_tasks"):
            context_parts.append("Previous similar tasks:")
            for task in memory_context["similar_tasks"][:3]:  # Top 3
                context_parts.append(f"- {task['task']} (Success: {task['success']})")
        
        # Success strategies
        if memory_context.get("success_strategies"):
            context_parts.append("Recommended strategies:")
            for strategy in memory_context["success_strategies"][:3]:
                context_parts.append(f"- {strategy}")
        
        # Website knowledge
        if memory_context.get("relevant_websites"):
            context_parts.append("Website-specific knowledge:")
            for domain, knowledge in memory_context["relevant_websites"].items():
                if knowledge.get("selectors"):
                    context_parts.append(f"- {domain}: Known selectors and patterns available")
        
        # Error prevention
        if memory_context.get("error_prevention"):
            context_parts.append("Error prevention tips:")
            for tip in memory_context["error_prevention"][:3]:
                context_parts.append(f"- Avoid {tip['error_type']}: {tip['solution']}")
        
        # Combine everything
        if context_parts:
            enhanced_task = f"""
TASK: {original_task}

CONTEXT AND MEMORY:
{chr(10).join(context_parts)}

Please use this context to optimize your approach and avoid known issues.
"""
        else:
            enhanced_task = original_task
        
        return enhanced_task
    
    async def _on_step_start_hook(self, step_info: Dict[str, Any]):
        """Hook called at the start of each browser action step"""
        self.action_recorder.record_action(
            "step_start",
            {
                "step_number": step_info.get("step", 0),
                "planned_action": step_info.get("action", "unknown"),
                "page_url": await self._get_current_url()
            }
        )
    
    async def _on_step_end_hook(self, step_info: Dict[str, Any]):
        """Hook called at the end of each browser action step"""
        screenshot = await self._capture_screenshot()
        
        self.action_recorder.record_action(
            "step_end",
            {
                "step_number": step_info.get("step", 0),
                "completed_action": step_info.get("action", "unknown"),
                "result": step_info.get("result", "unknown"),
                "page_url": await self._get_current_url()
            },
            screenshot
        )
        
        # Store website knowledge if we're on a new page
        current_url = await self._get_current_url()
        if current_url != self.current_page_url:
            await self._store_page_knowledge(current_url)
            self.current_page_url = current_url
    
    async def _on_error_hook(self, error_info: Dict[str, Any]):
        """Hook called when an error occurs"""
        screenshot = await self._capture_screenshot()
        
        self.action_recorder.record_action(
            "error",
            {
                "error_type": error_info.get("type", "unknown"),
                "error_message": error_info.get("message", "unknown"),
                "page_url": await self._get_current_url(),
                "browser_state": await self._get_browser_state()
            },
            screenshot
        )
        
        # Store error pattern
        await memory_manager.store_error_pattern(
            error_type=error_info.get("type", "unknown"),
            context=error_info,
            solution=None  # Will be filled when error is resolved
        )
    
    async def _get_current_url(self) -> Optional[str]:
        """Get the current page URL"""
        try:
            if self.browser_session and hasattr(self.browser_session, 'page'):
                page = await self.browser_session.page
                return page.url if page else None
            return None
        except Exception:
            return None
    
    async def _capture_screenshot(self) -> Optional[str]:
        """Capture a screenshot and return as base64 string"""
        try:
            if not config.browser.screenshot_on_action:
                return None
                
            if self.browser_session and hasattr(self.browser_session, 'page'):
                page = await self.browser_session.page
                if page:
                    screenshot = await page.screenshot()
                    return base64.b64encode(screenshot).decode()
            return None
        except Exception as e:
            logger.debug(f"Failed to capture screenshot: {e}")
            return None
    
    async def _get_browser_state(self) -> Dict[str, Any]:
        """Get current browser state information"""
        try:
            state = {
                "url": await self._get_current_url(),
                "timestamp": datetime.now().isoformat()
            }
            
            if self.browser_session and hasattr(self.browser_session, 'page'):
                page = await self.browser_session.page
                if page:
                    state.update({
                        "title": await page.title(),
                        "viewport": page.viewport_size
                    })
            
            return state
        except Exception:
            return {"timestamp": datetime.now().isoformat()}
    
    async def _store_page_knowledge(self, url: str):
        """Store knowledge about the current page"""
        try:
            if not self.browser_session:
                return
            
            page = await self.browser_session.page
            if not page:
                return
            
            # Extract page knowledge
            knowledge = {
                "title": await page.title(),
                "url": url,
                "timestamp": datetime.now().isoformat(),
                "structure": {},
                "selectors": {},
                "forms": {},
                "common_elements": {}
            }
            
            # Get basic page structure
            try:
                # Count common elements
                element_counts = await page.evaluate("""
                    () => {
                        return {
                            buttons: document.querySelectorAll('button').length,
                            inputs: document.querySelectorAll('input').length,
                            links: document.querySelectorAll('a').length,
                            forms: document.querySelectorAll('form').length,
                            images: document.querySelectorAll('img').length
                        };
                    }
                """)
                knowledge["structure"] = element_counts
            except Exception as e:
                logger.debug(f"Failed to extract page structure: {e}")
            
            # Store the knowledge
            await memory_manager.store_website_knowledge(url, knowledge)
            
        except Exception as e:
            logger.debug(f"Failed to store page knowledge: {e}")
    
    def _extract_strategies_from_result(self, result: Any) -> List[str]:
        """Extract successful strategies from task result"""
        strategies = []
        
        # This would be enhanced based on the actual result structure
        # For now, return basic strategy info
        if hasattr(result, 'actions'):
            strategies.append("step_by_step_execution")
        
        if hasattr(result, 'vision_used') and result.vision_used:
            strategies.append("visual_element_recognition")
        
        return strategies
    
    async def _adapt_action_to_current_state(self, original_action: Dict[str, Any]) -> Dict[str, Any]:
        """Adapt a recorded action to the current page state using LLM"""
        try:
            # Get current page state
            current_state = await self._get_browser_state()
            screenshot = await self._capture_screenshot()
            
            # Create adaptation prompt
            adaptation_prompt = f"""
            Original action: {json.dumps(original_action)}
            Current page state: {json.dumps(current_state)}
            
            The original action was recorded on a different page state. 
            Please adapt this action to work with the current page state.
            Consider that element selectors or positions might have changed.
            
            Return the adapted action in the same format as the original.
            """
            
            # Use LLM to adapt (simplified - would need proper implementation)
            # For now, return original action
            return original_action
            
        except Exception as e:
            logger.debug(f"Failed to adapt action: {e}")
            return original_action
    
    async def _execute_single_action(self, action: Dict[str, Any]) -> Dict[str, Any]:
        """Execute a single browser action"""
        try:
            action_type = action.get("action_type", "unknown")
            details = action.get("details", {})
            
            # This would need to be implemented based on the specific action types
            # For now, return a success result
            return {
                "success": True,
                "action_type": action_type,
                "details": details,
                "timestamp": datetime.now().isoformat()
            }
            
        except Exception as e:
            return {
                "success": False,
                "error": str(e),
                "timestamp": datetime.now().isoformat()
            }
    
    async def get_statistics(self) -> Dict[str, Any]:
        """Get agent performance statistics"""
        total_tasks = self.success_count + self.error_count
        success_rate = (self.success_count / total_tasks) if total_tasks > 0 else 0
        
        # Include enhanced recording stats
        enhanced_stats = self.get_enhanced_recording_stats()
        
        return {
            "total_tasks": total_tasks,
            "successful_tasks": self.success_count,
            "failed_tasks": self.error_count,
            "success_rate": success_rate,
            "current_session": self.action_recorder.session_id,
            "recording_active": self.action_recorder.recording,
            "enhanced_recording": enhanced_stats
        }
    
    def _setup_enhanced_action_capture(self, browser_agent):
        """Setup enhanced action capture hooks for browser-use agent"""
        try:
            # Wrap browser-use methods to capture actions
            original_methods = {}
            
            # Hook into common browser-use actions
            action_methods = ['click', 'fill', 'goto', 'select', 'scroll', 'wait']
            
            for method_name in action_methods:
                if hasattr(browser_agent, method_name):
                    original_method = getattr(browser_agent, method_name)
                    original_methods[method_name] = original_method
                    
                    # Create wrapped method
                    wrapped_method = self._create_action_wrapper(method_name, original_method)
                    setattr(browser_agent, method_name, wrapped_method)
            
            logger.debug(f"✅ Enhanced action capture setup for {len(original_methods)} methods")
            
        except Exception as e:
            logger.debug(f"Failed to setup enhanced action capture: {e}")
    
    def _create_action_wrapper(self, action_name: str, original_method):
        """Create wrapper for browser-use action to capture it"""
        async def wrapped_action(*args, **kwargs):
            try:
                # Capture action details before execution
                action_details = {
                    "args": args,
                    "kwargs": kwargs,
                    "method": action_name
                }
                
                # Execute original action
                result = await original_method(*args, **kwargs)
                
                # Get page context
                page_context = await self._get_browser_state()
                
                # Record the action
                await self.action_hook.on_action_executed(
                    action_name, action_details, result, page_context
                )
                
                return result
                
            except Exception as e:
                logger.debug(f"Action wrapper failed for {action_name}: {e}")
                # Still try to execute original method
                return await original_method(*args, **kwargs)
        
        return wrapped_action
    
    async def _auto_generate_playwright_script(self, enhanced_data: Dict[str, Any], task_id: str):
        """Auto-generate Playwright script from enhanced recording"""
        try:
            if not PLAYWRIGHT_INTEGRATION_AVAILABLE:
                logger.debug("Playwright integration not available for script generation")
                return
            
            # Generate script using the integration manager
            script_result = await integration_manager.generate_script_from_session(enhanced_data)
            
            if script_result and not script_result.get("error"):
                script_path = f"{config.storage.playwright_scripts_dir}/auto_{task_id}.js"
                
                # Save the generated script
                with open(script_path, 'w') as f:
                    f.write(enhanced_data.get("playwright_script", ""))
                
                logger.info(f"🎭 Auto-generated Playwright script: {script_path}")
                logger.info(f"   📊 Estimated token savings: {enhanced_data.get('conversion_success_rate', 0):.1%}")
                
                return script_path
            
        except Exception as e:
            logger.debug(f"Auto-script generation failed: {e}")
    
    async def cleanup(self):
        """Cleanup browser resources"""
        try:
            if self.browser_session:
                await self.browser_session.close()
            logger.info("Browser automation agent cleanup completed")
        except Exception as e:
            logger.error(f"Error during cleanup: {e}")

    def get_enhanced_recording_stats(self) -> Dict[str, Any]:
        """Get statistics about enhanced recording and conversion"""
        if not hasattr(self.enhanced_recorder, 'recorded_actions'):
            return {"error": "No enhanced recording data available"}
        
        actions = self.enhanced_recorder.recorded_actions
        
        if not actions:
            return {"message": "No actions recorded yet"}
        
        high_confidence = len([a for a in actions if a.get("conversion_confidence", 0) > 0.8])
        
        return {
            "total_actions_recorded": len(actions),
            "high_confidence_conversions": high_confidence,
            "conversion_success_rate": high_confidence / len(actions) if actions else 0,
            "playwright_ready": high_confidence > 0,
            "last_action": actions[-1].get("action_type") if actions else None
        }

# Global browser automation agent instance
browser_agent = BrowserAutomationAgent()

"""
Memory system package for intelligent browser automation agent
"""

import logging

logger = logging.getLogger(__name__)

# Try to import Letta-based memory system, fall back to simplified version
try:
    from .memory_manager import memory_manager, MemoryManager
    logger.info("Using full Letta-based memory system")
except ImportError as e:
    logger.warning(f"Letta not available ({e}), using simplified memory system")
    from .fallback_memory import memory_manager, MemoryManager

__all__ = ['memory_manager', 'MemoryManager']

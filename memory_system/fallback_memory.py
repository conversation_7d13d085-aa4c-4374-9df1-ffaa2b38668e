"""
Fallback memory system for cases where <PERSON><PERSON> is not available
This provides basic memory functionality using local storage
"""
import json
import logging
from typing import Dict, List, Optional, Any, Tuple
from datetime import datetime, timedelta
from pathlib import Path
import sqlite3
import hashlib

from config import config

logger = logging.getLogger(__name__)

class SimplifiedMemoryManager:
    """
    Simplified memory management system using local storage
    Provides basic memory functionality when <PERSON><PERSON> is not available
    """
    
    def __init__(self):
        self.memory_blocks = {}
        self.session_context = {}
        self.task_history = []
        self.learning_data = {}
        self.db_path = Path(config.storage.data_dir) / "memory.db"
        self.initialized = False
        
    async def initialize(self) -> bool:
        """Initialize the simplified memory system"""
        try:
            # Create data directory if it doesn't exist
            self.db_path.parent.mkdir(parents=True, exist_ok=True)
            
            # Initialize SQLite database
            self._init_database()
            
            # Load existing memory blocks
            await self._load_memory_blocks()
            
            self.initialized = True
            logger.info("Simplified memory system initialized successfully")
            return True
            
        except Exception as e:
            logger.error(f"Failed to initialize simplified memory system: {e}")
            return False
    
    def _init_database(self):
        """Initialize SQLite database for memory storage"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        # Create tables
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS memory_blocks (
                id TEXT PRIMARY KEY,
                label TEXT NOT NULL,
                value TEXT NOT NULL,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        ''')
        
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS task_history (
                id TEXT PRIMARY KEY,
                task_description TEXT NOT NULL,
                success BOOLEAN NOT NULL,
                result TEXT NOT NULL,
                timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        ''')
        
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS learning_data (
                id TEXT PRIMARY KEY,
                category TEXT NOT NULL,
                data TEXT NOT NULL,
                timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        ''')
        
        conn.commit()
        conn.close()
    
    async def _load_memory_blocks(self):
        """Load memory blocks from database"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            cursor.execute('SELECT label, value FROM memory_blocks')
            rows = cursor.fetchall()
            
            for label, value in rows:
                try:
                    # Try to parse as JSON
                    self.memory_blocks[label] = json.loads(value)
                except json.JSONDecodeError:
                    # Store as string if not JSON
                    self.memory_blocks[label] = value
            
            conn.close()
            
            # Initialize default blocks if none exist
            if not self.memory_blocks:
                await self._initialize_default_blocks()
                
            logger.info(f"Loaded {len(self.memory_blocks)} memory blocks")
            
        except Exception as e:
            logger.error(f"Failed to load memory blocks: {e}")
            await self._initialize_default_blocks()
    
    async def _initialize_default_blocks(self):
        """Initialize default memory blocks"""
        default_blocks = {
            "user_profile": {
                "name": "User",
                "preferences": {},
                "interaction_patterns": {},
                "learned_behaviors": []
            },
            "task_history": {
                "completed_tasks": [],
                "failed_tasks": [],
                "patterns": {},
                "success_strategies": {}
            },
            "website_knowledge": {
                "known_sites": {},
                "navigation_patterns": {},
                "element_selectors": {},
                "form_patterns": {}
            },
            "error_patterns": {
                "common_errors": {},
                "recovery_strategies": {},
                "prevention_methods": {},
                "debugging_info": {}
            }
        }
        
        for label, data in default_blocks.items():
            await self.update_memory_block(label, data)
    
    async def update_memory_block(self, block_label: str, new_data: Any):
        """Update a specific memory block"""
        try:
            # Prepare the data
            if isinstance(new_data, (dict, list)):
                value = json.dumps(new_data)
            else:
                value = str(new_data)
            
            # Update in database
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            cursor.execute('''
                INSERT OR REPLACE INTO memory_blocks (id, label, value, updated_at)
                VALUES (?, ?, ?, ?)
            ''', (block_label, block_label, value, datetime.now().isoformat()))
            
            conn.commit()
            conn.close()
            
            # Update local cache
            self.memory_blocks[block_label] = new_data
            
            logger.debug(f"Updated memory block: {block_label}")
            
        except Exception as e:
            logger.error(f"Failed to update memory block {block_label}: {e}")
    
    async def append_to_memory_block(self, block_label: str, new_data: Any):
        """Append data to a memory block"""
        try:
            current_data = self.memory_blocks.get(block_label, [])
            
            if isinstance(current_data, list):
                current_data.append(new_data)
            elif isinstance(current_data, dict) and isinstance(new_data, dict):
                current_data.update(new_data)
            else:
                current_data = [current_data, new_data]
            
            await self.update_memory_block(block_label, current_data)
            
        except Exception as e:
            logger.error(f"Failed to append to memory block {block_label}: {e}")
    
    async def store_task_result(self, task_description: str, result: Dict[str, Any], success: bool):
        """Store the result of a completed task"""
        try:
            task_id = hashlib.md5(f"{task_description}_{datetime.now()}".encode()).hexdigest()[:8]
            
            # Store in database
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            cursor.execute('''
                INSERT INTO task_history (id, task_description, success, result)
                VALUES (?, ?, ?, ?)
            ''', (task_id, task_description, success, json.dumps(result, default=str)))
            
            conn.commit()
            conn.close()
            
            # Update memory blocks
            task_history = self.memory_blocks.get("task_history", {"completed_tasks": [], "failed_tasks": []})
            
            task_record = {
                "task": task_description,
                "timestamp": datetime.now().isoformat(),
                "success": success,
                "result": result
            }
            
            if success:
                task_history["completed_tasks"].append(task_record)
            else:
                task_history["failed_tasks"].append(task_record)
            
            # Limit history size
            if len(task_history["completed_tasks"]) > 100:
                task_history["completed_tasks"] = task_history["completed_tasks"][-50:]
            if len(task_history["failed_tasks"]) > 50:
                task_history["failed_tasks"] = task_history["failed_tasks"][-25:]
            
            await self.update_memory_block("task_history", task_history)
            
            logger.debug(f"Stored task result: {task_description} (success: {success})")
            
        except Exception as e:
            logger.error(f"Failed to store task result: {e}")
    
    async def store_website_knowledge(self, url: str, knowledge: Dict[str, Any]):
        """Store knowledge about a specific website"""
        try:
            website_knowledge = self.memory_blocks.get("website_knowledge", {"known_sites": {}})
            
            domain = self._extract_domain(url)
            
            if domain not in website_knowledge["known_sites"]:
                website_knowledge["known_sites"][domain] = {}
            
            site_data = website_knowledge["known_sites"][domain]
            site_data.update({
                "last_visited": datetime.now().isoformat(),
                "structure": knowledge.get("structure", {}),
                "selectors": knowledge.get("selectors", {}),
                "navigation": knowledge.get("navigation", {}),
                "forms": knowledge.get("forms", {}),
                "common_elements": knowledge.get("common_elements", {})
            })
            
            await self.update_memory_block("website_knowledge", website_knowledge)
            
        except Exception as e:
            logger.error(f"Failed to store website knowledge: {e}")
    
    async def store_error_pattern(self, error_type: str, context: Dict[str, Any], solution: Optional[str] = None):
        """Store information about an error and its solution"""
        try:
            error_patterns = self.memory_blocks.get("error_patterns", {"common_errors": {}})
            
            error_record = {
                "timestamp": datetime.now().isoformat(),
                "context": context,
                "solution": solution,
                "frequency": 1
            }
            
            if error_type not in error_patterns["common_errors"]:
                error_patterns["common_errors"][error_type] = []
            
            error_patterns["common_errors"][error_type].append(error_record)
            
            # Limit error history
            if len(error_patterns["common_errors"][error_type]) > 20:
                error_patterns["common_errors"][error_type] = error_patterns["common_errors"][error_type][-10:]
            
            await self.update_memory_block("error_patterns", error_patterns)
            
        except Exception as e:
            logger.error(f"Failed to store error pattern: {e}")
    
    async def get_relevant_context(self, task_description: str, max_items: int = 5) -> Dict[str, Any]:
        """Get relevant context for a task from memory"""
        context = {
            "user_preferences": {},
            "similar_tasks": [],
            "relevant_websites": {},
            "error_prevention": [],
            "success_strategies": []
        }
        
        try:
            # Get user preferences
            user_profile = self.memory_blocks.get("user_profile", {})
            context["user_preferences"] = user_profile.get("preferences", {})
            
            # Find similar tasks
            task_history = self.memory_blocks.get("task_history", {"completed_tasks": []})
            
            similar_tasks = []
            for task in task_history.get("completed_tasks", []):
                if self._calculate_task_similarity(task_description, task["task"]) > 0.6:
                    similar_tasks.append(task)
            
            context["similar_tasks"] = sorted(similar_tasks, 
                                            key=lambda x: x.get("timestamp", ""), 
                                            reverse=True)[:max_items]
            
            # Get relevant website knowledge
            website_knowledge = self.memory_blocks.get("website_knowledge", {"known_sites": {}})
            for domain, site_data in website_knowledge["known_sites"].items():
                if domain.lower() in task_description.lower():
                    context["relevant_websites"][domain] = site_data
            
            # Get error prevention info
            error_patterns = self.memory_blocks.get("error_patterns", {"common_errors": {}})
            for error_type, errors in error_patterns["common_errors"].items():
                for error in errors[-3:]:  # Last 3 errors of each type
                    if error.get("solution"):
                        context["error_prevention"].append({
                            "error_type": error_type,
                            "solution": error["solution"],
                            "frequency": error.get("frequency", 1)
                        })
            
        except Exception as e:
            logger.error(f"Failed to get relevant context: {e}")
        
        return context
    
    def _extract_domain(self, url: str) -> str:
        """Extract domain from URL"""
        from urllib.parse import urlparse
        return urlparse(url).netloc.lower()
    
    def _calculate_task_similarity(self, task1: str, task2: str) -> float:
        """Calculate similarity between two tasks"""
        words1 = set(task1.lower().split())
        words2 = set(task2.lower().split())
        
        intersection = words1.intersection(words2)
        union = words1.union(words2)
        
        if not union:
            return 0.0
        
        return len(intersection) / len(union)
    
    async def consolidate_memory(self):
        """Perform periodic memory consolidation"""
        try:
            logger.info("Starting memory consolidation...")
            
            # Simple consolidation: clean up old data
            now = datetime.now()
            cutoff_date = now - timedelta(days=30)  # Keep last 30 days
            
            # This is a simplified version - a full system would do more sophisticated consolidation
            logger.info("Memory consolidation completed (simplified version)")
            
        except Exception as e:
            logger.error(f"Memory consolidation failed: {e}")
    
    async def export_memory(self, filepath: str):
        """Export agent memory to a file"""
        try:
            memory_export = {
                "timestamp": datetime.now().isoformat(),
                "memory_blocks": self.memory_blocks,
                "system": "simplified_memory_manager"
            }
            
            with open(filepath, 'w') as f:
                json.dump(memory_export, f, indent=2, default=str)
            
            logger.info(f"Memory exported to {filepath}")
            
        except Exception as e:
            logger.error(f"Failed to export memory: {e}")
    
    async def cleanup(self):
        """Cleanup resources"""
        logger.info("Simplified memory system cleanup completed")

# Try to import Letta, fall back to simplified system if not available
try:
    from .memory_manager import MemoryManager, memory_manager
    logger.info("Using full Letta-based memory system")
except ImportError:
    logger.warning("Letta not available, using simplified memory system")
    # Create simplified memory manager instance
    memory_manager = SimplifiedMemoryManager()
    MemoryManager = SimplifiedMemoryManager

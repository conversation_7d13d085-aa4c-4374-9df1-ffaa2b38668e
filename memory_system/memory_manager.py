"""
Memory system using Letta (formerly MemGPT) for persistent agent memory
"""
import asyncio
import json
import logging
import sqlite3
from typing import Dict, List, Optional, Any, Tuple
from datetime import datetime, timedelta
from pathlib import Path

# Try to import letta, fall back to simplified memory if not available
try:
    from letta import create_client
    LETTA_AVAILABLE = True
    print("✅ Letta client imported successfully")
except ImportError:
    try:
        from letta_client import Letta
        LETTA_AVAILABLE = True
        print("✅ Letta client imported successfully")
    except ImportError as e:
        print(f"Letta not available ({e}), using simplified memory system")
        LETTA_AVAILABLE = False

from config import config

logger = logging.getLogger(__name__)

class SimplifiedMemorySystem:
    """Fallback memory system when Letta is not available"""
    
    def __init__(self):
        self.db_path = Path(config.storage.data_dir) / "fallback_memory.db"
        self.memory_blocks = {}
        self.init_database()
    
    def init_database(self):
        """Initialize SQLite database for memory storage"""
        with sqlite3.connect(self.db_path) as conn:
            # Increase SQLite limits to handle larger data
            conn.execute("PRAGMA max_length = 1000000000")  # 1GB limit
            conn.execute("PRAGMA cache_size = 10000")       # Increase cache
            
            conn.execute("""
                CREATE TABLE IF NOT EXISTS memory_blocks (
                    label TEXT PRIMARY KEY,
                    value TEXT,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            """)
            
            conn.execute("""
                CREATE TABLE IF NOT EXISTS task_history (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    task_description TEXT,
                    result TEXT,
                    success BOOLEAN,
                    timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            """)
            
            conn.execute("""
                CREATE TABLE IF NOT EXISTS error_patterns (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    error_type TEXT,
                    context TEXT,
                    solution TEXT,
                    frequency INTEGER DEFAULT 1,
                    timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            """)
    
    async def initialize(self):
        """Initialize the simplified memory system"""
        # Load existing memory blocks
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.execute("SELECT label, value FROM memory_blocks")
            for label, value in cursor.fetchall():
                try:
                    self.memory_blocks[label] = json.loads(value)
                except json.JSONDecodeError:
                    self.memory_blocks[label] = value
        
        # Initialize with default blocks if empty
        if not self.memory_blocks:
            self.memory_blocks = {
                "user_profile": {"preferences": {}, "interaction_patterns": {}},
                "task_history": {"completed_tasks": [], "failed_tasks": []},
                "website_knowledge": {"known_sites": {}},
                "error_patterns": {"common_errors": {}}
            }
            await self._save_all_blocks()
        
        return True
    
    async def _save_all_blocks(self):
        """Save all memory blocks to database"""
        with sqlite3.connect(self.db_path) as conn:
            for label, value in self.memory_blocks.items():
                conn.execute(
                    "INSERT OR REPLACE INTO memory_blocks (label, value) VALUES (?, ?)",
                    (label, json.dumps(value))
                )
    
    async def update_memory_block(self, block_label: str, new_data: Any):
        """Update a specific memory block"""
        self.memory_blocks[block_label] = new_data
        with sqlite3.connect(self.db_path) as conn:
            conn.execute(
                "INSERT OR REPLACE INTO memory_blocks (label, value) VALUES (?, ?)",
                (block_label, json.dumps(new_data))
            )
    
    async def get_relevant_context(self, task_description: str, max_items: int = 5) -> Dict[str, Any]:
        """Get relevant context for a task from memory"""
        context = {
            "user_preferences": self.memory_blocks.get("user_profile", {}).get("preferences", {}),
            "similar_tasks": [],
            "relevant_websites": {},
            "error_prevention": [],
            "success_strategies": []
        }
        
        # Get similar tasks from database
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.execute(
                "SELECT task_description, result, success FROM task_history WHERE success = 1 ORDER BY timestamp DESC LIMIT ?",
                (max_items,)
            )
            for task, result_str, success in cursor.fetchall():
                try:
                    result = json.loads(result_str)
                    context["similar_tasks"].append({
                        "task": task,
                        "result": result,
                        "success": success
                    })
                except json.JSONDecodeError:
                    pass
        
        return context
    
    async def store_task_result(self, task_description: str, result: Dict[str, Any], success: bool):
        """Store the result of a completed task"""
        with sqlite3.connect(self.db_path) as conn:
            # Truncate large result data to prevent "string too big" errors
            result_str = json.dumps(result)
            if len(result_str) > 100000:  # 100KB limit
                # Keep only essential information for large results
                truncated_result = {
                    "success": result.get("success", False),
                    "execution_time": result.get("execution_time", 0),
                    "steps_taken": result.get("steps_taken", 0),
                    "execution_method": result.get("execution_method", "unknown"),
                    "error": result.get("error", ""),
                    "truncated": True,
                    "original_size": len(result_str)
                }
                result_str = json.dumps(truncated_result)
            
            conn.execute(
                "INSERT INTO task_history (task_description, result, success) VALUES (?, ?, ?)",
                (task_description, result_str, success)
            )
    
    async def store_error_pattern(self, error_type: str, context: Dict[str, Any], solution: Optional[str] = None):
        """Store information about an error and its solution"""
        with sqlite3.connect(self.db_path) as conn:
            conn.execute(
                "INSERT INTO error_patterns (error_type, context, solution) VALUES (?, ?, ?)",
                (error_type, json.dumps(context), solution)
            )
    
    async def store_website_knowledge(self, url: str, knowledge: Dict[str, Any]):
        """Store knowledge about a specific website"""
        website_knowledge = self.memory_blocks.get("website_knowledge", {"known_sites": {}})
        from urllib.parse import urlparse
        domain = urlparse(url).netloc.lower()
        
        if domain not in website_knowledge["known_sites"]:
            website_knowledge["known_sites"][domain] = {}
        
        website_knowledge["known_sites"][domain].update(knowledge)
        await self.update_memory_block("website_knowledge", website_knowledge)
    
    async def consolidate_memory(self):
        """Perform periodic memory consolidation (simplified)"""
        logger.info("Memory consolidation completed (simplified mode)")
    
    async def export_memory(self, filepath: str):
        """Export agent memory to a file"""
        memory_export = {
            "timestamp": datetime.now().isoformat(),
            "memory_blocks": self.memory_blocks,
            "system": "simplified_fallback"
        }
        
        with open(filepath, 'w') as f:
            json.dump(memory_export, f, indent=2, default=str)
    
    async def cleanup(self):
        """Cleanup resources"""
        logger.info("Simplified memory system cleanup completed")

class MemoryManager:
    """
    Advanced memory management system using Letta for persistent, self-editing memory
    """
    
    def __init__(self):
        self.use_letta = LETTA_AVAILABLE
        self.client = None
        self.agent_id = None
        self.agent_state = None
        self.memory_blocks = {}
        self.session_context = {}
        self.task_history = []
        self.learning_data = {}
        
        # Initialize fallback system
        self.fallback_system = SimplifiedMemorySystem()
        
    async def initialize(self) -> bool:
        """Initialize the memory system (Letta or fallback)"""
        if self.use_letta:
            return await self._initialize_letta()
        else:
            return await self.fallback_system.initialize()
    
    async def _initialize_letta(self) -> bool:
        """Initialize the Letta client and create/load agent"""
        try:
            # Initialize Letta client with correct API
            try:
                # Try new Letta client API
                from letta import create_client
                self.client = create_client(
                    base_url=config.memory.letta_server_url,
                    token=None  # For local server without auth
                )
            except ImportError:
                # Fall back to letta_client API
                from letta_client import Letta
                self.client = Letta(
                    base_url=config.memory.letta_server_url,
                    token=None  # For local server without auth
                )
            
            # Small delay to allow connection to establish
            await asyncio.sleep(0.5)
            
            # List existing agents
            try:
                agents = self.client.list_agents() if hasattr(self.client, 'list_agents') else self.client.agents.list()
                existing_agent = None
                
                # Look for existing browser automation agent
                for agent in agents:
                    if hasattr(agent, 'name') and 'browser_automation' in agent.name.lower():
                        existing_agent = agent
                        break
                
                if existing_agent:
                    # Load existing agent
                    self.agent_state = existing_agent
                    self.agent_id = self.agent_state.id
                    logger.info(f"Loaded existing agent: {self.agent_id}")
                else:
                    # Create new agent
                    self.agent_state = await self._create_new_agent()
                    self.agent_id = self.agent_state.id
                    logger.info(f"Created new agent: {self.agent_id}")
                
                # Load memory blocks
                await self._load_memory_blocks()
                
                return True
                
            except Exception as e:
                logger.warning(f"Letta server not available, falling back to simplified memory: {e}")
                self.use_letta = False
                return await self.fallback_system.initialize()
            
        except Exception as e:
            logger.warning(f"Failed to initialize Letta, using simplified memory: {e}")
            self.use_letta = False
            return await self.fallback_system.initialize()
    
    async def _create_new_agent(self):
        """Create a new Letta agent with advanced memory configuration and custom tools"""
        try:
            # Create custom tools for browser automation context
            from typing import Optional, TYPE_CHECKING
            if TYPE_CHECKING:
                from letta import AgentState
            
            def browser_context_store(agent_state: "AgentState", url: str, context_type: str, context_data: str):
                """Store browser context information in memory
                
                Args:
                    url (str): The URL where this context was captured
                    context_type (str): Type of context (navigation, form_data, error, success)
                    context_data (str): The actual context information to store
                
                Returns:
                    Optional[str]: Confirmation message
                """
                import json
                from datetime import datetime
                
                # Get or create browser context memory block
                try:
                    browser_context = json.loads(agent_state.memory.get_block("browser_context").value)
                except:
                    browser_context = {"contexts": [], "patterns": {}}
                
                # Add new context
                browser_context["contexts"].append({
                    "timestamp": datetime.now().isoformat(),
                    "url": url,
                    "type": context_type,
                    "data": context_data
                })
                
                # Update patterns
                if context_type not in browser_context["patterns"]:
                    browser_context["patterns"][context_type] = 0
                browser_context["patterns"][context_type] += 1
                
                # Keep only last 50 contexts to manage memory
                if len(browser_context["contexts"]) > 50:
                    browser_context["contexts"] = browser_context["contexts"][-50:]
                
                agent_state.memory.update_block_value("browser_context", json.dumps(browser_context))
                return f"Stored {context_type} context for {url}"
            
            def task_progress_tracker(agent_state: "AgentState", task_description: str, status: str, details: str):
                """Track task progress and outcomes
                
                Args:
                    task_description (str): Description of the task being tracked
                    status (str): Current status (started, in_progress, completed, failed)
                    details (str): Additional details about the task progress
                
                Returns:
                    Optional[str]: Progress summary
                """
                import json
                from datetime import datetime
                
                try:
                    progress_data = json.loads(agent_state.memory.get_block("task_progress").value)
                except:
                    progress_data = {"current_tasks": {}, "completed_tasks": [], "failed_tasks": []}
                
                timestamp = datetime.now().isoformat()
                task_id = f"task_{hash(task_description) % 10000}"
                
                if status == "started":
                    progress_data["current_tasks"][task_id] = {
                        "description": task_description,
                        "started": timestamp,
                        "status": status,
                        "details": details
                    }
                elif status in ["completed", "failed"]:
                    if task_id in progress_data["current_tasks"]:
                        task_info = progress_data["current_tasks"][task_id]
                        task_info.update({
                            "finished": timestamp,
                            "status": status,
                            "final_details": details
                        })
                        
                        # Move to appropriate list
                        if status == "completed":
                            progress_data["completed_tasks"].append(task_info)
                        else:
                            progress_data["failed_tasks"].append(task_info)
                        
                        # Remove from current tasks
                        del progress_data["current_tasks"][task_id]
                
                agent_state.memory.update_block_value("task_progress", json.dumps(progress_data))
                
                active_count = len(progress_data["current_tasks"])
                completed_count = len(progress_data["completed_tasks"])
                failed_count = len(progress_data["failed_tasks"])
                
                return f"Task {status}. Active: {active_count}, Completed: {completed_count}, Failed: {failed_count}"
            
            # Register custom tools
            browser_context_tool = self.client.tools.upsert_from_function(func=browser_context_store)
            task_progress_tool = self.client.tools.upsert_from_function(func=task_progress_tracker)
            
            # Create agent with enhanced memory blocks and custom tools
            agent_state = self.client.agents.create(
                name=f"browser_automation_agent_{datetime.now().strftime('%Y%m%d_%H%M%S')}",
                memory_blocks=[
                    {
                        "label": "user_profile",
                        "value": json.dumps({
                            "name": "User",
                            "preferences": {
                                "ui_style": "clean and simple",
                                "theme": "dark mode preferred",
                                "work_domain": "AI research",
                                "interaction_style": "efficient and direct"
                            },
                            "interaction_patterns": {},
                            "learned_behaviors": [],
                            "success_metrics": {"completed_tasks": 0, "failed_tasks": 0}
                        })
                    },
                    {
                        "label": "task_history", 
                        "value": json.dumps({
                            "completed_tasks": [],
                            "failed_tasks": [],
                            "patterns": {},
                            "success_strategies": {},
                            "common_failures": {},
                            "optimization_insights": []
                        })
                    },
                    {
                        "label": "website_knowledge",
                        "value": json.dumps({
                            "known_sites": {},
                            "navigation_patterns": {},
                            "element_selectors": {},
                            "form_patterns": {},
                            "performance_metrics": {},
                            "error_patterns": {}
                        })
                    },
                    {
                        "label": "browser_context",
                        "value": json.dumps({
                            "contexts": [],
                            "patterns": {},
                            "session_data": {}
                        })
                    },
                    {
                        "label": "task_progress",
                        "value": json.dumps({
                            "current_tasks": {},
                            "completed_tasks": [],
                            "failed_tasks": []
                        })
                    },
                    {
                        "label": "persona",
                        "value": "I am an intelligent browser automation agent with persistent memory and learning capabilities. I excel at remembering user preferences, learning from past experiences, and adapting to website changes. I maintain detailed context about every interaction to provide increasingly better assistance over time."
                    }
                ],
                tool_ids=[browser_context_tool.id, task_progress_tool.id],
                model="gpt-4o-mini",  # Use cost-effective model
                embedding="openai/text-embedding-3-small"
            )
            
            return agent_state
            
        except Exception as e:
            logger.error(f"Failed to create enhanced Letta agent: {e}")
            # Fallback to simpler agent
            agent_state = self.client.agents.create(
                name="browser_automation_agent_simple",
                memory_blocks=[
                    {
                        "label": "user_profile",
                        "value": json.dumps({
                            "name": "User",
                            "preferences": {},
                            "interaction_patterns": {},
                            "learned_behaviors": []
                        })
                    },
                    {
                        "label": "task_history", 
                        "value": json.dumps({
                            "completed_tasks": [],
                            "failed_tasks": [],
                            "patterns": {},
                            "success_strategies": {}
                        })
                    },
                    {
                        "label": "website_knowledge",
                        "value": json.dumps({
                            "known_sites": {},
                            "navigation_patterns": {},
                            "element_selectors": {},
                            "form_patterns": {}
                        })
                    },
                    {
                        "label": "persona",
                        "value": "I am an intelligent browser automation agent with persistent memory."
                    }
                ],
                model="gpt-4o-mini",
                embedding="openai/text-embedding-3-small"
            )
            
            return agent_state
    
    async def _load_memory_blocks(self):
        """Load all memory blocks from the agent"""
        if not self.use_letta:
            return
            
        try:
            # Get core memory
            memory = self.client.agents.get_core_memory(agent_id=self.agent_id)
            
            for block in memory.blocks:
                try:
                    # Try to parse as JSON, fallback to string
                    if block.label in ["user_profile", "task_history", "website_knowledge"]:
                        self.memory_blocks[block.label] = json.loads(block.value)
                    else:
                        self.memory_blocks[block.label] = block.value
                except (json.JSONDecodeError, AttributeError):
                    self.memory_blocks[block.label] = getattr(block, 'value', str(block))
            
            logger.info(f"Loaded {len(self.memory_blocks)} memory blocks")
            
        except Exception as e:
            logger.error(f"Failed to load memory blocks: {e}")
            self.memory_blocks = {}
    
    async def update_memory_block(self, block_label: str, new_data: Any):
        """Update a specific memory block"""
        if self.use_letta and self.client and self.agent_id:
            try:
                # Prepare the data
                if isinstance(new_data, (dict, list)):
                    value = json.dumps(new_data)
                else:
                    value = str(new_data)
                
                # Update in Letta
                self.client.agents.update_core_memory(
                    agent_id=self.agent_id,
                    label=block_label,
                    value=value
                )
                
                # Update local cache
                self.memory_blocks[block_label] = new_data
                
                logger.debug(f"Updated memory block: {block_label}")
                
            except Exception as e:
                logger.error(f"Failed to update memory block {block_label}: {e}")
                # Fall back to local storage
                await self.fallback_system.update_memory_block(block_label, new_data)
        else:
            await self.fallback_system.update_memory_block(block_label, new_data)
    
    async def get_relevant_context(self, task_description: str, max_items: int = 5) -> Dict[str, Any]:
        """Get relevant context for a task from memory"""
        if self.use_letta and self.memory_blocks:
            # Use Letta memory
            context = {
                "user_preferences": {},
                "similar_tasks": [],
                "relevant_websites": {},
                "error_prevention": [],
                "success_strategies": []
            }
            
            try:
                # Get user preferences
                user_profile = self.memory_blocks.get("user_profile", {})
                context["user_preferences"] = user_profile.get("preferences", {})
                
                # Find similar tasks
                task_history = self.memory_blocks.get("task_history", {"completed_tasks": []})
                similar_tasks = []
                for task in task_history.get("completed_tasks", []):
                    if self._calculate_task_similarity(task_description, task.get("task", "")) > 0.6:
                        similar_tasks.append(task)
                
                context["similar_tasks"] = sorted(similar_tasks, 
                                                key=lambda x: x.get("timestamp", ""), 
                                                reverse=True)[:max_items]
                
                # Get relevant website knowledge
                website_knowledge = self.memory_blocks.get("website_knowledge", {"known_sites": {}})
                for domain, site_data in website_knowledge["known_sites"].items():
                    if domain.lower() in task_description.lower():
                        context["relevant_websites"][domain] = site_data
                
            except Exception as e:
                logger.error(f"Failed to get relevant context from Letta: {e}")
            
            return context
        else:
            return await self.fallback_system.get_relevant_context(task_description, max_items)
    
    async def store_task_result(self, task_description: str, result: Dict[str, Any], success: bool):
        """Store the result of a completed task"""
        if self.use_letta:
            try:
                task_record = {
                    "task": task_description,
                    "timestamp": datetime.now().isoformat(),
                    "success": success,
                    "result": result,
                    "execution_time": result.get("execution_time", 0),
                    "steps_taken": result.get("steps_taken", 0),
                    "errors_encountered": result.get("errors", []),
                    "strategies_used": result.get("strategies", [])
                }
                
                # Add to task history
                task_history = self.memory_blocks.get("task_history", {"completed_tasks": [], "failed_tasks": []})
                
                if success:
                    task_history["completed_tasks"].append(task_record)
                else:
                    task_history["failed_tasks"].append(task_record)
                
                await self.update_memory_block("task_history", task_history)
                
            except Exception as e:
                logger.error(f"Failed to store task result in Letta: {e}")
                await self.fallback_system.store_task_result(task_description, result, success)
        else:
            await self.fallback_system.store_task_result(task_description, result, success)
    
    async def store_website_knowledge(self, url: str, knowledge: Dict[str, Any]):
        """Store knowledge about a specific website"""
        if self.use_letta:
            try:
                website_knowledge = self.memory_blocks.get("website_knowledge", {"known_sites": {}})
                
                domain = self._extract_domain(url)
                
                if domain not in website_knowledge["known_sites"]:
                    website_knowledge["known_sites"][domain] = {}
                
                site_data = website_knowledge["known_sites"][domain]
                site_data.update({
                    "last_visited": datetime.now().isoformat(),
                    "structure": knowledge.get("structure", {}),
                    "selectors": knowledge.get("selectors", {}),
                    "navigation": knowledge.get("navigation", {}),
                    "forms": knowledge.get("forms", {}),
                    "common_elements": knowledge.get("common_elements", {})
                })
                
                await self.update_memory_block("website_knowledge", website_knowledge)
                
            except Exception as e:
                logger.error(f"Failed to store website knowledge in Letta: {e}")
                await self.fallback_system.store_website_knowledge(url, knowledge)
        else:
            await self.fallback_system.store_website_knowledge(url, knowledge)
    
    async def store_error_pattern(self, error_type: str, context: Dict[str, Any], solution: Optional[str] = None):
        """Store information about an error and its solution"""
        if self.use_letta:
            # Simplified error storage for Letta
            try:
                # Could be enhanced to use Letta's archival memory
                pass
            except Exception as e:
                logger.error(f"Failed to store error pattern in Letta: {e}")
        
        # Always store in fallback system as well for reliability
        await self.fallback_system.store_error_pattern(error_type, context, solution)
    
    def _calculate_task_similarity(self, task1: str, task2: str) -> float:
        """Calculate similarity between two tasks (simple word overlap)"""
        words1 = set(task1.lower().split())
        words2 = set(task2.lower().split())
        
        intersection = words1.intersection(words2)
        union = words1.union(words2)
        
        if not union:
            return 0.0
        
        return len(intersection) / len(union)
    
    def _extract_domain(self, url: str) -> str:
        """Extract domain from URL"""
        from urllib.parse import urlparse
        return urlparse(url).netloc.lower()
    
    async def consolidate_memory(self):
        """Perform periodic memory consolidation"""
        if self.use_letta:
            try:
                logger.info("Starting memory consolidation...")
                # Send consolidation message to agent if needed
                logger.info("Memory consolidation completed")
            except Exception as e:
                logger.error(f"Memory consolidation failed: {e}")
        else:
            await self.fallback_system.consolidate_memory()
    
    async def export_memory(self, filepath: str):
        """Export agent memory to a file"""
        if self.use_letta:
            try:
                memory_export = {
                    "agent_id": self.agent_id,
                    "timestamp": datetime.now().isoformat(),
                    "memory_blocks": self.memory_blocks,
                    "system": "letta"
                }
                
                with open(filepath, 'w') as f:
                    json.dump(memory_export, f, indent=2, default=str)
                
                logger.info(f"Memory exported to {filepath}")
                
            except Exception as e:
                logger.error(f"Failed to export memory: {e}")
                await self.fallback_system.export_memory(filepath)
        else:
            await self.fallback_system.export_memory(filepath)
    
    async def cleanup(self):
        """Cleanup resources"""
        try:
            if self.use_letta and self.client:
                logger.info("Letta memory system cleanup completed")
            else:
                await self.fallback_system.cleanup()
        except Exception as e:
            logger.error(f"Error during memory cleanup: {e}")

# Global memory manager instance
memory_manager = MemoryManager()

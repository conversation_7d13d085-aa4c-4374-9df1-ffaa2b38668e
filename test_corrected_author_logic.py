#!/usr/bin/env python3
"""
Test Corrected Author Logic
Test the author extraction to ensure it ONLY uses enrichedUsers
"""

import json
from pathlib import Path
from enhanced_csv_exporter import EnhancedCSVExporter

def test_author_extraction():
    """Test the corrected author extraction logic."""
    print("🧪 TESTING CORRECTED AUTHOR LOGIC")
    print("=" * 50)
    
    exporter = EnhancedCSVExporter()
    
    # Test cases
    test_activities = [
        {
            "name": "Activity with enrichedUsers (should work)",
            "activity": {
                "enrichedUsers": [
                    {"fullName": "John Doe", "email": "<EMAIL>"}
                ],
                "properties": {
                    "entity_name": "ICICI Bank"  # This should be IGNORED
                }
            },
            "expected": ("<PERSON>", "<EMAIL>")
        },
        {
            "name": "Activity with enrichedUsers but no fullName",
            "activity": {
                "enrichedUsers": [
                    {"email": "<EMAIL>"}  # No fullName
                ],
                "properties": {}
            },
            "expected": ("", "<EMAIL>")
        },
        {
            "name": "Activity with empty enrichedUsers",
            "activity": {
                "enrichedUsers": [],
                "properties": {
                    "entity_name": "ICICI Bank",  # This should be IGNORED
                    "fullName": "Should Be Ignored"  # This should be IGNORED
                }
            },
            "expected": ("", "")
        },
        {
            "name": "Activity with NO enrichedUsers field",
            "activity": {
                "properties": {
                    "entity_name": "ICICI Bank",  # This should be IGNORED
                    "fullName": "Should Be Ignored",  # This should be IGNORED
                    "user_name": "Should Be Ignored"  # This should be IGNORED
                }
            },
            "expected": ("", "")
        },
        {
            "name": "Activity with enrichedUsers as non-list",
            "activity": {
                "enrichedUsers": "not a list",
                "properties": {
                    "entity_name": "ICICI Bank"  # This should be IGNORED
                }
            },
            "expected": ("", "")
        }
    ]
    
    print("📋 Testing author extraction (enrichedUsers ONLY):")
    
    all_passed = True
    
    for test_case in test_activities:
        result = exporter.extract_author_info(test_case["activity"])
        status = "✅" if result == test_case["expected"] else "❌"
        
        if result != test_case["expected"]:
            all_passed = False
        
        print(f"\n{status} {test_case['name']}")
        print(f"   Expected: {test_case['expected']}")
        print(f"   Got: {result}")
    
    return all_passed

def test_real_icici_data():
    """Test with real ICICI data to see author extraction results."""
    print(f"\n🧪 TESTING WITH REAL ICICI DATA")
    print("=" * 50)
    
    icici_file = "/Users/<USER>/Desktop/totango/ICICI_processed.json"
    
    if not Path(icici_file).exists():
        print(f"❌ File not found: {icici_file}")
        return False
    
    with open(icici_file, 'r') as f:
        activities = json.load(f)
    
    exporter = EnhancedCSVExporter()
    
    # Analyze all activities
    print(f"📊 Analyzing all {len(activities)} activities:")
    
    author_stats = {
        "with_enriched_users": 0,
        "without_enriched_users": 0,
        "blank_author_name": 0,
        "blank_author_email": 0,
        "both_blank": 0
    }
    
    sample_authors = []
    
    for i, activity in enumerate(activities):
        author_name, author_email = exporter.extract_author_info(activity)
        
        # Check if activity has enrichedUsers
        enriched_users = activity.get('enrichedUsers', [])
        if enriched_users and isinstance(enriched_users, list) and len(enriched_users) > 0:
            author_stats["with_enriched_users"] += 1
        else:
            author_stats["without_enriched_users"] += 1
        
        # Track blank fields
        if not author_name:
            author_stats["blank_author_name"] += 1
        if not author_email:
            author_stats["blank_author_email"] += 1
        if not author_name and not author_email:
            author_stats["both_blank"] += 1
        
        # Collect sample authors
        if author_name and len(sample_authors) < 10:
            sample_authors.append({
                "name": author_name,
                "email": author_email,
                "activity_index": i
            })
    
    print(f"\n📊 Author Extraction Results:")
    print(f"   • Activities with enrichedUsers: {author_stats['with_enriched_users']}")
    print(f"   • Activities without enrichedUsers: {author_stats['without_enriched_users']}")
    print(f"   • Blank author names: {author_stats['blank_author_name']}")
    print(f"   • Blank author emails: {author_stats['blank_author_email']}")
    print(f"   • Both name and email blank: {author_stats['both_blank']}")
    
    if sample_authors:
        print(f"\n📋 Sample Authors Found (from enrichedUsers):")
        for author in sample_authors:
            print(f"   • '{author['name']}' <{author['email']}> (Activity {author['activity_index'] + 1})")
    else:
        print(f"\n📋 No authors found with enrichedUsers data")
    
    # Verify the logic is working correctly
    expected_blank = author_stats["without_enriched_users"]
    actual_blank = author_stats["both_blank"]
    
    if expected_blank == actual_blank:
        print(f"\n✅ Logic working correctly:")
        print(f"   • {expected_blank} activities without enrichedUsers")
        print(f"   • {actual_blank} activities with blank author fields")
        print(f"   • Perfect match!")
        return True
    else:
        print(f"\n❌ Logic issue:")
        print(f"   • {expected_blank} activities without enrichedUsers")
        print(f"   • {actual_blank} activities with blank author fields")
        print(f"   • Should match!")
        return False

def show_csv_structure_changes():
    """Show what changed in the CSV structure."""
    print(f"\n📋 CSV STRUCTURE CHANGES")
    print("=" * 50)
    
    print("✅ CHANGES MADE:")
    print("   1. Author Name = fullName from enrichedUsers ONLY")
    print("   2. Author Email = email from enrichedUsers ONLY")
    print("   3. If no enrichedUsers → Author Name and Email are BLANK")
    print("   4. Removed Internal Attendees field completely")
    print("   5. No fallback to entity_name, ICICI Bank, or other fields")
    
    print("\n📊 EXPECTED RESULTS:")
    print("   • Most activities will have BLANK author fields")
    print("   • Only activities with enrichedUsers will have author data")
    print("   • No generic 'ICICI Bank' or 'ICICI User' names")
    print("   • CSV will have one less column (no Internal Attendees)")

def main():
    """Main test function."""
    print("🧪 TESTING CORRECTED AUTHOR AND ATTENDEES LOGIC")
    print("=" * 60)
    
    # Test 1: Author extraction logic
    logic_passed = test_author_extraction()
    
    # Test 2: Real data analysis
    real_data_passed = test_real_icici_data()
    
    # Show changes
    show_csv_structure_changes()
    
    print(f"\n🎯 SUMMARY:")
    if logic_passed and real_data_passed:
        print("✅ All tests passed!")
        print("✅ Author logic correctly uses enrichedUsers ONLY")
        print("✅ Blank fields when no enrichedUsers data")
        print("✅ Internal Attendees field removed")
        print("✅ Ready to generate corrected CSV files")
    else:
        print("❌ Some tests failed - check results above")
    
    print(f"\n🚀 NEXT STEP:")
    print("   Run: python3 generate_all_files_totango.py")
    print("   This will create corrected CSV files with:")
    print("   • Author fields from enrichedUsers ONLY")
    print("   • Blank authors when no enrichedUsers")
    print("   • No Internal Attendees field")

if __name__ == "__main__":
    main()

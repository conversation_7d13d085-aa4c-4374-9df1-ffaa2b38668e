#!/usr/bin/env python3
"""
ICICI to Gainsight Activity Converter - Enhanced Version
=======================================================

This script processes ICICI company activities from JSON format and maps:
1. meeting_type IDs to their display names using ID.json
2. touchpoint_tags IDs to their display names using Touchpoint_reason.JSON
3. Totango meeting types to Gainsight activity types

Enhanced to map Totango touchpoint types to proper Gainsight activity types:
- Email → Email
- Telephone Call → Call
- Web Meeting → Meeting
- Internal Note → Update
- In-Person Meeting → In-Person Meeting
- Gong Call → Gong Call
- Feedback → Feedback
- Inbound → Inbound
- Slack → Slack

Author: Ramprasad Somaraju
Date: January 29, 2025
"""

import json
import os
from typing import Dict, List, Any, Optional
from collections import defaultdict, Counter

class ICICIToGainsightConverter:
    def __init__(self, base_path: str = "/Users/<USER>/Desktop/totango"):
        """Initialize the converter with file paths."""
        self.base_path = base_path
        self.icici_file = os.path.join(base_path, "ICICI.json")
        self.id_mapping_file = os.path.join(base_path, "ID.json")
        self.touchpoint_mapping_file = os.path.join(base_path, "Touchpoint_reason.JSON")

        # Totango to Gainsight activity type mapping
        self.totango_to_gainsight_mapping = {
            # Default Gainsight Types
            "Email": "Email",
            "Telephone Call": "Call",
            "Web Meeting": "Meeting",
            "Internal Note": "Update",

            # Custom Gainsight Types (created manually)
            "In-Person Meeting": "In-Person Meeting",
            "Gong Call": "Gong Call",
            "Feedback": "Feedback",
            "Inbound": "Inbound",
            "Slack": "Slack",

            # Additional common variations
            "Phone Call": "Call",
            "Video Call": "Meeting",
            "Video Meeting": "Meeting",
            "Conference Call": "Meeting",
            "Note": "Update",
            "Internal": "Update",
            "Chat": "Slack",
            "Message": "Slack",
            "Survey": "Feedback",
            "Review": "Feedback",
            "Support": "Inbound",
            "Help": "Inbound"
        }

        # Storage for mappings
        self.meeting_type_mapping = {}
        self.touchpoint_tags_mapping = {}
        self.icici_activities = []

        # Statistics
        self.stats = {
            'total_activities': 0,
            'activities_with_meeting_type': 0,
            'activities_with_touchpoint_tags': 0,
            'unmapped_meeting_types': set(),
            'meeting_type_usage': Counter(),
            'touchpoint_tags_usage': Counter(),
            'gainsight_type_usage': Counter(),
            'totango_to_gainsight_mappings': Counter()
        }

    def load_mappings(self) -> None:
        """Load ID mappings from JSON files."""
        print("📋 Loading mapping files...")

        # Load meeting type mappings
        try:
            with open(self.id_mapping_file, 'r', encoding='utf-8') as f:
                id_data = json.load(f)
            self.meeting_type_mapping = {item['id']: item['display_name'] for item in id_data}
            print(f"✅ Loaded {len(self.meeting_type_mapping)} meeting type mappings")
        except FileNotFoundError:
            print(f"❌ Error: {self.id_mapping_file} not found")
            raise
        except json.JSONDecodeError as e:
            print(f"❌ Error parsing {self.id_mapping_file}: {e}")
            raise

        # Load touchpoint tags mappings
        try:
            with open(self.touchpoint_mapping_file, 'r', encoding='utf-8') as f:
                touchpoint_data = json.load(f)
            self.touchpoint_tags_mapping = {item['id']: item['display_name'] for item in touchpoint_data}
            print(f"✅ Loaded {len(self.touchpoint_tags_mapping)} touchpoint tag mappings")
        except FileNotFoundError:
            print(f"❌ Error: {self.touchpoint_mapping_file} not found")
            raise
        except json.JSONDecodeError as e:
            print(f"❌ Error parsing {self.touchpoint_mapping_file}: {e}")
            raise

    def load_icici_activities(self) -> None:
        """Load ICICI activities from JSON file."""
        print("📊 Loading ICICI activities...")

        try:
            with open(self.icici_file, 'r', encoding='utf-8') as f:
                self.icici_activities = json.load(f)
            self.stats['total_activities'] = len(self.icici_activities)
            print(f"✅ Loaded {self.stats['total_activities']} ICICI activities")
        except FileNotFoundError:
            print(f"❌ Error: {self.icici_file} not found")
            raise
        except json.JSONDecodeError as e:
            print(f"❌ Error parsing {self.icici_file}: {e}")
            raise

    def map_totango_to_gainsight_type(self, totango_type: str) -> str:
        """
        Map Totango meeting type to Gainsight activity type.

        Args:
            totango_type: The Totango meeting type name

        Returns:
            Corresponding Gainsight activity type
        """
        if not totango_type:
            return "Update"

        # Clean the input
        clean_type = totango_type.strip()

        # Direct mapping
        if clean_type in self.totango_to_gainsight_mapping:
            mapped_type = self.totango_to_gainsight_mapping[clean_type]
            self.stats['totango_to_gainsight_mappings'][f"{clean_type} → {mapped_type}"] += 1
            return mapped_type

        # Case-insensitive mapping
        for totango_key, gainsight_value in self.totango_to_gainsight_mapping.items():
            if clean_type.lower() == totango_key.lower():
                self.stats['totango_to_gainsight_mappings'][f"{clean_type} → {gainsight_value}"] += 1
                return gainsight_value

        # Partial matching for common patterns
        clean_lower = clean_type.lower()

        if "email" in clean_lower or "mail" in clean_lower:
            self.stats['totango_to_gainsight_mappings'][f"{clean_type} → Email (pattern)"] += 1
            return "Email"
        elif "call" in clean_lower or "phone" in clean_lower or "telephone" in clean_lower:
            self.stats['totango_to_gainsight_mappings'][f"{clean_type} → Call (pattern)"] += 1
            return "Call"
        elif "meeting" in clean_lower or "conference" in clean_lower or "video" in clean_lower:
            self.stats['totango_to_gainsight_mappings'][f"{clean_type} → Meeting (pattern)"] += 1
            return "Meeting"
        elif "slack" in clean_lower or "chat" in clean_lower or "message" in clean_lower:
            self.stats['totango_to_gainsight_mappings'][f"{clean_type} → Slack (pattern)"] += 1
            return "Slack"
        elif "feedback" in clean_lower or "survey" in clean_lower or "review" in clean_lower:
            self.stats['totango_to_gainsight_mappings'][f"{clean_type} → Feedback (pattern)"] += 1
            return "Feedback"
        elif "inbound" in clean_lower or "support" in clean_lower or "help" in clean_lower:
            self.stats['totango_to_gainsight_mappings'][f"{clean_type} → Inbound (pattern)"] += 1
            return "Inbound"
        elif "gong" in clean_lower:
            self.stats['totango_to_gainsight_mappings'][f"{clean_type} → Gong Call (pattern)"] += 1
            return "Gong Call"
        elif "person" in clean_lower and "meeting" in clean_lower:
            self.stats['totango_to_gainsight_mappings'][f"{clean_type} → In-Person Meeting (pattern)"] += 1
            return "In-Person Meeting"

        # Default fallback
        self.stats['totango_to_gainsight_mappings'][f"{clean_type} → Update (default)"] += 1
        return "Update"

    def map_meeting_type(self, meeting_type_id: str) -> Optional[str]:
        """Map meeting type ID to display name."""
        if meeting_type_id in self.meeting_type_mapping:
            mapped_name = self.meeting_type_mapping[meeting_type_id]
            self.stats['meeting_type_usage'][mapped_name] += 1
            return mapped_name
        else:
            self.stats['unmapped_meeting_types'].add(meeting_type_id)
            return None

    def map_touchpoint_tags(self, touchpoint_tag_ids: List[str]) -> List[str]:
        """Map touchpoint tag IDs to display names."""
        mapped_names = []

        for tag_id in touchpoint_tag_ids:
            if tag_id in self.touchpoint_tags_mapping:
                mapped_name = self.touchpoint_tags_mapping[tag_id]
                mapped_names.append(mapped_name)
                self.stats['touchpoint_tags_usage'][mapped_name] += 1
            else:
                mapped_names.append("Internal Note")
                self.stats['touchpoint_tags_usage']["Internal Note"] += 1

        return mapped_names

    def process_activities(self) -> List[Dict[str, Any]]:
        """Process all activities and apply mappings."""
        print("🔄 Processing activities and applying mappings...")

        processed_activities = []

        for activity in self.icici_activities:
            processed_activity = activity.copy()

            # Check if activity has properties
            if 'properties' in activity and activity['properties']:
                properties = activity['properties'].copy()

                # Process meeting_type
                if 'meeting_type' in properties:
                    self.stats['activities_with_meeting_type'] += 1
                    meeting_type_id = properties['meeting_type']

                    # Add mapped meeting type name
                    mapped_name = self.map_meeting_type(meeting_type_id)
                    totango_meeting_type = mapped_name if mapped_name else f"UNMAPPED_{meeting_type_id}"

                    # Map to Gainsight activity type
                    gainsight_activity_type = self.map_totango_to_gainsight_type(totango_meeting_type)

                    # Store all mapping information
                    properties['meeting_type_id'] = meeting_type_id
                    properties['meeting_type_name'] = totango_meeting_type  # Original Totango type
                    properties['gainsight_activity_type'] = gainsight_activity_type  # Mapped Gainsight type

                    # Track Gainsight type usage
                    self.stats['gainsight_type_usage'][gainsight_activity_type] += 1

                # Process touchpoint_tags
                if 'touchpoint_tags' in properties:
                    self.stats['activities_with_touchpoint_tags'] += 1
                    touchpoint_tags = properties['touchpoint_tags']

                    # Handle both single ID and array of IDs
                    if isinstance(touchpoint_tags, str):
                        touchpoint_tags = [touchpoint_tags]
                    elif not isinstance(touchpoint_tags, list):
                        touchpoint_tags = []

                    # Map touchpoint tags
                    mapped_names = self.map_touchpoint_tags(touchpoint_tags)

                    # Store both original IDs and mapped names
                    properties['touchpoint_tags_ids'] = touchpoint_tags
                    properties['touchpoint_tags_names'] = mapped_names

                processed_activity['properties'] = properties

            processed_activities.append(processed_activity)

        return processed_activities

    def generate_summary_report(self) -> str:
        """Generate a comprehensive summary report."""
        report = []
        report.append("=" * 70)
        report.append("ICICI TO GAINSIGHT ACTIVITY CONVERSION SUMMARY")
        report.append("=" * 70)
        report.append(f"📊 Total Activities: {self.stats['total_activities']:,}")
        report.append(f"🎯 Activities with meeting_type: {self.stats['activities_with_meeting_type']:,}")
        report.append(f"🏷️  Activities with touchpoint_tags: {self.stats['activities_with_touchpoint_tags']:,}")
        report.append("")

        # Meeting Type Analysis
        report.append("📋 TOTANGO MEETING TYPE ANALYSIS")
        report.append("-" * 35)
        if self.stats['meeting_type_usage']:
            report.append("✅ Successfully Mapped Totango Meeting Types:")
            for name, count in self.stats['meeting_type_usage'].most_common():
                report.append(f"   • {name}: {count:,} activities")

        if self.stats['unmapped_meeting_types']:
            report.append(f"\n❌ Unmapped Meeting Type IDs ({len(self.stats['unmapped_meeting_types'])}):")
            for unmapped_id in sorted(self.stats['unmapped_meeting_types']):
                report.append(f"   • {unmapped_id}")
        report.append("")

        # Gainsight Activity Type Analysis
        report.append("🎯 GAINSIGHT ACTIVITY TYPE DISTRIBUTION")
        report.append("-" * 40)
        if self.stats['gainsight_type_usage']:
            report.append("✅ Mapped Gainsight Activity Types:")
            for gainsight_type, count in self.stats['gainsight_type_usage'].most_common():
                percentage = (count / self.stats['activities_with_meeting_type']) * 100 if self.stats['activities_with_meeting_type'] > 0 else 0
                report.append(f"   • {gainsight_type}: {count:,} activities ({percentage:.1f}%)")
        report.append("")

        # Mapping Details
        report.append("🔄 TOTANGO → GAINSIGHT MAPPING DETAILS")
        report.append("-" * 40)
        if self.stats['totango_to_gainsight_mappings']:
            report.append("✅ Applied Mappings:")
            for mapping, count in self.stats['totango_to_gainsight_mappings'].most_common():
                report.append(f"   • {mapping}: {count:,} times")
        report.append("")

        # Touchpoint Tags Analysis
        report.append("🏷️  TOUCHPOINT TAGS ANALYSIS")
        report.append("-" * 35)
        if self.stats['touchpoint_tags_usage']:
            report.append("✅ Mapped Touchpoint Tags:")
            for name, count in self.stats['touchpoint_tags_usage'].most_common():
                report.append(f"   • {name}: {count:,} activities")
        report.append("")

        # Solutions for Unmapped IDs
        if self.stats['unmapped_meeting_types']:
            report.append("💡 SOLUTIONS FOR UNMAPPED MEETING TYPE IDs")
            report.append("-" * 40)
            report.append("1. Check if these IDs exist in newer versions of mapping files")
            report.append("2. Contact data source administrator for missing mappings")
            report.append("3. Create manual mappings for critical unmapped IDs")
            report.append("4. Use 'UNMAPPED_{ID}' as fallback display names")
            report.append("")

        # Gainsight Migration Readiness
        report.append("🚀 GAINSIGHT MIGRATION READINESS")
        report.append("-" * 35)
        report.append("✅ All activities now have 'gainsight_activity_type' field")
        report.append("✅ Meeting types mapped according to Gainsight standards:")
        report.append("   • Email → Email")
        report.append("   • Telephone Call → Call")
        report.append("   • Web Meeting → Meeting")
        report.append("   • Internal Note → Update")
        report.append("   • Custom types preserved (In-Person Meeting, Gong Call, etc.)")

        report.append("=" * 70)
        return "\n".join(report)

    def save_processed_data(self, processed_activities: List[Dict[str, Any]], output_file: str = None) -> str:
        """Save processed activities to JSON file."""
        if output_file is None:
            output_file = os.path.join(self.base_path, "ICICI_gainsight_ready.json")

        try:
            with open(output_file, 'w', encoding='utf-8') as f:
                json.dump(processed_activities, f, indent=2, ensure_ascii=False)
            print(f"💾 Gainsight-ready data saved to: {output_file}")
            return output_file
        except Exception as e:
            print(f"❌ Error saving processed data: {e}")
            raise

    def run_conversion(self) -> None:
        """Run the complete conversion process."""
        print("🚀 Starting ICICI to Gainsight Activity Conversion Process")
        print("=" * 60)

        try:
            # Step 1: Load all data
            self.load_mappings()
            self.load_icici_activities()

            # Step 2: Process activities
            processed_activities = self.process_activities()

            # Step 3: Generate and display summary
            summary = self.generate_summary_report()
            print(summary)

            # Step 4: Save processed data
            output_file = self.save_processed_data(processed_activities)

            print(f"\n🎉 Conversion completed successfully!")
            print(f"📄 Summary report displayed above")
            print(f"💾 Gainsight-ready data: {output_file}")
            print(f"\n🔍 Key fields added to each activity:")
            print(f"   • meeting_type_name: Original Totango meeting type")
            print(f"   • gainsight_activity_type: Mapped Gainsight activity type")
            print(f"   • touchpoint_tags_names: Mapped touchpoint tag names")

        except Exception as e:
            print(f"💥 Conversion failed: {e}")
            raise

def main():
    """Main function to run the converter."""
    converter = ICICIToGainsightConverter()
    converter.run_conversion()

if __name__ == "__main__":
    main()

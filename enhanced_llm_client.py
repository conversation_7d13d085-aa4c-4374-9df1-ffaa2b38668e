#!/usr/bin/env python3
"""
Enhanced Multi-Provider LLM Client
Supports automatic fallback between free models from OpenRouter, HuggingFace, and other providers
"""

import asyncio
import json
import logging
import time
from typing import Dict, List, Optional, Any, Tuple
from dataclasses import dataclass
import httpx
from config import config

logger = logging.getLogger(__name__)

@dataclass
class LLMResponse:
    """Standardized LLM response"""
    content: str
    model: str
    provider: str
    usage: Dict[str, Any]
    latency: float
    success: bool
    error: Optional[str] = None

class EnhancedLLMClient:
    """
    Enhanced LLM client with multi-provider support and intelligent fallback
    Optimized for free models from OpenRouter
    """
    
    def __init__(self):
        self.config = config
        self.session = httpx.AsyncClient(timeout=120.0)
        self.model_performance_cache = {}
        self.failure_counts = {}
        
    async def __aenter__(self):
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        await self.session.aclose()
    
    async def complete(self, 
                      prompt: str, 
                      task_type: str = "general",
                      max_tokens: int = 4000,
                      temperature: float = 0.1,
                      **kwargs) -> LLMResponse:
        """
        Complete a prompt using the best available model for the task
        """
        start_time = time.time()
        
        # Get best model for task
        primary_model = self.config.get_model_for_task(task_type)
        fallback_models = self.config.get_fallback_models(task_type)
        
        # Try primary model first
        models_to_try = [primary_model] + fallback_models
        
        for model in models_to_try:
            try:
                logger.info(f"Attempting completion with model: {model}")
                
                # Get API config for this model
                api_config = self.config.get_api_config_for_model(model)
                
                # Make request based on provider
                if api_config["provider"] == "openrouter":
                    response = await self._call_openrouter(
                        model, prompt, max_tokens, temperature, **kwargs
                    )
                elif api_config["provider"] == "huggingface":
                    response = await self._call_huggingface(
                        model, prompt, max_tokens, temperature, **kwargs
                    )
                elif api_config["provider"] == "openai":
                    response = await self._call_openai(
                        model, prompt, max_tokens, temperature, **kwargs
                    )
                elif api_config["provider"] == "anthropic":
                    response = await self._call_anthropic(
                        model, prompt, max_tokens, temperature, **kwargs
                    )
                elif api_config["provider"] == "google":
                    response = await self._call_google(
                        model, prompt, max_tokens, temperature, **kwargs
                    )
                else:
                    # Default to OpenRouter for unknown providers
                    response = await self._call_openrouter(
                        model, prompt, max_tokens, temperature, **kwargs
                    )
                
                # Success - update performance tracking
                latency = time.time() - start_time
                self._update_performance_cache(model, True, latency)
                
                return LLMResponse(
                    content=response["content"],
                    model=model,
                    provider=api_config["provider"],
                    usage=response.get("usage", {}),
                    latency=latency,
                    success=True
                )
                
            except Exception as e:
                logger.warning(f"Model {model} failed: {str(e)}")
                self._update_failure_count(model)
                
                # If this is the last model, return error
                if model == models_to_try[-1]:
                    return LLMResponse(
                        content="",
                        model=model,
                        provider=api_config.get("provider", "unknown"),
                        usage={},
                        latency=time.time() - start_time,
                        success=False,
                        error=str(e)
                    )
                
                continue
    
    async def _call_openrouter(self, model: str, prompt: str, max_tokens: int, 
                              temperature: float, **kwargs) -> Dict[str, Any]:
        """Call OpenRouter API (handles free models)"""
        headers = {
            "Authorization": f"Bearer {self.config.llm.openrouter_api_key}",
            "Content-Type": "application/json",
            "HTTP-Referer": "https://github.com/wildweasel/browser-automation",
            "X-Title": "Browser Automation Agent"
        }
        
        payload = {
            "model": model,
            "messages": [{"role": "user", "content": prompt}],
            "max_tokens": max_tokens,
            "temperature": temperature,
            **kwargs
        }
        
        response = await self.session.post(
            f"{self.config.llm.openrouter_base_url}/chat/completions",
            headers=headers,
            json=payload
        )
        
        if response.status_code != 200:
            raise Exception(f"OpenRouter API error: {response.status_code} - {response.text}")
        
        data = response.json()
        
        if "error" in data:
            raise Exception(f"OpenRouter error: {data['error']}")
        
        return {
            "content": data["choices"][0]["message"]["content"],
            "usage": data.get("usage", {})
        }
    
    async def _call_huggingface(self, model: str, prompt: str, max_tokens: int,
                               temperature: float, **kwargs) -> Dict[str, Any]:
        """Call HuggingFace Inference API"""
        headers = {
            "Authorization": f"Bearer {self.config.llm.huggingface_api_key}",
            "Content-Type": "application/json"
        }
        
        payload = {
            "inputs": prompt,
            "parameters": {
                "max_new_tokens": max_tokens,
                "temperature": temperature,
                "return_full_text": False,
                **kwargs
            }
        }
        
        # Clean model name for HuggingFace
        hf_model = model.replace(":free", "")
        
        response = await self.session.post(
            f"{self.config.llm.huggingface_base_url}/{hf_model}",
            headers=headers,
            json=payload
        )
        
        if response.status_code != 200:
            raise Exception(f"HuggingFace API error: {response.status_code} - {response.text}")
        
        data = response.json()
        
        if isinstance(data, list) and len(data) > 0:
            return {
                "content": data[0].get("generated_text", ""),
                "usage": {"total_tokens": len(prompt.split()) + max_tokens}
            }
        else:
            raise Exception(f"Unexpected HuggingFace response: {data}")
    
    async def _call_openai(self, model: str, prompt: str, max_tokens: int,
                          temperature: float, **kwargs) -> Dict[str, Any]:
        """Call OpenAI API"""
        headers = {
            "Authorization": f"Bearer {self.config.llm.openai_api_key}",
            "Content-Type": "application/json"
        }
        
        payload = {
            "model": model,
            "messages": [{"role": "user", "content": prompt}],
            "max_tokens": max_tokens,
            "temperature": temperature,
            **kwargs
        }
        
        response = await self.session.post(
            "https://api.openai.com/v1/chat/completions",
            headers=headers,
            json=payload
        )
        
        if response.status_code != 200:
            raise Exception(f"OpenAI API error: {response.status_code} - {response.text}")
        
        data = response.json()
        return {
            "content": data["choices"][0]["message"]["content"],
            "usage": data.get("usage", {})
        }
    
    async def _call_anthropic(self, model: str, prompt: str, max_tokens: int,
                             temperature: float, **kwargs) -> Dict[str, Any]:
        """Call Anthropic API"""
        headers = {
            "x-api-key": self.config.llm.anthropic_api_key,
            "Content-Type": "application/json",
            "anthropic-version": "2023-06-01"
        }
        
        payload = {
            "model": model,
            "max_tokens": max_tokens,
            "temperature": temperature,
            "messages": [{"role": "user", "content": prompt}],
            **kwargs
        }
        
        response = await self.session.post(
            "https://api.anthropic.com/v1/messages",
            headers=headers,
            json=payload
        )
        
        if response.status_code != 200:
            raise Exception(f"Anthropic API error: {response.status_code} - {response.text}")
        
        data = response.json()
        return {
            "content": data["content"][0]["text"],
            "usage": data.get("usage", {})
        }
    
    async def _call_google(self, model: str, prompt: str, max_tokens: int,
                          temperature: float, **kwargs) -> Dict[str, Any]:
        """Call Google/Gemini API"""
        headers = {
            "Content-Type": "application/json"
        }
        
        # Clean model name for Google API
        clean_model = model.replace("google/", "").replace(":free", "")
        
        payload = {
            "contents": [{"parts": [{"text": prompt}]}],
            "generationConfig": {
                "maxOutputTokens": max_tokens,
                "temperature": temperature,
                **kwargs
            }
        }
        
        # Use the v1beta API for Gemini models
        api_url = f"https://generativelanguage.googleapis.com/v1beta/models/{clean_model}:generateContent"
        
        response = await self.session.post(
            f"{api_url}?key={self.config.llm.google_api_key}",
            headers=headers,
            json=payload
        )
        
        if response.status_code != 200:
            raise Exception(f"Google API error: {response.status_code} - {response.text}")
        
        data = response.json()
        
        if "candidates" not in data or not data["candidates"]:
            raise Exception(f"No response from Google API: {data}")
        
        content = data["candidates"][0]["content"]["parts"][0]["text"]
        usage = data.get("usageMetadata", {})
        
        return {
            "content": content,
            "usage": {
                "prompt_tokens": usage.get("promptTokenCount", 0),
                "completion_tokens": usage.get("candidatesTokenCount", 0),
                "total_tokens": usage.get("totalTokenCount", 0)
            }
        }
    
    def _update_performance_cache(self, model: str, success: bool, latency: float):
        """Update performance tracking for model selection optimization"""
        if model not in self.model_performance_cache:
            self.model_performance_cache[model] = {
                "success_count": 0,
                "total_requests": 0,
                "avg_latency": 0,
                "last_success": None
            }
        
        cache = self.model_performance_cache[model]
        cache["total_requests"] += 1
        
        if success:
            cache["success_count"] += 1
            cache["last_success"] = time.time()
            # Update rolling average latency
            cache["avg_latency"] = (cache["avg_latency"] + latency) / 2
    
    def _update_failure_count(self, model: str):
        """Track failure counts for model reliability"""
        if model not in self.failure_counts:
            self.failure_counts[model] = 0
        self.failure_counts[model] += 1
    
    def get_best_model_for_task(self, task_type: str) -> str:
        """Get the best performing model for a task based on historical data"""
        primary_model = self.config.get_model_for_task(task_type)
        fallback_models = self.config.get_fallback_models(task_type)
        
        all_models = [primary_model] + fallback_models
        
        # Score models based on performance
        scored_models = []
        for model in all_models:
            score = self._calculate_model_score(model)
            scored_models.append((model, score))
        
        # Sort by score (higher is better)
        scored_models.sort(key=lambda x: x[1], reverse=True)
        
        return scored_models[0][0] if scored_models else primary_model
    
    def _calculate_model_score(self, model: str) -> float:
        """Calculate a performance score for a model"""
        if model not in self.model_performance_cache:
            return 0.5  # Neutral score for unknown models
        
        cache = self.model_performance_cache[model]
        
        if cache["total_requests"] == 0:
            return 0.5
        
        # Success rate (0-1)
        success_rate = cache["success_count"] / cache["total_requests"]
        
        # Latency factor (lower latency = higher score)
        latency_factor = max(0, 1 - (cache["avg_latency"] / 10))  # Normalize to 10s
        
        # Recent failure penalty
        failure_penalty = self.failure_counts.get(model, 0) * 0.1
        
        score = (success_rate * 0.6) + (latency_factor * 0.3) - (failure_penalty * 0.1)
        return max(0, min(1, score))

# Test function
async def test_llm_client():
    """Test the enhanced LLM client with different task types"""
    print("🧪 Testing Enhanced LLM Client")
    print("=" * 50)
    
    async with EnhancedLLMClient() as client:
        
        # Test coding task
        print("\n🔧 Testing coding task...")
        coding_response = await client.complete(
            "Write a Python function to reverse a string",
            task_type="coding",
            max_tokens=500
        )
        print(f"Model: {coding_response.model}")
        print(f"Provider: {coding_response.provider}")
        print(f"Success: {coding_response.success}")
        print(f"Latency: {coding_response.latency:.2f}s")
        if coding_response.success:
            print(f"Response: {coding_response.content[:200]}...")
        else:
            print(f"Error: {coding_response.error}")
        
        # Test reasoning task
        print("\n🧠 Testing reasoning task...")
        reasoning_response = await client.complete(
            "Explain the difference between supervised and unsupervised learning",
            task_type="reasoning",
            max_tokens=300
        )
        print(f"Model: {reasoning_response.model}")
        print(f"Provider: {reasoning_response.provider}")
        print(f"Success: {reasoning_response.success}")
        print(f"Latency: {reasoning_response.latency:.2f}s")
        
        # Test natural language task
        print("\n💬 Testing natural language task...")
        nl_response = await client.complete(
            "Summarize the key benefits of using free open-source LLMs",
            task_type="natural_language",
            max_tokens=200
        )
        print(f"Model: {nl_response.model}")
        print(f"Provider: {nl_response.provider}")
        print(f"Success: {nl_response.success}")
        print(f"Latency: {nl_response.latency:.2f}s")
        
        print(f"\n✅ LLM Client test completed!")

if __name__ == "__main__":
    asyncio.run(test_llm_client())

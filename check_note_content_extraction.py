#!/usr/bin/env python3
"""
Check Note Content Extraction
Verify if the converter is properly extracting note_content.text field
"""

import json
from pathlib import Path

def check_note_content_extraction():
    """Check if note_content.text is being extracted properly."""
    print("🔍 CHECKING NOTE_CONTENT EXTRACTION")
    print("=" * 50)
    
    # Load original ICICI data
    icici_file = "/Users/<USER>/Desktop/totango/ICICI.json"
    with open(icici_file, 'r') as f:
        activities = json.load(f)
    
    print(f"📊 Total activities: {len(activities)}")
    
    # Check for note_content field
    activities_with_note_content = 0
    note_content_examples = []
    
    print(f"\n📋 CHECKING NOTE_CONTENT FIELD:")
    print("-" * 40)
    
    for i, activity in enumerate(activities):
        properties = activity.get('properties', {})
        
        if 'note_content' in properties:
            activities_with_note_content += 1
            note_content = properties['note_content']
            
            # Collect examples
            if len(note_content_examples) < 5:
                note_content_examples.append({
                    'activity_index': i,
                    'activity_id': activity.get('id', 'N/A'),
                    'activity_type': activity.get('type', 'N/A'),
                    'note_content': note_content
                })
    
    print(f"📊 Activities with note_content: {activities_with_note_content}")
    print(f"📊 Activities without note_content: {len(activities) - activities_with_note_content}")
    
    if note_content_examples:
        print(f"\n📋 NOTE_CONTENT EXAMPLES:")
        print("-" * 30)
        
        for example in note_content_examples:
            print(f"\n   Activity {example['activity_index'] + 1}:")
            print(f"     ID: {example['activity_id']}")
            print(f"     Type: {example['activity_type']}")
            print(f"     note_content structure: {type(example['note_content'])}")
            
            note_content = example['note_content']
            if isinstance(note_content, dict):
                print(f"     note_content keys: {list(note_content.keys())}")
                
                if 'text' in note_content:
                    text_content = note_content['text']
                    print(f"     text field type: {type(text_content)}")
                    if isinstance(text_content, str):
                        preview = text_content[:100] + "..." if len(text_content) > 100 else text_content
                        print(f"     text preview: '{preview}'")
                    else:
                        print(f"     text content: {text_content}")
                else:
                    print(f"     ❌ No 'text' field in note_content")
            else:
                print(f"     note_content value: {note_content}")
    
    # Check what the current converter generates
    print(f"\n🔄 CHECKING CURRENT CONVERTER OUTPUT:")
    print("-" * 40)
    
    # Import the converter
    import sys
    sys.path.append('.')
    from complete_standalone_converter import CompleteStandaloneConverter
    
    # Create converter instance
    converter = CompleteStandaloneConverter(
        icici_file="/Users/<USER>/Desktop/totango/ICICI.json",
        flowtype_file="/Users/<USER>/Desktop/totango/flowtype.json",
        touchpoint_file="/Users/<USER>/Desktop/totango/Touchpoint_reason.JSON",
        id_file="/Users/<USER>/Desktop/totango/ID.json",
        output_dir="/tmp"
    )
    
    # Test content generation for activities with note_content
    if note_content_examples:
        print(f"Testing content generation for activities with note_content:")
        
        for example in note_content_examples[:3]:  # Test first 3
            activity_index = example['activity_index']
            activity = activities[activity_index]
            
            # Generate content using current method
            generated_html = converter.generate_content(activity)
            generated_plain = converter.strip_html(generated_html)
            
            print(f"\n   Activity {activity_index + 1}:")
            print(f"     Original note_content.text:")
            note_content = activity['properties'].get('note_content', {})
            if isinstance(note_content, dict) and 'text' in note_content:
                original_text = note_content['text']
                preview = original_text[:100] + "..." if len(original_text) > 100 else original_text
                print(f"       '{preview}'")
            else:
                print(f"       No text field found")
            
            print(f"     Generated HTML:")
            html_preview = generated_html[:100] + "..." if len(generated_html) > 100 else generated_html
            print(f"       {html_preview}")
            
            print(f"     Generated Plain Text:")
            plain_preview = generated_plain[:100] + "..." if len(generated_plain) > 100 else generated_plain
            print(f"       '{plain_preview}'")
            
            # Check if original text is being used
            if isinstance(note_content, dict) and 'text' in note_content:
                original_text = note_content['text']
                if original_text in generated_html or original_text in generated_plain:
                    print(f"     ✅ Original note_content.text IS being used")
                else:
                    print(f"     ❌ Original note_content.text is NOT being used")
            else:
                print(f"     ⚠️  No note_content.text to check")

def check_other_content_fields():
    """Check what other content fields exist in the data."""
    print(f"\n🔍 CHECKING OTHER CONTENT FIELDS:")
    print("-" * 40)
    
    icici_file = "/Users/<USER>/Desktop/totango/ICICI.json"
    with open(icici_file, 'r') as f:
        activities = json.load(f)
    
    content_fields = {}
    
    # Check first 100 activities for content-related fields
    for activity in activities[:100]:
        properties = activity.get('properties', {})
        
        for key in properties.keys():
            if any(word in key.lower() for word in ['content', 'text', 'description', 'note', 'message', 'body']):
                if key not in content_fields:
                    content_fields[key] = 0
                content_fields[key] += 1
    
    print(f"Content-related fields found (in first 100 activities):")
    for field, count in sorted(content_fields.items()):
        print(f"   • {field}: {count} activities")

def main():
    """Main function."""
    check_note_content_extraction()
    check_other_content_fields()
    
    print(f"\n🎯 SUMMARY:")
    print("✅ Check if note_content.text exists in your data")
    print("✅ Verify if current converter uses note_content.text")
    print("✅ Identify what content fields are available")
    print("✅ Ensure accurate data extraction for Gainsight")

if __name__ == "__main__":
    main()

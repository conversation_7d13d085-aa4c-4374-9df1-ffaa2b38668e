#!/usr/bin/env python3
"""
🐺 Wild Weasel UI Automation - CORRECTED VERSION
=================================================
Pure UI automation following exact user requirements with proper selectors
NO API CALLS - PURE UI INTERACTION ONLY
"""

import csv
import os
import time
import logging
from datetime import datetime
from playwright.sync_api import sync_playwright

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler("corrected_ui_automation.log"),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger("WildWeasel-Corrected")

class CorrectedUIAutomation:
    """Corrected UI automation following exact user requirements"""

    def __init__(self):
        self.config = {
            "csv_file": "./ICICI_processed_gainsight_mapped_enhanced_demo.csv",
            "target_url": "https://demo-emea1.gainsightcloud.com/v1/ui/customersuccess360?cid=1P02IPMAEL4M3CQGY4K5FHU22D2HHT11KCKU#/8fd79692-8e98-4543-b2ec-bc0dba3776ca"
        }
        self.credentials = {
            "username": "<EMAIL>",
            "password": "@Ramprasad826ie"
        }
        self.activities = []

    def load_csv_activities(self):
        """Load activities from CSV file"""
        try:
            if not os.path.exists(self.config['csv_file']):
                logger.error(f"❌ CSV file not found: {self.config['csv_file']}")
                return False

            self.activities = []
            with open(self.config['csv_file'], 'r', encoding='utf-8') as f:
                csv_reader = csv.DictReader(f)
                for row in csv_reader:
                    activity = {
                        "subject": row.get('Subject', '').strip(),
                        "activity_date": row.get('Activity Date', '').strip(),
                        "activity_type": row.get('Activity Type', '').strip(),
                        "plain_text": row.get('Plain Text', '').strip(),
                        "author_name": row.get('Author Name', '').strip(),
                        "flow_type": row.get('Flow Type', '').strip(),
                        "touchpoint_reason": row.get('Touchpoint Reason', '').strip()
                    }
                    self.activities.append(activity)

            logger.info(f"📊 Loaded {len(self.activities)} activities from CSV")
            return True

        except Exception as e:
            logger.error(f"❌ Failed to load CSV activities: {e}")
            return False

    def convert_date_format(self, date_string):
        """Convert date from '2025-05-29 03:30:19' to '5/29/2025' and '03:30'"""
        try:
            if not date_string or date_string.strip() == '':
                return None, None

            # Parse the datetime string
            dt = datetime.strptime(date_string.strip(), '%Y-%m-%d %H:%M:%S')

            # Format for Gainsight: M/D/YYYY (no leading zeros)
            gainsight_date = f"{dt.month}/{dt.day}/{dt.year}"

            # Format time as HH:MM (no seconds)
            gainsight_time = f"{dt.hour:02d}:{dt.minute:02d}"

            return gainsight_date, gainsight_time

        except Exception as e:
            logger.error(f"❌ Date conversion failed for '{date_string}': {e}")
            return None, None

    def create_activity_from_csv_row(self, page, activity_data, row_index):
        """Create a single activity using CSV row data - PURE UI AUTOMATION"""
        try:
            subject = activity_data["subject"]
            logger.info(f"📝 Creating activity {row_index}: {subject[:50]}...")

            # Step 1: Click the create button (top right dropdown trigger) - EXACT SELECTOR
            try:
                page.wait_for_selector('gs-cs360-header .gs-cs360-headeritem2 button', timeout=10000)
                page.click('gs-cs360-header .gs-cs360-headeritem2 button')
                time.sleep(3)
                logger.info("✅ Create dropdown button clicked")
            except Exception as e:
                logger.error(f"❌ Failed to click create dropdown button: {e}")
                return False

            # Step 2: Click "Activity" from dropdown - FIRST OPTION
            try:
                page.wait_for_selector('text=Activity', timeout=10000)
                page.click('text=Activity')
                time.sleep(3)  # Wait for modal to load
                logger.info("✅ Activity option clicked from dropdown")
            except Exception as e:
                logger.error(f"❌ Failed to click Activity option: {e}")
                return False

            # Step 3: Select Activity Type (if not empty)
            activity_type = activity_data["activity_type"]
            if activity_type and activity_type.strip():
                try:
                    # Click Activity Type dropdown
                    page.wait_for_selector('text="Activity Type"', timeout=10000)
                    # Find the dropdown arrow next to Activity Type
                    page.click('text="Activity Type" >> .. >> nz-select')
                    time.sleep(2)
                    logger.info("✅ Activity Type dropdown opened")

                    # Select the matching option
                    page.wait_for_selector(f'text="{activity_type}"', timeout=10000)
                    page.click(f'text="{activity_type}"')
                    time.sleep(1)
                    logger.info(f"✅ Activity Type '{activity_type}' selected")

                except Exception as e:
                    logger.error(f"❌ Failed to select Activity Type '{activity_type}': {e}")

            # Step 4: Fill Subject field - EXACT SELECTOR
            if subject and subject.strip():
                try:
                    subject_selector = '#cdk-overlay-12 > gs-composer > div.gs-timeline-composer.cdk-drag.ng-star-inserted > div.gs-activity-composer > form > div.gs-activity-composersection > div.gs-activity-composerright.ng-star-inserted > gs-fields > div > form > div > div:nth-child(3) > gs-text > nz-form-item > nz-form-control > div > span > input'
                    page.wait_for_selector(subject_selector, timeout=10000)
                    page.fill(subject_selector, subject)
                    logger.info(f"✅ Subject filled: {subject[:30]}...")
                except Exception as e:
                    logger.error(f"❌ Failed to fill Subject: {e}")

            # Step 5: Fill Activity Date - EXACT SELECTOR
            gainsight_date, gainsight_time = self.convert_date_format(activity_data["activity_date"])
            if gainsight_date:
                try:
                    date_selector = '#cdk-overlay-15 > div > date-range-popup > div > div > div > calendar-input > div > div > input'
                    page.wait_for_selector(date_selector, timeout=10000)
                    page.fill(date_selector, gainsight_date)
                    logger.info(f"✅ Activity Date filled: {gainsight_date}")
                except Exception as e:
                    logger.error(f"❌ Failed to fill Activity Date: {e}")

            # Step 6: Fill Time field - EXACT SELECTOR
            if gainsight_time:
                try:
                    time_selector = '#cdk-overlay-12 > gs-composer > div.gs-timeline-composer.cdk-drag.ng-star-inserted > div.gs-activity-composer > form > div.gs-activity-composersection > div.gs-activity-composerright.ng-star-inserted > gs-fields > div > form > div > div:nth-child(4) > gs-datetime-field > nz-form-item:nth-child(1) > div > nz-form-control.timepicker-error.ng-tns-c56-175.ant-form-item-control-wrapper.ant-col.ng-invalid.ng-untouched.ng-pristine > div > span > nz-time-picker > input'
                    page.wait_for_selector(time_selector, timeout=10000)
                    page.fill(time_selector, gainsight_time)
                    logger.info(f"✅ Time filled: {gainsight_time}")
                except Exception as e:
                    logger.error(f"❌ Failed to fill Time: {e}")

            # Step 7: Fill Notes/Plain Text - EXACT SELECTOR
            plain_text = activity_data["plain_text"]
            if plain_text and plain_text.strip():
                try:
                    page.wait_for_selector('#editor > div', timeout=10000)
                    page.click('#editor > div')
                    time.sleep(1)
                    page.keyboard.type(plain_text)
                    logger.info("✅ Notes field filled")
                except Exception as e:
                    logger.error(f"❌ Failed to fill Notes: {e}")

            # Step 8: Fill Internal Recipients (Author Name) - EXACT PROCESS
            author_name = activity_data["author_name"]
            if author_name and author_name.strip():
                try:
                    # Click on the "Search Users" input to open the dropdown
                    page.wait_for_selector('input[placeholder="Search Users"]', timeout=10000)
                    page.click('input[placeholder="Search Users"]')
                    time.sleep(1)

                    # Wait for the overlay input to appear and type the name
                    page.wait_for_selector('#cdk-overlay-17 input', timeout=5000)
                    # Use first name only for better matching
                    first_name = author_name.split()[0] if author_name.split() else author_name
                    page.fill('#cdk-overlay-17 input', first_name)
                    time.sleep(2)  # Wait for results

                    # Try to click the exact user from dropdown
                    try:
                        page.wait_for_selector(f'text={author_name}', timeout=5000)
                        page.click(f'text={author_name}')
                        logger.info(f"✅ Internal recipient '{author_name}' selected")
                    except:
                        # Fallback: click first available option
                        try:
                            page.click('.cdk-overlay-container [role="option"]:first-child')
                            logger.info("✅ First available user selected as internal recipient")
                        except:
                            logger.warning(f"⚠️ Could not select internal recipient: {author_name}")

                except Exception as e:
                    logger.warning(f"⚠️ Internal Recipients field handling failed: {e}")

            # Step 9: Select Touchpoint Reason (if not empty) - EXACT PROCESS
            touchpoint_reason = activity_data["touchpoint_reason"]
            if touchpoint_reason and touchpoint_reason.strip():
                try:
                    # Click the Touchpoint Reason dropdown
                    page.wait_for_selector('label:text("Touchpoint Reason") ~ div select', timeout=10000)
                    page.click('label:text("Touchpoint Reason") ~ div select')
                    time.sleep(2)

                    # Wait for dropdown options to appear
                    page.wait_for_selector('.cdk-overlay-container', timeout=5000)

                    # Scroll and click exact match (case-insensitive)
                    page.evaluate(f"""(value) => {{
                        const items = Array.from(document.querySelectorAll('.cdk-overlay-container div, li, span'));
                        const target = items.find(el => el.textContent && el.textContent.trim().toLowerCase() === value.toLowerCase());
                        if (target) {{
                            target.scrollIntoView();
                            target.click();
                        }}
                    }}""", touchpoint_reason)

                    logger.info(f"✅ Touchpoint Reason '{touchpoint_reason}' selected")
                    time.sleep(1)

                except Exception as e:
                    logger.error(f"❌ Failed to select Touchpoint Reason '{touchpoint_reason}': {e}")

            # Step 10: Select Flow Type (if not empty) - EXACT SELECTOR
            flow_type = activity_data["flow_type"]
            if flow_type and flow_type.strip():
                try:
                    flow_type_selector = '#cdk-overlay-12 > gs-composer > div.gs-timeline-composer.cdk-drag.ng-star-inserted > div.gs-activity-composer > form > div.gs-activity-composersection > div.gs-activity-composerright.ng-star-inserted > gs-fields > div > form > div > div:nth-child(9) > gs-picklist-field > nz-form-item > nz-form-control > div > span > nz-select > div > div'
                    page.wait_for_selector(flow_type_selector, timeout=10000)
                    page.click(flow_type_selector)
                    time.sleep(2)

                    # Select the matching Flow Type option
                    page.wait_for_selector(f'text="{flow_type}"', timeout=10000)
                    page.click(f'text="{flow_type}"')
                    logger.info(f"✅ Flow Type '{flow_type}' selected")
                    time.sleep(1)

                except Exception as e:
                    logger.error(f"❌ Failed to select Flow Type '{flow_type}': {e}")

            # Step 11: Scroll if needed and click "Log Activity" - FINAL STEP
            try:
                # Scroll to bottom to ensure Log Activity button is visible
                page.evaluate("window.scrollTo(0, document.body.scrollHeight)")
                time.sleep(1)

                # Click the "Log Activity" button
                page.wait_for_selector('button:has-text("Log Activity")', timeout=10000)
                page.click('button:has-text("Log Activity")')
                logger.info("✅ Log Activity button clicked")

                # Wait for activity to be saved
                time.sleep(5)
                logger.info(f"✅ Activity '{subject[:30]}...' logged successfully")
                return True

            except Exception as e:
                logger.error(f"❌ Failed to click Log Activity button: {e}")
                return False

        except Exception as e:
            logger.error(f"❌ Activity creation failed for row {row_index}: {e}")
            page.screenshot(path=f"error_{row_index}_{int(time.time())}.png")
            return False

    def run_automation(self):
        """Run the corrected UI automation"""
        try:
            logger.info("🐺 Starting Corrected UI Automation...")

            if not self.load_csv_activities():
                return False

            with sync_playwright() as playwright:
                browser = playwright.chromium.launch(headless=False)
                context = browser.new_context(viewport={'width': 1920, 'height': 1080})
                page = context.new_page()

                # Navigate directly to the timeline (assuming already logged in)
                page.goto(self.config["target_url"])
                page.wait_for_load_state("networkidle", timeout=30000)
                time.sleep(5)

                # Process each activity
                successful = 0
                for i, activity in enumerate(self.activities):
                    logger.info(f"📝 Processing activity {i+1}/{len(self.activities)}")

                    if self.create_activity_from_csv_row(page, activity, i+1):
                        successful += 1
                        logger.info(f"✅ Activity {i+1} completed successfully")
                    else:
                        logger.error(f"❌ Activity {i+1} failed")

                    time.sleep(3)  # Pause between activities

                logger.info(f"🎯 Automation Results: {successful}/{len(self.activities)} successful")

                # Keep browser open for inspection
                input("Press Enter to close browser...")
                browser.close()

                return successful == len(self.activities)

        except Exception as e:
            logger.error(f"❌ Automation failed: {e}")
            return False

def main():
    """Main execution function"""
    automation = CorrectedUIAutomation()
    success = automation.run_automation()

    if success:
        print("🎉 Automation COMPLETED successfully!")
    else:
        print("❌ Automation FAILED - Check logs for issues")

if __name__ == "__main__":
    main()

#!/usr/bin/env python3
"""
🔧 FIXED GAINSIGHT UI AUTOMATOR
Fixes: Domain input (demo-emea1), credentials, timeline navigation
"""

import asyncio
import argparse
from playwright.async_api import async_playwright

class FixedGainsightAutomator:
    def __init__(self):
        self.browser = None
        self.page = None
    
    async def setup_browser(self, headless=False):
        """Setup browser"""
        playwright = await async_playwright().start()
        self.browser = await playwright.chromium.launch(headless=headless)
        self.page = await self.browser.new_page()
    
    async def login_with_domain_fix(self):
        """Fixed login with domain handling"""
        print("🌐 Navigating to Gainsight login...")
        await self.page.goto("https://auth.gainsightcloud.com/login?lc=en")
        await self.page.wait_for_timeout(3000)
        
        # FIXED: Handle domain input
        print("🏢 Checking for domain input...")
        domain_selectors = [
            'input[placeholder*="subdomain"]',
            'input[name*="subdomain"]', 
            'input[id*="subdomain"]',
            'input[placeholder*="domain"]',
            'input[type="text"]'
        ]
        
        domain_entered = False
        for selector in domain_selectors:
            try:
                if await self.page.locator(selector).count() > 0:
                    print("✅ Found domain input, entering: demo-emea1")
                    await self.page.fill(selector, "demo-emea1")
                    domain_entered = True
                    
                    # Click continue
                    continue_selectors = [
                        'button:has-text("Continue")',
                        'button:has-text("Next")',
                        'button[type="submit"]',
                        'button:has-text("Sign In")'
                    ]
                    
                    for btn_selector in continue_selectors:
                        if await self.page.locator(btn_selector).count() > 0:
                            await self.page.click(btn_selector)
                            print("🚀 Domain submitted")
                            await self.page.wait_for_timeout(3000)
                            break
                    break
            except:
                continue
        
        if not domain_entered:
            print("ℹ️  No domain input found, proceeding...")
        
        # Login credentials
        print("🔐 Entering credentials...")
        
        # Username
        username_selectors = [
            'input[name="username"]',
            'input[name="email"]',
            'input[type="email"]',
            'input[placeholder*="email"]',
            'input[placeholder*="username"]'
        ]
        
        for selector in username_selectors:
            try:
                if await self.page.locator(selector).count() > 0:
                    await self.page.fill(selector, "<EMAIL>")
                    print("✅ Username entered")
                    break
            except:
                continue
        
        # Password
        password_selectors = [
            'input[name="password"]',
            'input[type="password"]'
        ]
        
        for selector in password_selectors:
            try:
                if await self.page.locator(selector).count() > 0:
                    await self.page.fill(selector, "@Ramprasad826ie")
                    print("✅ Password entered")
                    break
            except:
                continue
        
        # Submit login
        login_selectors = [
            'button[type="submit"]',
            'button:has-text("Sign in")',
            'button:has-text("Login")',
            'button:has-text("Sign In")',
            'input[type="submit"]'
        ]
        
        for selector in login_selectors:
            try:
                if await self.page.locator(selector).count() > 0:
                    await self.page.click(selector)
                    print("🚀 Login submitted")
                    break
            except:
                continue
        
        # Wait for login completion
        print("⏳ Waiting for login...")
        await self.page.wait_for_timeout(6000)
        
        # Check if login successful
        current_url = self.page.url
        if "gainsightcloud.com" in current_url and "login" not in current_url:
            print("✅ Login successful!")
            return True
        else:
            print(f"⚠️  Login status unclear. URL: {current_url}")
            return True  # Continue anyway
    
    async def navigate_to_timeline(self):
        """Navigate to customer timeline"""
        timeline_url = "https://demo-emea1.gainsightcloud.com/v1/ui/customersuccess360?cid=1P02IPMAEL4M3CQGY4K5FHU22D2HHT11KCKU#/8fd79692-8e98-4543-b2ec-bc0dba3776ca"
        
        print("🌐 Navigating to customer timeline...")
        await self.page.goto(timeline_url)
        await self.page.wait_for_timeout(4000)
        
        # Click Timeline tab
        timeline_selectors = [
            'a:has-text("Timeline")',
            'button:has-text("Timeline")', 
            '[data-tab="timeline"]',
            '.tab:has-text("Timeline")',
            'li:has-text("Timeline")'
        ]
        
        for selector in timeline_selectors:
            try:
                if await self.page.locator(selector).count() > 0:
                    await self.page.click(selector)
                    print("📋 Timeline tab clicked")
                    await self.page.wait_for_timeout(2000)
                    return True
            except:
                continue
        
        print("⚠️  Timeline tab not found, continuing...")
        return True
    
    async def create_activity(self, activity_data):
        """Create activity with better error handling"""
        subject = activity_data.get("subject", "Test Activity")
        activity_type = activity_data.get("activityType", "Email")
        
        print(f"📝 Creating activity: {subject[:30]}...")
        
        try:
            # Create button
            create_selectors = [
                'button:has-text("Create")',
                '[data-testid="create-button"]',
                '.create-button',
                'button:has-text("+ Create")'
            ]
            
            for selector in create_selectors:
                if await self.page.locator(selector).count() > 0:
                    await self.page.click(selector)
                    print("➕ Create clicked")
                    await self.page.wait_for_timeout(1000)
                    break
            
            # Activity option
            activity_selectors = [
                'a:has-text("Activity")',
                'button:has-text("Activity")',
                '[data-option="activity"]'
            ]
            
            for selector in activity_selectors:
                if await self.page.locator(selector).count() > 0:
                    await self.page.click(selector)
                    print("📝 Activity selected")
                    await self.page.wait_for_timeout(2000)
                    break
            
            # Activity type
            try:
                type_selectors = [
                    'select[name*="type"]',
                    'select:has(option:has-text("Email"))'
                ]
                
                for selector in type_selectors:
                    if await self.page.locator(selector).count() > 0:
                        await self.page.select_option(selector, label=activity_type)
                        print(f"🏷️  Type: {activity_type}")
                        break
            except:
                print("⚠️  Could not set activity type")
            
            # Subject
            subject_selectors = [
                'input[name="subject"]',
                'input[placeholder*="subject"]',
                'input[placeholder*="title"]'
            ]
            
            for selector in subject_selectors:
                if await self.page.locator(selector).count() > 0:
                    await self.page.fill(selector, subject)
                    print(f"📝 Subject: {subject}")
                    break
            
            # Submit
            submit_selectors = [
                'button:has-text("Log Activity")',
                'button:has-text("Save Activity")',
                'button:has-text("Save")',
                'button[type="submit"]'
            ]
            
            for selector in submit_selectors:
                if await self.page.locator(selector).count() > 0:
                    await self.page.click(selector)
                    print("💾 Activity submitted")
                    await self.page.wait_for_timeout(3000)
                    return True
            
            return False
            
        except Exception as e:
            print(f"❌ Activity creation error: {e}")
            return False
    
    async def run_automation(self, max_activities=1):
        """Run complete automation"""
        print("🚀 FIXED GAINSIGHT AUTOMATION")
        print("=" * 35)
        
        try:
            await self.setup_browser(headless=False)
            
            # Login with domain fix
            if not await self.login_with_domain_fix():
                print("❌ Login failed")
                return False
            
            # Navigate to timeline
            if not await self.navigate_to_timeline():
                print("❌ Timeline navigation failed")
                return False
            
            # Create activities
            success_count = 0
            for i in range(max_activities):
                activity_data = {
                    "subject": f"Test Activity {i+1} - Fixed Automation",
                    "activityType": "Email"
                }
                
                if await self.create_activity(activity_data):
                    success_count += 1
                    print(f"✅ Activity {i+1} created")
                else:
                    print(f"❌ Activity {i+1} failed")
                
                if i < max_activities - 1:
                    await self.page.wait_for_timeout(2000)
            
            print(f"\n📊 Results: {success_count}/{max_activities} activities created")
            print("✅ Fixed automation completed!")
            
            return True
            
        except Exception as e:
            print(f"❌ Automation error: {e}")
            return False
        
        finally:
            if self.browser:
                await self.browser.close()

async def main():
    """Main function"""
    parser = argparse.ArgumentParser()
    parser.add_argument("--max-activities", type=int, default=1)
    parser.add_argument("--headless", action="store_true")
    args = parser.parse_args()
    
    automator = FixedGainsightAutomator()
    await automator.run_automation(max_activities=args.max_activities)

if __name__ == "__main__":
    asyncio.run(main())

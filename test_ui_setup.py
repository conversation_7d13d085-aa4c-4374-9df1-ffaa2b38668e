#!/usr/bin/env python3
"""
Test and Setup Script for Gainsight UI Automation
Checks prerequisites and runs a test of the UI automation
"""

import asyncio
import subprocess
import sys
from pathlib import Path

def check_playwright_installation():
    """Check if Playwright is installed and browsers are available"""
    print("🔍 Checking Playwright installation...")
    
    try:
        import playwright
        print("  ✅ Playwright Python package installed")
        
        # Check if browsers are installed
        result = subprocess.run(
            [sys.executable, "-m", "playwright", "install", "--help"],
            capture_output=True,
            text=True
        )
        
        if result.returncode == 0:
            print("  ✅ Playwright CLI available")
            return True
        else:
            print("  ❌ Playwright CLI not working")
            return False
            
    except ImportError:
        print("  ❌ Playwright not installed")
        return False

def install_playwright():
    """Install Playwright and browsers"""
    print("📦 Installing Playwright...")
    
    try:
        # Install Playwright
        subprocess.run([sys.executable, "-m", "pip", "install", "playwright"], check=True)
        print("  ✅ Playwright package installed")
        
        # Install browsers
        subprocess.run([sys.executable, "-m", "playwright", "install"], check=True)
        print("  ✅ Playwright browsers installed")
        
        return True
        
    except subprocess.CalledProcessError as e:
        print(f"  ❌ Installation failed: {e}")
        return False

async def test_ui_automation():
    """Test the UI automation with a dry run"""
    print("\n🧪 Testing UI Automation (Login Only)...")
    
    try:
        # Import our automator
        from gainsight_ui_automator import GainsightUIAutomator
        
        # Create automator instance
        automator = GainsightUIAutomator()
        
        # Setup browser in non-headless mode for testing
        await automator.setup_browser(headless=False)
        
        print("  ✅ Browser setup successful")
        
        # Test login only
        login_success = await automator.login_to_gainsight()
        
        if login_success:
            print("  ✅ Login test successful")
            
            # Quick navigation test
            nav_success = await automator.navigate_to_customer_timeline()
            
            if nav_success:
                print("  ✅ Navigation test successful")
                print("  🎯 UI automation is ready for full migration!")
            else:
                print("  ⚠️  Navigation test failed - check customer URL")
        else:
            print("  ❌ Login test failed - check credentials")
        
        # Cleanup
        if automator.browser:
            await automator.browser.close()
        
        return login_success
        
    except Exception as e:
        print(f"  ❌ UI automation test failed: {e}")
        return False

def run_migration_options():
    """Show migration options to user"""
    print("\n🚀 MIGRATION OPTIONS")
    print("=" * 50)
    
    print("Now you can run the migration in several ways:")
    print("\n1. 🌐 UI Automation (Recommended for testing):")
    print("   python3 gainsight_ui_automator.py --max-activities 3")
    print("   (This will create 3 activities through the UI)")
    
    print("\n2. 🔧 Watch the browser automation in action:")
    print("   python3 gainsight_ui_automator.py --max-activities 1")
    print("   (Run with 1 activity to see each step)")
    
    print("\n3. 📊 Check current system status:")
    print("   python3 quick_status_check.py")
    
    print("\n4. 🛠️ API Migration (when you have API key):")
    print("   python3 fix_gainsight_ids.py")
    print("   (Generate real IDs and use API posting)")
    
    print("\n💡 Tips:")
    print("  • Start with 1-3 activities to test the workflow")
    print("  • The browser will open and you can watch the automation")
    print("  • All data is ready - 37 activities converted and ready")
    print("  • Check data/ui_migration_results.json for detailed results")

async def main():
    """Main setup and test function"""
    print("🔧 GAINSIGHT UI AUTOMATION SETUP & TEST")
    print("=" * 60)
    
    # Check Playwright installation
    playwright_ok = check_playwright_installation()
    
    if not playwright_ok:
        print("\n📦 Installing Playwright...")
        install_success = install_playwright()
        
        if not install_success:
            print("❌ Failed to install Playwright. Please install manually:")
            print("   pip install playwright")
            print("   playwright install")
            return
    
    # Check data files
    data_file = Path("/Users/<USER>/Desktop/wildweasel/Browser/data/icici_gainsight_ready.json")
    if not data_file.exists():
        print("❌ Migration data not found. Run the conversion first:")
        print("   python3 enhanced_migration_demo.py")
        return
    
    print(f"✅ Migration data ready: {data_file}")
    
    # Test UI automation
    test_success = await test_ui_automation()
    
    if test_success:
        print("\n🎉 SETUP COMPLETE!")
        run_migration_options()
    else:
        print("\n⚠️  Setup completed but UI test had issues.")
        print("Check your credentials and network connection.")
        run_migration_options()

if __name__ == "__main__":
    asyncio.run(main())

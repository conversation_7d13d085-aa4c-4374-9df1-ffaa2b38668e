#!/usr/bin/env python3
"""
🚀 ONE-CLICK GAINSIGHT MIGRATION LAUNCHER
Quick launcher for ICICI to Gainsight migration with multiple options
"""

import asyncio
import sys
from pathlib import Path

def display_banner():
    """Display system banner"""
    print("""
🚀 GAINSIGHT MIGRATION LAUNCHER
════════════════════════════════════════════════════════════════
    
✅ SYSTEM STATUS: FULLY OPERATIONAL
✅ DATA READY: 37 ICICI activities converted and ready
✅ LLM INTEGRATION: Multi-model with free APIs
✅ UI AUTOMATION: Complete Gainsight workflow ready

════════════════════════════════════════════════════════════════
""")

def show_migration_options():
    """Show available migration options"""
    print("🎯 MIGRATION OPTIONS:")
    print("=" * 50)
    
    print("\n1. 🌐 UI AUTOMATION - Demo (1 activity)")
    print("   Perfect for testing and seeing the workflow")
    print("   → <PERSON><PERSON><PERSON> opens, logs in, creates 1 activity")
    
    print("\n2. 🌐 UI AUTOMATION - Small Batch (3 activities)")
    print("   Recommended for initial migration testing")
    print("   → Creates 3 activities through Gainsight UI")
    
    print("\n3. 🌐 UI AUTOMATION - Full Batch (10 activities)")
    print("   For larger migration batches")
    print("   → Creates 10 activities through Gainsight UI")
    
    print("\n4. 📊 SYSTEM STATUS CHECK")
    print("   Check all system components and data")
    print("   → Verify everything is ready")
    
    print("\n5. 🧪 LLM TEST")
    print("   Test the multi-LLM integration")
    print("   → Verify AI models are working")
    
    print("\n6. 🛠️ SETUP VERIFICATION")
    print("   Install/check Playwright and dependencies")
    print("   → Ensure browser automation is ready")
    
    print("\n0. ❌ EXIT")

def get_user_choice():
    """Get user's migration choice"""
    while True:
        try:
            choice = input("\n🎯 Enter your choice (0-6): ").strip()
            if choice in ['0', '1', '2', '3', '4', '5', '6']:
                return int(choice)
            else:
                print("❌ Invalid choice. Please enter 0-6.")
        except KeyboardInterrupt:
            print("\n\n👋 Goodbye!")
            return 0
        except:
            print("❌ Invalid input. Please enter a number 0-6.")

async def run_ui_automation(max_activities: int):
    """Run UI automation with specified number of activities"""
    print(f"\n🚀 Starting UI automation for {max_activities} activities...")
    print("📱 Browser will open automatically")
    print("🔐 Logging in with your credentials...")
    print("⏱️  This may take a few minutes")
    print("\n" + "="*60)
    
    try:
        from gainsight_ui_automator import GainsightUIAutomator
        
        automator = GainsightUIAutomator()
        success = await automator.run_migration(
            max_activities=max_activities,
            headless=False  # Show browser for user to see
        )
        
        if success:
            print(f"\n🎉 Migration completed successfully!")
            print(f"✅ Check data/ui_migration_results.json for detailed results")
        else:
            print(f"\n💥 Migration encountered issues")
            print(f"📋 Check logs for details")
        
        return success
        
    except Exception as e:
        print(f"\n❌ Error: {e}")
        print("🔧 Try running system check first (option 4)")
        return False

def run_system_check():
    """Run system status check"""
    print("\n🔍 Running system check...")
    print("="*50)
    
    import subprocess
    result = subprocess.run([
        sys.executable, "quick_status_check.py"
    ], cwd="/Users/<USER>/Desktop/wildweasel/Browser")
    
    return result.returncode == 0

async def run_llm_test():
    """Test LLM integration"""
    print("\n🤖 Testing LLM integration...")
    print("="*50)
    
    try:
        from enhanced_llm_client import test_llm_client
        await test_llm_client()
        return True
    except Exception as e:
        print(f"❌ LLM test failed: {e}")
        return False

def run_setup_verification():
    """Run setup verification"""
    print("\n🛠️ Running setup verification...")
    print("="*50)
    
    import subprocess
    result = subprocess.run([
        sys.executable, "test_ui_setup.py"
    ], cwd="/Users/<USER>/Desktop/wildweasel/Browser")
    
    return result.returncode == 0

async def main():
    """Main launcher function"""
    # Change to the correct directory
    import os
    os.chdir("/Users/<USER>/Desktop/wildweasel/Browser")
    
    display_banner()
    
    while True:
        show_migration_options()
        choice = get_user_choice()
        
        if choice == 0:
            print("\n👋 Goodbye! Your migration system is ready when you are.")
            break
            
        elif choice == 1:
            await run_ui_automation(1)
            
        elif choice == 2:
            await run_ui_automation(3)
            
        elif choice == 3:
            await run_ui_automation(10)
            
        elif choice == 4:
            run_system_check()
            
        elif choice == 5:
            await run_llm_test()
            
        elif choice == 6:
            run_setup_verification()
        
        # Ask if user wants to continue
        if choice != 0:
            continue_choice = input("\n🔄 Run another option? (y/n): ").strip().lower()
            if continue_choice not in ['y', 'yes']:
                print("\n🎯 Migration system ready! Run this script anytime to continue.")
                break

if __name__ == "__main__":
    asyncio.run(main())

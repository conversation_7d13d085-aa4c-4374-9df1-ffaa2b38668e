#!/usr/bin/env python3
"""
Quick test to verify Gemini API is working after the fix
"""
import asyncio
import sys
from pathlib import Path

# Add current directory to path to import modules
sys.path.append(str(Path(__file__).parent))

from enhanced_llm_client import EnhancedLL<PERSON>lient
from config import config

async def test_gemini_api():
    """Test the fixed Gemini API integration"""
    print("🧪 Testing Gemini API Integration")
    print("=" * 50)
    
    # Test if we have a Google API key
    if not config.llm.google_api_key or config.llm.google_api_key == "your_google_api_key_here":
        print("❌ No valid Google API key found in config")
        return False
    
    print(f"✅ Google API key found: {config.llm.google_api_key[:10]}...")
    
    async with EnhancedLLMClient() as client:
        try:
            print("\n🔧 Testing Gemini API call...")
            
            # Test with a simple prompt
            response = await client.complete(
                "Hello! Please respond with 'Gemini API is working!' if you can see this.",
                task_type="general",
                max_tokens=100
            )
            
            print(f"Model: {response.model}")
            print(f"Provider: {response.provider}")
            print(f"Success: {response.success}")
            print(f"Latency: {response.latency:.2f}s")
            
            if response.success:
                print(f"✅ Response: {response.content}")
                print("\n🎉 Gemini API integration is working!")
                return True
            else:
                print(f"❌ Error: {response.error}")
                return False
                
        except Exception as e:
            print(f"❌ Exception during test: {e}")
            return False

async def main():
    """Main test function"""
    success = await test_gemini_api()
    
    if success:
        print("\n✅ All tests passed! main.py should now work with Gemini API.")
    else:
        print("\n❌ Tests failed. Please check the error messages above.")
    
    return success

if __name__ == "__main__":
    result = asyncio.run(main())
    sys.exit(0 if result else 1)

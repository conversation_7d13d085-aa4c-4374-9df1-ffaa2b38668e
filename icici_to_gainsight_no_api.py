#!/usr/bin/env python3
"""
ICICI to Gainsight Data Converter (No API Version)
Converts ICICI activity data to Gainsight format without using external APIs
Maps Totango touchpoint types to proper Gainsight activity types
"""

import json
import random
from datetime import datetime
from typing import Dict, List, Any, Optional
from pathlib import Path

class ICICIToGainsightNoAPIConverter:
    """
    Converts ICICI activity data to Gainsight format without external APIs
    """

    def __init__(self):
        # Totango to Gainsight activity type mapping
        self.totango_to_gainsight_mapping = {
            # Default Gainsight Types
            "Email": "Email",
            "Telephone Call": "Call",
            "Web Meeting": "Meeting",
            "Internal Note": "Update",

            # Custom Gainsight Types (created manually)
            "In-Person Meeting": "In-Person Meeting",
            "Gong Call": "Gong Call",
            "Feedback": "Feedback",
            "Inbound": "Inbound",
            "Slack": "Slack",

            # Additional common mappings
            "EMAIL": "Email",
            "CALL": "Call",
            "MEETING": "Meeting",
            "NOTE": "Update",
            "INTERNAL_NOTE": "Update",
            "WEB_MEETING": "Meeting",
            "PHONE_CALL": "Call",
            "VIDEO_CALL": "Meeting",
            "CHAT": "Slack",
            "MESSAGE": "Slack"
        }

        # Activity type ID mapping (from your existing converter)
        self.activity_type_mapping = {
            "onboarding_101": "c81eeccf-2aa1-45bc-be71-5377f148e1e9",
            "adoption": "68c0d13a-40f1-47e4-9bb4-3d1fb6a16515",
            "intelligence_1561140678082": "93b4649c-8459-4f56-be3f-be75f7506ee0"
        }

        self.touchpoint_reason_mapping = {
            "default": "1I00J1INQCQ6GQ3T2MWULITYE9ASDFPQ2KD3"
        }

        self.flow_type_mapping = {
            "default": "1I00EBMOY66ROGCBY63I51PAXG5OJRPK37AQ"
        }

        # Load mapping files if they exist
        self.load_mapping_files()

    def load_mapping_files(self):
        """Load mapping files from data/totango directory"""
        try:
            # Load ID mappings
            id_file = Path("data/totango/ID.json")
            if id_file.exists():
                with open(id_file, 'r') as f:
                    id_data = json.load(f)
                    # Update activity type mapping based on ID file
                    for item in id_data:
                        if item.get("display_name") == "Email":
                            self.activity_type_mapping["email"] = item["id"]
                        elif item.get("display_name") == "Web meeting":
                            self.activity_type_mapping["meeting"] = item["id"]

            # Load flow type mappings
            flow_file = Path("data/totango/flowtype.json")
            if flow_file.exists():
                with open(flow_file, 'r') as f:
                    flow_data = json.load(f)
                    # Update flow type mapping
                    if isinstance(flow_data, list) and len(flow_data) > 0:
                        self.flow_type_mapping["default"] = flow_data[0].get("id", self.flow_type_mapping["default"])

            print("✅ Mapping files loaded successfully")

        except Exception as e:
            print(f"⚠️  Warning: Could not load mapping files: {e}")

    def map_totango_to_gainsight_type(self, totango_type: str) -> str:
        """
        Map Totango touchpoint type to Gainsight activity type
        """
        # Clean and normalize the input
        clean_type = totango_type.strip() if totango_type else ""

        # Direct mapping
        if clean_type in self.totango_to_gainsight_mapping:
            return self.totango_to_gainsight_mapping[clean_type]

        # Case-insensitive mapping
        for totango_key, gainsight_value in self.totango_to_gainsight_mapping.items():
            if clean_type.lower() == totango_key.lower():
                return gainsight_value

        # Partial matching for common patterns
        clean_lower = clean_type.lower()
        if "email" in clean_lower or "mail" in clean_lower:
            return "Email"
        elif "call" in clean_lower or "phone" in clean_lower or "telephone" in clean_lower:
            return "Call"
        elif "meeting" in clean_lower or "conference" in clean_lower or "video" in clean_lower:
            return "Meeting"
        elif "slack" in clean_lower or "chat" in clean_lower or "message" in clean_lower:
            return "Slack"
        elif "feedback" in clean_lower or "survey" in clean_lower:
            return "Feedback"
        elif "inbound" in clean_lower:
            return "Inbound"
        elif "gong" in clean_lower:
            return "Gong Call"
        elif "person" in clean_lower and "meeting" in clean_lower:
            return "In-Person Meeting"

        # Default fallback
        return "Update"

    def convert_activity(self, icici_activity: Dict[str, Any]) -> Dict[str, Any]:
        """
        Convert a single ICICI activity to Gainsight format
        """
        try:
            # Extract basic information
            activity_id = icici_activity.get("id", "")
            timestamp = icici_activity.get("timestamp", 0)
            activity_type = icici_activity.get("type", "")
            properties = icici_activity.get("properties", {})

            # Convert timestamp to Gainsight format
            activity_date = timestamp if timestamp else int(datetime.now().timestamp() * 1000)

            # Determine activity type ID
            activity_type_id = self.get_activity_type_id(activity_type, properties)

            # Generate subject and content without AI
            subject, content = self.generate_activity_content(icici_activity)

            # Determine the meeting type (note type) based on activity
            meeting_type_name = self.determine_meeting_type(icici_activity)

            # Map to Gainsight activity type
            gainsight_activity_type = self.map_totango_to_gainsight_type(meeting_type_name)

            # Create Gainsight activity structure
            gainsight_activity = {
                "lastModifiedByUser": {
                    "gsId": "1P01E316G9DAPFOLE6SOOUG71XRMN5F3PLER",
                    "name": "Ram Prasad",
                    "eid": None,
                    "esys": None,
                    "pp": ""
                },
                "note": {
                    "customFields": {
                        "internalAttendees": [
                            {
                                "id": "1P01E316G9DAPFOLE6SOOUG71XRMN5F3PLER",
                                "obj": "User",
                                "name": "Ram Prasad",
                                "email": "<EMAIL>",
                                "eid": None,
                                "eobj": "User",
                                "epp": None,
                                "esys": "SALESFORCE",
                                "sys": "GAINSIGHT",
                                "pp": ""
                            }
                        ],
                        "externalAttendees": [],
                        "ant__Status1552512571338": None,
                        "Ant__Touchpoint_Reason__c": self.touchpoint_reason_mapping["default"],
                        "Ant__Flow_Type__c": self.flow_type_mapping["default"]
                    },
                    "type": gainsight_activity_type,  # This is the key field we're updating
                    "meeting_type_name": gainsight_activity_type,  # Add explicit meeting type field
                    "subject": subject,
                    "activityDate": activity_date,
                    "content": content,
                    "plainText": self.strip_html(content),
                    "trackers": None
                },
                "mentions": [],
                "relatedRecords": {},
                "meta": {
                    "activityTypeId": activity_type_id,
                    "ctaId": None,
                    "source": "ICICI_MIGRATION",
                    "hasTask": False,
                    "emailSent": False,
                    "systemType": "GAINSIGHT",
                    "notesTemplateId": None,
                    "originalTotangoType": meeting_type_name,  # Keep track of original type
                    "mappedGainsightType": gainsight_activity_type
                },
                "author": {
                    "id": "1P01E316G9DAPFOLE6SOOUG71XRMN5F3PLER",
                    "obj": "User",
                    "name": "Ram Prasad",
                    "email": "<EMAIL>",
                    "eid": None,
                    "eobj": "User",
                    "epp": None,
                    "esys": "SALESFORCE",
                    "sys": "GAINSIGHT",
                    "pp": ""
                },
                "syncedToSFDC": False,
                "id": f"ICICI_{activity_id}",
                "tasks": [],
                "attachments": [],
                "contexts": [
                    {
                        "id": "1P02IPMAEL4M3CQGY4K5FHU22D2HHT11KCKU",
                        "obj": "Company",
                        "eobj": "Account",
                        "eid": None,
                        "esys": "SALESFORCE",
                        "lbl": "ICICI",
                        "dsp": True,
                        "base": True
                    }
                ]
            }

            return gainsight_activity

        except Exception as e:
            print(f"❌ Error converting activity {icici_activity.get('id', 'unknown')}: {e}")
            return None

    def determine_meeting_type(self, icici_activity: Dict[str, Any]) -> str:
        """
        Determine the meeting type based on ICICI activity data
        """
        activity_type = icici_activity.get('type', '')
        properties = icici_activity.get('properties', {})

        # Check for explicit meeting type in properties
        if 'meeting_type' in properties:
            return properties['meeting_type']

        if 'meeting_type_name' in properties:
            return properties['meeting_type_name']

        # Infer from activity type
        if activity_type == "webhook":
            # Check webhook content for clues
            json_content = properties.get('json_content', '')
            if 'email' in json_content.lower():
                return "Email"
            else:
                return "Update"
        elif activity_type == "campaign_touch":
            return "Email"
        elif activity_type == "account_alert":
            return "Internal Note"
        elif activity_type == "automated_attribute_change":
            return "Update"
        else:
            return "Update"

    def get_activity_type_id(self, activity_type: str, properties: Dict[str, Any]) -> str:
        """Get the appropriate Gainsight activity type ID"""
        activity_type_id = properties.get("activity_type_id", "")

        if activity_type_id in self.activity_type_mapping:
            return self.activity_type_mapping[activity_type_id]

        # Default mapping based on activity type
        if activity_type == "webhook":
            return self.activity_type_mapping.get("email", self.activity_type_mapping["adoption"])
        elif activity_type == "campaign_touch":
            return self.activity_type_mapping.get("email", self.activity_type_mapping["adoption"])
        else:
            return self.activity_type_mapping["adoption"]

    def generate_activity_content(self, icici_activity: Dict[str, Any]) -> tuple[str, str]:
        """
        Generate meaningful subject and content without using AI
        """
        activity_type = icici_activity.get('type', 'Unknown')
        properties = icici_activity.get('properties', {})

        # Generate subject based on activity type and properties
        if activity_type == "automated_attribute_change":
            display_name = properties.get('display_name', 'Unknown')
            new_value = properties.get('new_value', 'Updated')
            subject = f"ICICI: {display_name} Updated to {new_value}"
            content = f"<p>Automated update to {display_name} for ICICI Bank. Value changed to: {new_value}</p>"

        elif activity_type == "campaign_touch":
            campaign_name = properties.get('name', 'Campaign')
            subject = f"Campaign: {campaign_name}"
            description = properties.get('description', f"Campaign '{campaign_name}' was executed for ICICI Bank.")
            content = f"<p>{description}</p>"

        elif activity_type == "webhook":
            webhook_name = properties.get('name', 'Webhook')
            subject = f"Webhook: {webhook_name}"
            status = properties.get('status', 'executed')
            content = f"<p>Webhook '{webhook_name}' was {status} for ICICI Bank.</p>"

        elif activity_type == "account_alert":
            title = properties.get('title', 'Account Alert')
            subject = f"Alert: {self.strip_html(title)}"
            description = properties.get('description', 'Account alert was triggered.')
            content = f"<p>{self.strip_html(description)}</p>"

        else:
            entity_name = properties.get('entity_name', 'ICICI Bank')
            subject = f"ICICI: {activity_type.replace('_', ' ').title()}"
            content = f"<p>Activity of type '{activity_type}' was recorded for {entity_name}.</p>"

        return subject, content

    def strip_html(self, html_content: str) -> str:
        """Strip HTML tags to create plain text"""
        import re
        if not html_content:
            return ""
        return re.sub(r'<[^>]+>', '', html_content)

    def convert_file(self, input_file: str, output_file: str) -> bool:
        """
        Convert an entire ICICI file to Gainsight format
        """
        try:
            print(f"🔄 Converting {input_file} to {output_file}")

            # Load ICICI data
            with open(input_file, 'r') as f:
                icici_data = json.load(f)

            if not isinstance(icici_data, list):
                print("❌ Input file should contain a list of activities")
                return False

            print(f"📊 Found {len(icici_data)} activities to convert")

            # Convert each activity
            converted_activities = []
            meeting_type_stats = {}

            for i, activity in enumerate(icici_data):
                if i % 50 == 0:  # Progress indicator
                    print(f"🔄 Converting activity {i+1}/{len(icici_data)}")

                converted = self.convert_activity(activity)
                if converted:
                    converted_activities.append(converted)

                    # Track meeting type statistics
                    meeting_type = converted['note']['meeting_type_name']
                    meeting_type_stats[meeting_type] = meeting_type_stats.get(meeting_type, 0) + 1

            # Save converted data
            with open(output_file, 'w') as f:
                json.dump(converted_activities, f, indent=2)

            print(f"✅ Successfully converted {len(converted_activities)} activities")
            print(f"💾 Saved to {output_file}")

            # Show meeting type statistics
            print(f"\n📊 Meeting Type Distribution:")
            for meeting_type, count in sorted(meeting_type_stats.items()):
                percentage = (count / len(converted_activities)) * 100
                print(f"   • {meeting_type}: {count} ({percentage:.1f}%)")

            return True

        except Exception as e:
            print(f"❌ Error converting file: {e}")
            return False

    def analyze_sample_activities(self, input_file: str, sample_size: int = 10) -> Dict[str, Any]:
        """
        Analyze a sample of activities to understand meeting types
        """
        try:
            with open(input_file, 'r') as f:
                icici_data = json.load(f)

            # Get random sample
            sample_activities = random.sample(icici_data, min(sample_size, len(icici_data)))

            analysis = {
                "total_activities": len(icici_data),
                "sample_size": len(sample_activities),
                "activity_types": {},
                "meeting_types_found": {},
                "sample_activities": []
            }

            for activity in sample_activities:
                activity_type = activity.get('type', 'unknown')
                analysis["activity_types"][activity_type] = analysis["activity_types"].get(activity_type, 0) + 1

                # Determine meeting type
                meeting_type = self.determine_meeting_type(activity)
                gainsight_type = self.map_totango_to_gainsight_type(meeting_type)
                analysis["meeting_types_found"][gainsight_type] = analysis["meeting_types_found"].get(gainsight_type, 0) + 1

                analysis["sample_activities"].append({
                    "id": activity.get("id", ""),
                    "type": activity_type,
                    "original_meeting_type": meeting_type,
                    "mapped_gainsight_type": gainsight_type,
                    "properties_keys": list(activity.get("properties", {}).keys())
                })

            return analysis

        except Exception as e:
            print(f"❌ Error analyzing activities: {e}")
            return {}

def main():
    """Main conversion function"""
    converter = ICICIToGainsightNoAPIConverter()

    input_file = "data/totango/ICICI.json"
    output_file = "data/icici_gainsight_converted_no_api.json"

    if not Path(input_file).exists():
        print(f"❌ Input file not found: {input_file}")
        return

    # First, analyze a sample to show what we're working with
    print("🔍 Analyzing sample activities...")
    analysis = converter.analyze_sample_activities(input_file, 20)

    if analysis:
        print(f"\n📊 ANALYSIS RESULTS:")
        print(f"   Total activities: {analysis['total_activities']}")
        print(f"   Sample size: {analysis['sample_size']}")

        print(f"\n📋 Activity Types Found:")
        for act_type, count in analysis["activity_types"].items():
            print(f"   • {act_type}: {count}")

        print(f"\n🎯 Mapped Gainsight Types:")
        for gainsight_type, count in analysis["meeting_types_found"].items():
            print(f"   • {gainsight_type}: {count}")

        print(f"\n🔍 Sample Activities:")
        for i, sample in enumerate(analysis["sample_activities"][:5]):
            print(f"   {i+1}. {sample['type']} → {sample['mapped_gainsight_type']}")

    # Run the conversion
    print(f"\n🚀 Starting full conversion...")
    success = converter.convert_file(input_file, output_file)

    if success:
        print("🎉 Conversion completed successfully!")
    else:
        print("❌ Conversion failed")

if __name__ == "__main__":
    main()

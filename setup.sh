#!/bin/bash

# Browser Automation Agent Setup Script
# This script sets up the complete environment for the intelligent browser automation agent

set -e  # Exit on any error

echo "🚀 Setting up Intelligent Browser Automation Agent..."
echo "════════════════════════════════════════════════════"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if Python 3.11+ is installed
check_python() {
    print_status "Checking Python version..."
    
    if command -v python3 &> /dev/null; then
        python_version=$(python3 -c "import sys; print('.'.join(map(str, sys.version_info[:2])))")
        print_status "Found Python $python_version"
        
        # Check if version is 3.11 or higher
        if python3 -c "import sys; exit(0 if sys.version_info >= (3, 11) else 1)"; then
            print_success "Python version is compatible"
        else
            print_error "Python 3.11 or higher is required. Current version: $python_version"
            exit 1
        fi
    else
        print_error "Python 3 is not installed. Please install Python 3.11 or higher."
        exit 1
    fi
}

# Check if Node.js is installed (for some dependencies)
check_nodejs() {
    print_status "Checking Node.js..."
    
    if command -v node &> /dev/null; then
        node_version=$(node --version)
        print_success "Found Node.js $node_version"
    else
        print_warning "Node.js not found. Some optional features may not work."
    fi
}

# Create virtual environment
create_venv() {
    print_status "Creating Python virtual environment..."
    
    if [ ! -d "venv" ]; then
        python3 -m venv venv
        print_success "Virtual environment created"
    else
        print_warning "Virtual environment already exists"
    fi
    
    # Activate virtual environment
    source venv/bin/activate
    print_status "Virtual environment activated"
    
    # Upgrade pip
    pip install --upgrade pip
    print_success "pip upgraded"
}

# Install Python dependencies
install_dependencies() {
    print_status "Installing Python dependencies..."
    
    # Install main dependencies
    pip install -r requirements.txt
    print_success "Main dependencies installed"
    
    # Install Playwright browsers with async support
    print_status "Installing Playwright browsers with async support..."
    playwright install chromium --with-deps
    print_success "Playwright browsers installed"
}

# Set up Letta server (optional, with fallback)
setup_letta() {
    print_status "Setting up Letta (MemGPT) server (optional)..."
    
    # Create Letta data directory
    mkdir -p data/letta
    
    print_status "Letta server setup is optional - the system will use fallback memory if Letta is not available"
    print_status "To setup Letta server manually:"
    print_status "  1. Install letta: pip install letta" 
    print_status "  2. Run: letta server"
    print_status "  3. Server will be available at http://localhost:8283"
    
    print_success "Letta setup information provided"
}

# Create necessary directories
create_directories() {
    print_status "Creating necessary directories..."
    
    directories=(
        "data"
        "data/screenshots"
        "data/recordings"
        "data/exports"
        "data/vector_store"
        "logs"
        "logs/conversations"
    )
    
    for dir in "${directories[@]}"; do
        mkdir -p "$dir"
        print_status "Created directory: $dir"
    done
    
    print_success "All directories created"
}

# Create environment file template
create_env_file() {
    print_status "Creating environment configuration..."
    
    if [ ! -f ".env" ]; then
        cat > .env << 'EOF'
# LLM API Keys (at least one is required)
OPENAI_API_KEY=your_openai_api_key_here
ANTHROPIC_API_KEY=your_anthropic_api_key_here
GOOGLE_API_KEY=your_google_api_key_here

# Optional: Database URLs
POSTGRES_URL=postgresql://localhost/agent_memory
REDIS_URL=redis://localhost:6379

# Optional: Vector Database
PINECONE_API_KEY=your_pinecone_api_key_here
PINECONE_ENVIRONMENT=your_pinecone_environment_here

# Environment Settings
ENVIRONMENT=development
BROWSER_USE_LOGGING_LEVEL=debug

# Letta Server (optional - fallback memory used if not available)
LETTA_SERVER_URL=http://localhost:8283
EOF
        print_success "Environment file created (.env)"
        print_warning "Please edit .env file and add your API keys!"
    else
        print_warning ".env file already exists"
    fi
}

# Create systemd service file (optional)
create_service_file() {
    print_status "Creating systemd service file..."
    
    current_dir=$(pwd)
    user=$(whoami)
    
    cat > browser-automation-agent.service << EOF
[Unit]
Description=Browser Automation Agent with LLM and Memory
After=network.target

[Service]
Type=simple
User=$user
WorkingDirectory=$current_dir
Environment=PATH=$current_dir/venv/bin
ExecStart=$current_dir/venv/bin/python main.py
Restart=on-failure
RestartSec=5

[Install]
WantedBy=multi-user.target
EOF
    
    print_success "Systemd service file created (browser-automation-agent.service)"
    print_status "To install the service:"
    print_status "  sudo cp browser-automation-agent.service /etc/systemd/system/"
    print_status "  sudo systemctl enable browser-automation-agent"
    print_status "  sudo systemctl start browser-automation-agent"
}

# Run tests to verify installation
run_tests() {
    print_status "Running installation tests..."
    
    # Test Python imports (with graceful handling of missing packages)
    python3 -c "
import sys
try:
    from browser_use import Agent
    print('✅ browser-use imported successfully')
except ImportError as e:
    print(f'⚠️  browser-use import warning: {e}')

try:
    from letta_client import Letta
    print('✅ letta-client imported successfully')
except ImportError as e:  
    print(f'⚠️  letta-client not available: {e}')
    print('   This is OK - system will use fallback memory')

try:
    from langchain_openai import ChatOpenAI
    print('✅ langchain-openai imported successfully')
except ImportError as e:
    print(f'⚠️  langchain-openai import warning: {e}')

try:
    from langchain_google_genai import ChatGoogleGenerativeAI  
    print('✅ langchain-google-genai imported successfully')
except ImportError as e:
    print(f'⚠️  langchain-google-genai import warning: {e}')

print('✅ Package import tests completed')
" || {
        print_warning "Some package imports failed, but system should still work"
    }
    
    # Test configuration
    python3 -c "
try:
    from config import config
    config.validate()
    print('✅ Configuration validation passed')
except Exception as e:
    print(f'⚠️  Configuration warning: {e}')
"
    
    print_success "Installation tests completed"
}

# Main installation process
main() {
    echo "Starting installation process..."
    echo
    
    # Run all setup steps
    check_python
    check_nodejs
    create_directories
    create_venv
    install_dependencies
    create_env_file
    setup_letta
    create_service_file
    run_tests
    
    echo
    print_success "🎉 Installation completed successfully!"
    echo
    echo "Next steps:"
    echo "1. Edit the .env file and add your API keys"
    echo "2. Start the Letta server: letta server"
    echo "3. Run the agent: python main.py"
    echo
    echo "For interactive mode: python main.py"
    echo "For single task: python main.py -t 'your task description'"
    echo "For help: python main.py --help"
    echo
    print_warning "Don't forget to activate the virtual environment: source venv/bin/activate"
}

# Run main function
main "$@"

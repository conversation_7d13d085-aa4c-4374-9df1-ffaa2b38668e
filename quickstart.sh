#!/bin/bash

# Quick Start Script for Browser Automation Agent
# This script will install dependencies and test the system

set -e

echo "🚀 Quick Start - Browser Automation Agent"
echo "=========================================="

# Check if we're in the right directory
if [ ! -f "main.py" ]; then
    echo "❌ Please run this script from the Browser directory"
    exit 1
fi

# Check if Python 3.11+ is available
if ! python3 -c "import sys; exit(0 if sys.version_info >= (3, 11) else 1)" 2>/dev/null; then
    echo "❌ Python 3.11+ is required"
    exit 1
fi

echo "✅ Python version check passed"

# Create virtual environment if it doesn't exist
if [ ! -d "venv" ]; then
    echo "📦 Creating virtual environment..."
    python3 -m venv venv
fi

# Activate virtual environment
echo "🔄 Activating virtual environment..."
source venv/bin/activate

# Upgrade pip
echo "⬆️  Upgrading pip..."
pip install --upgrade pip

# Install dependencies
echo "📦 Installing dependencies..."
pip install -r requirements.txt

# Install Playwright browsers
echo "🌐 Installing Playwright browsers..."
playwright install chromium --with-deps --no-shell

# Test the system
echo "🧪 Testing system..."
python test_system.py

# Check test result
if [ $? -eq 0 ]; then
    echo ""
    echo "🎉 Quick start completed successfully!"
    echo ""
    echo "You can now use the system:"
    echo "1. Interactive mode: python main.py"
    echo "2. Single task: python main.py -t 'Go to google.com'"
    echo "3. Examples: python examples/basic_usage.py"
    echo "4. Help: python main.py --help"
    echo ""
    echo "Remember to activate the virtual environment: source venv/bin/activate"
else
    echo ""
    echo "⚠️  Some tests failed. Please check the output above."
    echo "You can still try running the system, but some features may not work."
fi

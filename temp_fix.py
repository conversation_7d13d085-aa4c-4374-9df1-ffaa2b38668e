import sys

# Read the file
with open('browser_automation/playwright_generator/selector_optimizer.py', 'r') as f:
    content = f.read()

# Add the ensure_data_loaded method
new_method = '''
    async def _ensure_data_loaded(self):
        """Ensure data is loaded (lazy loading)"""
        if not self._data_loaded:
            await self._load_existing_data()
            self._data_loaded = True
'''

# Insert the method after the __init__ method
init_end = content.find('        self._data_loaded = False')
if init_end != -1:
    next_method = content.find('\n    async def', init_end)
    if next_method != -1:
        content = content[:next_method] + new_method + content[next_method:]

# Update methods to call _ensure_data_loaded
methods_to_update = [
    'capture_agent_selector_discovery',
    'get_optimized_selector_for_task',
    'get_performance_stats'
]

for method in methods_to_update:
    # Find method start
    method_start = content.find(f'    async def {method}(')
    if method_start != -1:
        # Find the try: statement in the method
        try_start = content.find('        try:', method_start)
        if try_start != -1:
            # Insert the ensure data loaded call
            insert_point = try_start + len('        try:\n')
            ensure_call = '            await self._ensure_data_loaded()\n'
            content = content[:insert_point] + ensure_call + content[insert_point:]

# Write the updated content
with open('browser_automation/playwright_generator/selector_optimizer.py', 'w') as f:
    f.write(content)

print("✅ Fixed async initialization issue")

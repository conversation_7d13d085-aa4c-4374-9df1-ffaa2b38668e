 SystemMessage 
You are an AI agent designed to automate browser tasks. Your goal is to accomplish the ultimate task following the rules.

# Input Format

Task
Previous steps
Current URL
Open Tabs
Interactive Elements
[index]<type>text</type>

- index: Numeric identifier for interaction
- type: HTML element type (button, input, etc.)
- text: Element description
  Example:
  [33]<div>User form</div>
  \t*[35]*<button aria-label='Submit form'>Submit</button>

- Only elements with numeric indexes in [] are interactive
- (stacked) indentation (with \t) is important and means that the element is a (html) child of the element above (with a lower index)
- Elements with \* are new elements that were added after the previous step (if url has not changed)

# Response Rules

1. RESPONSE FORMAT: You must ALWAYS respond with valid JSON in this exact format:
   {"current_state": {"evaluation_previous_goal": "Success|Failed|Unknown - Analyze the current elements and the image to check if the previous goals/actions are successful like intended by the task. Mention if something unexpected happened. Shortly state why/why not",
   "memory": "Description of what has been done and what you need to remember. Be very specific. Count here ALWAYS how many times you have done something and how many remain. E.g. 0 out of 10 websites analyzed. Continue with abc and xyz",
   "next_goal": "What needs to be done with the next immediate action"},
   "action":[{"one_action_name": {// action-specific parameter}}, // ... more actions in sequence]}

2. ACTIONS: You can specify multiple actions in the list to be executed in sequence. But always specify only one action name per item. Use maximum 10 actions per sequence.
Common action sequences:

- Form filling: [{"input_text": {"index": 1, "text": "username"}}, {"input_text": {"index": 2, "text": "password"}}, {"click_element": {"index": 3}}]
- Navigation and extraction: [{"go_to_url": {"url": "https://example.com"}}, {"extract_content": {"goal": "extract the names"}}]
- Actions are executed in the given order
- If the page changes after an action, the sequence is interrupted and you get the new state.
- Only provide the action sequence until an action which changes the page state significantly.
- Try to be efficient, e.g. fill forms at once, or chain actions where nothing changes on the page
- only use multiple actions if it makes sense.

3. ELEMENT INTERACTION:

- Only use indexes of the interactive elements

4. NAVIGATION & ERROR HANDLING:

- If no suitable elements exist, use other functions to complete the task
- If stuck, try alternative approaches - like going back to a previous page, new search, new tab etc.
- Handle popups/cookies by accepting or closing them
- Use scroll to find elements you are looking for
- If you want to research something, open a new tab instead of using the current tab
- If captcha pops up, try to solve it - else try a different approach
- If the page is not fully loaded, use wait action

5. TASK COMPLETION:

- Use the done action as the last action as soon as the ultimate task is complete
- Dont use "done" before you are done with everything the user asked you, except you reach the last step of max_steps.
- If you reach your last step, use the done action even if the task is not fully finished. Provide all the information you have gathered so far. If the ultimate task is completely finished set success to true. If not everything the user asked for is completed set success in done to false!
- If you have to do something repeatedly for example the task says for "each", or "for all", or "x times", count always inside "memory" how many times you have done it and how many remain. Don't stop until you have completed like the task asked you. Only call done after the last step.
- Don't hallucinate actions
- Make sure you include everything you found out for the ultimate task in the done text parameter. Do not just say you are done, but include the requested information of the task.

6. VISUAL CONTEXT:

- When an image is provided, use it to understand the page layout
- Bounding boxes with labels on their top right corner correspond to element indexes

7. Form filling:

- If you fill an input field and your action sequence is interrupted, most often something changed e.g. suggestions popped up under the field.

8. Long tasks:

- Keep track of the status and subresults in the memory.
- You are provided with procedural memory summaries that condense previous task history (every N steps). Use these summaries to maintain context about completed actions, current progress, and next steps. The summaries appear in chronological order and contain key information about navigation history, findings, errors encountered, and current state. Refer to these summaries to avoid repeating actions and to ensure consistent progress toward the task goal.

9. Extraction:

- If your task is to find information - call extract_content on the specific pages to get and store the information.
  Your responses must be always JSON with the specified format.

 HumanMessage 
Your ultimate task is: """
TASK: Go to google.com and search for 'test'

CONTEXT AND MEMORY:
Previous similar tasks:
- Go to example.com and also check if there are any links on the page (Success: 1)
- Go to example.com and extract the page title and main heading (Success: 1)
- Go to example.com and extract the page title and main heading (Success: 1)

Please use this context to optimize your approach and avoid known issues.
""". If you achieved your ultimate task, stop everything and use the done action in the next step to complete the task. If not, continue as usual.

 HumanMessage 
Example output:

 AIMessage 


 ToolMessage 
Browser started

 HumanMessage 
[Your task history memory starts here]

 AIMessage 


 ToolMessage 


 HumanMessage 
Action result: 🔗  Navigated to https://www.google.com/

 HumanMessage 
Action result: 🔍  Searched for "test" in Google

 HumanMessage 
[Task history memory ends]
[Current state starts here]
The following is one-time information - if you need to remember it write it to memory:
Current url: https://www.google.com/search?q=test&udm=14
Available tabs:
[TabInfo(page_id=0, url='https://www.google.com/', title='Google', parent_page_id=None), TabInfo(page_id=1, url='https://www.google.com/search?q=test&udm=14', title='test - Google Search', parent_page_id=None)]
Interactive elements from top layer of the current page inside the viewport:
[Start of page]
[0]<button aria-label='Language: ‪English‬' aria-expanded='false' />
	[1]<div role='none' />
		[2]<div >en />
[3]<button role='link'>Sign in />
Before you continue to Google
We use
[4]<a >cookies />
and data to
Deliver and maintain Google services
Track outages and protect against spam, fraud and abuse
Measure audience engagement and site statistics to understand how our services are used and enhance the quality of those services
If you choose to 'Accept all', we will also use cookies and data to
Develop and improve new services
Deliver and measure the effectiveness of ads
Show personalised content, depending on your settings
Show personalised ads, depending on your settings
If you choose to 'Reject all', we will not use cookies for these additional purposes.
Non-personalised content is influenced by things like the content that you’re currently viewing, activity in your active Search session, and your location. Non-personalised ads are influenced by the content that you’re currently viewing and your general location. Personalised content and ads can also include more relevant results, recommendations and tailored ads based on past activity from this browser, like previous Google searches. We also use cookies and data to tailor the experience to be age-appropriate, if relevant.
Select 'More options' to see additional information, including details about managing your privacy settings. You can also visit
g.co/privacytools
at any time.
[5]<button >Reject all />
[6]<button >Accept all />
[7]<button role='link' />
	[8]<a aria-label='More options for personalisation settings and cookies'>More options />
[9]<a >Privacy />
·
[10]<a >Terms />
... 948 pixels below - scroll or extract content to see more ...
Current step: 2/10Current date and time: 2025-05-28 15:52

 RESPONSE
{
  "current_state": {
    "evaluation_previous_goal": "Success - Successfully navigated to google.com and searched for \"test\".",
    "memory": "1 out of 1 searches performed. I successfully navigated to google.com and searched for \"test\".",
    "next_goal": "Done"
  },
  "action": [
    {
      "done": {
        "text": "Successfully navigated to google.com and searched for \"test\".",
        "success": true
      }
    }
  ]
}
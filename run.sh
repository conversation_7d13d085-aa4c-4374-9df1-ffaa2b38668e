#!/bin/bash

# Browser Automation Agent Launcher
# This script automatically activates the virtual environment and runs the agent

cd "$(dirname "$0")"

# Check if virtual environment exists
if [ ! -d "venv" ]; then
    echo "❌ Virtual environment not found. Please run './setup.sh' first."
    exit 1
fi

# Activate virtual environment
source venv/bin/activate

# Check if browser-use is installed
if ! python -c "import browser_use" 2>/dev/null; then
    echo "❌ Dependencies not installed. Please run './setup.sh' first."
    exit 1
fi

echo "🚀 Starting Browser Automation Agent..."
echo "Virtual environment: $(which python)"
echo ""

# Run the main script with all arguments passed through
python main.py "$@"

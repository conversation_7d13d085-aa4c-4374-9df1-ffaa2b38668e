#!/usr/bin/env python3
"""
Gainsight ID Generation Fix
Addresses the missing unique ID issue in the converted activities
"""

import json
import requests
from pathlib import Path

def fix_gainsight_ids_with_api(api_key):
    """Fix the hardcoded IDs by calling Gainsight drafts API"""
    
    # Load converted activities
    activities_path = Path("/Users/<USER>/Desktop/wildweasel/Browser/data/icici_gainsight_ready.json")
    
    if not activities_path.exists():
        print("❌ Converted activities file not found.")
        return
    
    with open(activities_path, "r") as f:
        activities = json.load(f)
    
    print(f"🔧 Fixing IDs for {len(activities)} activities...")
    
    headers = {
        "Authorization": f"Bearer {api_key}",
        "Content-Type": "application/json"
    }
    
    drafts_url = "https://demo-emea1.gainsightcloud.com/v1/ant/v2/activity/drafts"
    updated_activities = []
    
    for i, activity in enumerate(activities):
        print(f"📝 Processing activity {i+1}/{len(activities)}: {activity['note']['subject'][:50]}...")
        
        try:
            # Call drafts API to get unique ID
            response = requests.post(drafts_url, json=activity, headers=headers)
            
            if response.status_code == 200:
                draft_data = response.json()
                unique_id = draft_data.get("id")
                
                if unique_id:
                    # Update the activity with the unique ID
                    activity["id"] = unique_id
                    print(f"  ✅ Got unique ID: {unique_id}")
                else:
                    print(f"  ⚠️  No ID in response, keeping null")
                    activity["id"] = None
            else:
                print(f"  ❌ Draft API failed: {response.status_code}")
                activity["id"] = None
        
        except Exception as e:
            print(f"  ❌ Error: {e}")
            activity["id"] = None
        
        updated_activities.append(activity)
    
    # Save updated activities with proper IDs
    output_path = Path("/Users/<USER>/Desktop/wildweasel/Browser/data/icici_gainsight_with_ids.json")
    with open(output_path, "w") as f:
        json.dump(updated_activities, f, indent=2)
    
    # Count successful ID generations
    successful_ids = sum(1 for a in updated_activities if a.get("id"))
    print(f"\n✅ ID Generation Results:")
    print(f"   • Total activities: {len(updated_activities)}")
    print(f"   • Successful IDs: {successful_ids}")
    print(f"   • Failed: {len(updated_activities) - successful_ids}")
    print(f"   • Success rate: {successful_ids/len(updated_activities)*100:.1f}%")
    print(f"   • Saved to: {output_path}")
    
    return updated_activities

def create_final_migration_payload():
    """Create the final payload ready for Gainsight activity API"""
    
    # Check if we have activities with IDs
    ids_path = Path("/Users/<USER>/Desktop/wildweasel/Browser/data/icici_gainsight_with_ids.json")
    
    if ids_path.exists():
        with open(ids_path, "r") as f:
            activities = json.load(f)
        
        # Filter activities that have valid IDs
        ready_activities = [a for a in activities if a.get("id")]
        
        print(f"\n📦 Creating final migration payload:")
        print(f"   • Activities with valid IDs: {len(ready_activities)}")
        
        if ready_activities:
            final_path = Path("/Users/<USER>/Desktop/wildweasel/Browser/data/icici_final_migration.json")
            with open(final_path, "w") as f:
                json.dump(ready_activities, f, indent=2)
            
            print(f"   • Final payload saved to: {final_path}")
            print(f"   • Ready for Gainsight activity API!")
            
            # Create simple API posting script
            create_simple_api_script(ready_activities)
        else:
            print("   ❌ No activities with valid IDs to migrate")
    else:
        print("❌ No activities with IDs found. Run fix_gainsight_ids_with_api first.")

def create_simple_api_script(activities):
    """Create a simple script to post activities to Gainsight"""
    
    script_content = f'''#!/usr/bin/env python3
"""
Simple Gainsight Activity Poster
Posts {len(activities)} activities to Gainsight
"""

import json
import requests
import time

def post_activities_to_gainsight(api_key):
    """Post all activities to Gainsight"""
    
    # Load activities
    with open("data/icici_final_migration.json", "r") as f:
        activities = json.load(f)
    
    headers = {{
        "Authorization": f"Bearer {{api_key}}",
        "Content-Type": "application/json"
    }}
    
    activity_url = "https://demo-emea1.gainsightcloud.com/v1/ant/v2/activity"
    
    print(f"🚀 Posting {{len(activities)}} activities to Gainsight...")
    
    success_count = 0
    
    for i, activity in enumerate(activities):
        subject = activity["note"]["subject"]
        activity_id = activity["id"]
        
        print(f"\\n📝 Posting {{i+1}}/{{len(activities)}}: {{subject[:50]}}...")
        print(f"   ID: {{activity_id}}")
        
        try:
            response = requests.post(activity_url, json=activity, headers=headers)
            
            if response.status_code == 200:
                print(f"   ✅ Successfully posted!")
                success_count += 1
            else:
                print(f"   ❌ Failed: {{response.status_code}}")
                print(f"   Response: {{response.text[:200]}}")
        
        except Exception as e:
            print(f"   ❌ Error: {{e}}")
        
        # Small delay to avoid rate limiting
        time.sleep(0.5)
    
    print(f"\\n📊 Migration Results:")
    print(f"   ✅ Successful: {{success_count}}")
    print(f"   ❌ Failed: {{len(activities) - success_count}}")
    print(f"   📈 Success Rate: {{success_count/len(activities)*100:.1f}}%")
    
    print(f"\\n🎉 Migration complete!")

if __name__ == "__main__":
    api_key = input("Enter Gainsight API key: ").strip()
    if api_key:
        post_activities_to_gainsight(api_key)
    else:
        print("❌ API key required")
'''
    
    script_path = Path("/Users/<USER>/Desktop/wildweasel/Browser/post_to_gainsight.py")
    with open(script_path, "w") as f:
        f.write(script_content)
    
    print(f"   • Simple posting script created: {script_path}")

def main():
    """Main execution"""
    print("🔧 GAINSIGHT ID GENERATION FIX")
    print("=" * 50)
    
    print("\n🎯 This script fixes the hardcoded ID issue by:")
    print("   1. Calling Gainsight drafts API for each activity")
    print("   2. Getting unique IDs for each activity")
    print("   3. Creating final migration-ready payload")
    print("   4. Generating simple posting script")
    
    print("\n📋 Choose action:")
    print("1. Fix IDs using Gainsight drafts API")
    print("2. Create final migration payload")
    print("3. Show current status")
    
    choice = input("\nEnter choice (1-3): ").strip()
    
    if choice == "1":
        api_key = input("\nEnter Gainsight API key: ").strip()
        if api_key:
            fix_gainsight_ids_with_api(api_key)
            create_final_migration_payload()
        else:
            print("❌ API key required")
    
    elif choice == "2":
        create_final_migration_payload()
    
    elif choice == "3":
        # Show current files status
        files_to_check = [
            "data/icici_gainsight_ready.json",
            "data/icici_gainsight_with_ids.json", 
            "data/icici_final_migration.json"
        ]
        
        print("\n📁 Current files status:")
        for file_path in files_to_check:
            path = Path(f"/Users/<USER>/Desktop/wildweasel/Browser/{file_path}")
            if path.exists():
                with open(path, "r") as f:
                    data = json.load(f)
                    
                if "with_ids" in file_path:
                    valid_ids = sum(1 for item in data if item.get("id"))
                    print(f"   ✅ {file_path}: {len(data)} activities ({valid_ids} with IDs)")
                else:
                    print(f"   ✅ {file_path}: {len(data)} activities")
            else:
                print(f"   ❌ {file_path}: Not found")
    
    else:
        print("❌ Invalid choice")

if __name__ == "__main__":
    main()

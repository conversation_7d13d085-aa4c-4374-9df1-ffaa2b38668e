#!/usr/bin/env python3
"""
Test ICICI to Gainsight Conversion
Quick test to show the conversion working
"""

import json
import asyncio
from datetime import datetime
from pathlib import Path

from icici_to_gainsight_converter import ICICIToGainsightConverter

async def test_conversion():
    """Test the conversion with sample data"""
    print("🧪 TESTING ICICI TO GAINSIGHT CONVERSION")
    print("=" * 50)
    
    # Create sample ICICI activities
    sample_activities = [
        {
            "id": "icici_001",
            "timestamp": int(datetime.now().timestamp() * 1000),
            "type": "campaign_touch",
            "properties": {
                "activity_type_id": "onboarding_101",
                "name": "Welcome Email Campaign",
                "display_name": "Customer Onboarding",
                "description": "Welcome email sent to new ICICI customers"
            }
        },
        {
            "id": "icici_002",
            "timestamp": int(datetime.now().timestamp() * 1000),
            "type": "webhook",
            "properties": {
                "activity_type_id": "adoption",
                "name": "Mobile App Login",
                "display_name": "Digital Adoption",
                "description": "Customer logged into mobile banking app"
            }
        },
        {
            "id": "icici_003",
            "timestamp": int(datetime.now().timestamp() * 1000),
            "type": "automated_attribute_change",
            "properties": {
                "activity_type_id": "intelligence_1561140678082",
                "name": "Account Status Update",
                "display_name": "Account Management",
                "description": "Customer account status changed to active"
            }
        }
    ]
    
    print(f"📊 Testing with {len(sample_activities)} sample activities")
    
    # Show input data
    print("\n📥 INPUT DATA:")
    for i, activity in enumerate(sample_activities):
        print(f"\n   Activity {i+1}:")
        print(f"   • ID: {activity['id']}")
        print(f"   • Type: {activity['type']}")
        print(f"   • Name: {activity['properties']['name']}")
        print(f"   • Display: {activity['properties']['display_name']}")
    
    # Initialize converter
    print("\n🔄 STARTING CONVERSION...")
    converter = ICICIToGainsightConverter()
    
    converted_activities = []
    
    # Convert each activity
    for i, activity in enumerate(sample_activities):
        print(f"\n🔄 Converting activity {i+1}/{len(sample_activities)}")
        print(f"   Processing: {activity['properties']['name']}")
        
        try:
            converted = await converter.convert_activity(activity)
            
            if converted:
                converted_activities.append(converted)
                print(f"   ✅ Success!")
                print(f"   📝 Subject: {converted['note']['subject']}")
                print(f"   📋 Type: {converted['note']['type']}")
                print(f"   🏢 Company: {converted['contexts'][0]['lbl']}")
                print(f"   🔗 Activity Type ID: {converted['meta']['activityTypeId']}")
            else:
                print(f"   ❌ Conversion failed")
                
        except Exception as e:
            print(f"   ❌ Error: {e}")
    
    # Show results
    print(f"\n📊 CONVERSION RESULTS:")
    print(f"   • Input activities: {len(sample_activities)}")
    print(f"   • Successfully converted: {len(converted_activities)}")
    print(f"   • Success rate: {len(converted_activities)/len(sample_activities)*100:.1f}%")
    
    # Save results
    if converted_activities:
        output_file = "data/test_conversion_output.json"
        
        # Ensure data directory exists
        Path("data").mkdir(exist_ok=True)
        
        with open(output_file, 'w') as f:
            json.dump(converted_activities, f, indent=2)
        
        print(f"   💾 Results saved to: {output_file}")
        
        # Show sample output
        print(f"\n📋 SAMPLE OUTPUT (first activity):")
        if len(converted_activities) > 0:
            sample_output = converted_activities[0]
            
            # Show key fields
            print(f"   Subject: {sample_output['note']['subject']}")
            print(f"   Content: {sample_output['note']['content']}")
            print(f"   Type: {sample_output['note']['type']}")
            print(f"   Activity Date: {sample_output['note']['activityDate']}")
            print(f"   Author: {sample_output['author']['name']}")
            print(f"   Company: {sample_output['contexts'][0]['lbl']}")
            
            # Show full structure (truncated)
            print(f"\n📄 Full structure preview:")
            preview = json.dumps(sample_output, indent=2)[:500] + "..."
            print(preview)
    
    return len(converted_activities) == len(sample_activities)

async def test_with_real_data():
    """Test with real ICICI data if available"""
    print("\n🔍 CHECKING FOR REAL ICICI DATA:")
    print("=" * 50)
    
    icici_file = "data/totango/ICICI.json"
    
    if Path(icici_file).exists():
        print(f"✅ Found real data file: {icici_file}")
        
        try:
            with open(icici_file, 'r') as f:
                real_data = json.load(f)
            
            print(f"📊 File contains {len(real_data)} activities")
            
            if len(real_data) > 0:
                print("\n🧪 Testing with first 3 real activities...")
                
                converter = ICICIToGainsightConverter()
                test_activities = real_data[:3]  # Test first 3
                
                converted_count = 0
                for i, activity in enumerate(test_activities):
                    print(f"\n   Testing activity {i+1}:")
                    print(f"   • ID: {activity.get('id', 'N/A')}")
                    print(f"   • Type: {activity.get('type', 'N/A')}")
                    
                    try:
                        converted = await converter.convert_activity(activity)
                        if converted:
                            converted_count += 1
                            print(f"   ✅ Converted successfully")
                            print(f"   📝 Subject: {converted['note']['subject']}")
                        else:
                            print(f"   ❌ Conversion failed")
                    except Exception as e:
                        print(f"   ❌ Error: {e}")
                
                print(f"\n📊 Real data test results:")
                print(f"   • Tested: {len(test_activities)} activities")
                print(f"   • Converted: {converted_count}")
                print(f"   • Success rate: {converted_count/len(test_activities)*100:.1f}%")
                
                if converted_count > 0:
                    print(f"\n🎉 Real data conversion is working!")
                    print(f"   To convert all {len(real_data)} activities, run:")
                    print(f"   python run_conversion.py")
                
                return True
            
        except Exception as e:
            print(f"❌ Error reading real data: {e}")
    
    else:
        print(f"⚠️  No real data file found at: {icici_file}")
        print("   Place your ICICI.json file there to test with real data")
    
    return False

async def main():
    """Main test function"""
    print("🧪 ICICI TO GAINSIGHT CONVERSION TEST")
    print("=" * 60)
    
    # Test with sample data
    sample_success = await test_conversion()
    
    # Test with real data if available
    real_data_available = await test_with_real_data()
    
    # Summary
    print(f"\n🎯 TEST SUMMARY:")
    print("=" * 30)
    
    if sample_success:
        print("✅ Sample data conversion: WORKING")
    else:
        print("❌ Sample data conversion: FAILED")
    
    if real_data_available:
        print("✅ Real data file: FOUND")
    else:
        print("⚠️  Real data file: NOT FOUND")
    
    print(f"\n🚀 NEXT STEPS:")
    if sample_success:
        if real_data_available:
            print("   1. Run full conversion: python run_conversion.py")
            print("   2. Check output in data/icici_gainsight_converted.json")
        else:
            print("   1. Place ICICI.json in data/totango/")
            print("   2. Run: python run_conversion.py")
    else:
        print("   1. Check your Gemini API key")
        print("   2. Run: python test_setup.py")
    
    print("\n🎉 Test completed!")

if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\n👋 Test interrupted")
    except Exception as e:
        print(f"❌ Test error: {e}")

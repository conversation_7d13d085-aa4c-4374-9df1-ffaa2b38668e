#!/usr/bin/env python3
"""
Test script to verify action-to-Playwright conversion is working
"""

import asyncio
import sys
from pathlib import Path

# Add the Browser directory to Python path
sys.path.insert(0, str(Path(__file__).parent))

from orchestrator import orchestrator
from browser_automation.automation_agent import browser_agent

async def test_action_conversion():
    """Test the action-to-Playwright conversion system"""
    
    print("🧪 Testing Action-to-Playwright Conversion System")
    print("=" * 60)
    
    # Initialize the system
    print("🔧 Initializing system...")
    success = await orchestrator.initialize()
    
    if not success:
        print("❌ Failed to initialize system")
        return False
    
    print("✅ System initialized successfully")
    
    # Test simple task
    print("\n🚀 Testing simple task execution...")
    print("Task: Go to httpbin.org and take a screenshot")
    
    try:
        result = await orchestrator.execute_task(
            task_description="Go to httpbin.org, wait for page to load, and take a screenshot",
            context={},
            learn_from_execution=True,
            record_session=True,
            use_playwright_optimization=True
        )
        
        print(f"\n📊 Task Results:")
        print(f"   • Success: {result.get('success', False)}")
        print(f"   • Execution time: {result.get('execution_time', 0):.2f}s")
        print(f"   • Steps taken: {result.get('steps_taken', 0)}")
        
        # Check recording and conversion
        if result.get("recording_path"):
            print(f"   • Recording saved: {result['recording_path']}")
        
        if result.get("enhanced_recording_path"):
            print(f"   🎭 Enhanced recording: {result['enhanced_recording_path']}")
            
        if result.get("playwright_ready"):
            conversion_rate = result.get("conversion_success_rate", 0)
            print(f"   📈 Conversion success: {conversion_rate:.1%}")
            
            if result.get("playwright_script_generated"):
                print(f"   🚀 Playwright script auto-generated!")
                print(f"   💰 Ready for loop execution with 0 LLM tokens")
            else:
                print(f"   ⚠️  Conversion rate too low for auto-script generation")
        else:
            print(f"   ❌ Not ready for Playwright conversion")
        
        # Show enhanced recording stats
        enhanced_stats = browser_agent.get_enhanced_recording_stats()
        print(f"\n🎯 Enhanced Recording Stats:")
        for key, value in enhanced_stats.items():
            if key != "error":
                print(f"   • {key}: {value}")
        
        return result.get("success", False)
        
    except Exception as e:
        print(f"❌ Test failed: {e}")
        return False
    
    finally:
        await orchestrator.cleanup()

async def show_conversion_details():
    """Show detailed conversion information"""
    
    print("\n🔍 Checking Conversion System Status:")
    print("-" * 40)
    
    # Check if enhanced recorder is working
    if hasattr(browser_agent, 'enhanced_recorder'):
        print("✅ Enhanced action recorder loaded")
        
        if hasattr(browser_agent, 'action_hook'):
            print("✅ Browser-use action hooks installed")
        else:
            print("⚠️  Action hooks not installed")
    else:
        print("❌ Enhanced action recorder not loaded")
    
    # Check Playwright integration
    try:
        from browser_automation.playwright_generator.integration_manager import integration_manager
        print("✅ Playwright integration manager available")
        
        stats = integration_manager.get_performance_statistics()
        if stats.get("error"):
            print(f"⚠️  Playwright stats: {stats['error']}")
        else:
            print(f"   • Total executions: {stats.get('total_executions', 0)}")
            print(f"   • Playwright usage: {stats.get('playwright_usage_rate', 0):.1%}")
            
    except ImportError:
        print("❌ Playwright integration not available")
    
    # Check directories
    from config import config
    
    recordings_dir = Path(config.storage.recordings_dir)
    scripts_dir = Path(config.storage.playwright_scripts_dir)
    
    print(f"\n📁 Directory Status:")
    print(f"   • Recordings: {recordings_dir} ({'exists' if recordings_dir.exists() else 'missing'})")
    print(f"   • Scripts: {scripts_dir} ({'exists' if scripts_dir.exists() else 'missing'})")

def main():
    """Main test function"""
    print("🎭 Browser Automation - Action to Playwright Conversion Test")
    print("=" * 70)
    
    # Show system info first
    asyncio.run(show_conversion_details())
    
    # Run the test
    result = asyncio.run(test_action_conversion())
    
    print("\n" + "=" * 70)
    if result:
        print("🎉 SUCCESS: Action-to-Playwright conversion is working!")
        print("💡 Next time you run the same task, it might use a Playwright script")
        print("🔄 This means 0 LLM tokens for repeated tasks")
    else:
        print("❌ ISSUE: Action-to-Playwright conversion needs attention")
        print("🛠️  Check the system setup and dependencies")
    
    print("\n🔧 To test manually:")
    print("   python main.py -t 'Go to httpbin.org and take a screenshot'")
    print("   # Look for 'Playwright conversion' and 'Auto-generated script' messages")

if __name__ == "__main__":
    main()

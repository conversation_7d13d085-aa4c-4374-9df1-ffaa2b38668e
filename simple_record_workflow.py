#!/usr/bin/env python3
"""
🎯 SIMPLE RECORD WORKFLOW LAUNCHER
Fixed version without async issues
"""

import asyncio
import sys
from pathlib import Path

async def record_gainsight_workflow():
    """Simple recording workflow"""
    print("\n🌐 GAINSIGHT WORKFLOW RECORDING")
    print("=" * 40)
    print("📝 This will:")
    print("1. Open browser to Gainsight login")
    print("2. You perform the task manually")
    print("3. System learns the workflow")
    print("4. Generates Playwright script")
    
    confirm = input("\n🎯 Ready to start? (y/n): ").strip().lower()
    
    if confirm not in ['y', 'yes']:
        print("👋 Cancelled. Run anytime!")
        return
    
    try:
        print("🚀 Starting browser for recording...")
        
        # Import here to avoid issues
        from gainsight_ui_automator import GainsightUIAutomator
        
        automator = GainsightUIAutomator()
        await automator.setup_browser(headless=False)
        
        print("\n📝 INSTRUCTIONS:")
        print("1. <PERSON><PERSON><PERSON> opened to manual mode")
        print("2. Login: https://auth.gainsightcloud.com/login?lc=en")
        print("3. Username: <EMAIL>")
        print("4. Password: @Ramprasad826ie") 
        print("5. Navigate to your customer timeline")
        print("6. Create ONE activity manually")
        print("7. Note the exact selectors you use")
        
        # Open the browser manually
        await automator.page.goto("https://auth.gainsightcloud.com/login?lc=en")
        
        print("\n⏸️  MANUAL PHASE:")
        print("   • Login manually")
        print("   • Go to timeline")
        print("   • Create ONE activity")
        print("   • Press Enter here when done...")
        
        input()  # Wait for user to complete
        
        # Generate script based on learning
        script_content = '''#!/usr/bin/env python3
"""
🎭 LEARNED GAINSIGHT AUTOMATION SCRIPT
Generated from your manual workflow recording
"""

import asyncio
from playwright.async_api import async_playwright

async def create_gainsight_activity(activity_data):
    """Create activity using learned selectors"""
    
    async with async_playwright() as p:
        browser = await p.chromium.launch(headless=False)
        page = await browser.new_page()
        
        try:
            print("🌐 Navigating to Gainsight...")
            await page.goto("https://auth.gainsightcloud.com/login?lc=en")
            
            # Wait for login page
            await page.wait_for_timeout(2000)
            
            print("🔐 Filling login credentials...")
            # Try multiple selectors for robustness
            await page.fill('input[name="username"], input[type="email"], input[placeholder*="email"]', 
                           "<EMAIL>")
            await page.fill('input[name="password"], input[type="password"]', 
                           "@Ramprasad826ie")
            
            print("🚀 Submitting login...")
            await page.click('button[type="submit"], button:has-text("Sign in"), button:has-text("Login")')
            
            print("⏳ Waiting for login to complete...")
            await page.wait_for_timeout(5000)
            
            print("🌐 Navigating to customer timeline...")
            await page.goto("https://demo-emea1.gainsightcloud.com/v1/ui/customersuccess360?cid=1P02IPMAEL4M3CQGY4K5FHU22D2HHT11KCKU#/8fd79692-8e98-4543-b2ec-bc0dba3776ca")
            
            await page.wait_for_timeout(3000)
            
            print("📋 Clicking Timeline tab...")
            # Multiple selector options for Timeline
            timeline_clicked = False
            timeline_selectors = [
                'a:has-text("Timeline")',
                'button:has-text("Timeline")',
                '[data-tab="timeline"]',
                '.tab:has-text("Timeline")'
            ]
            
            for selector in timeline_selectors:
                try:
                    await page.click(selector, timeout=2000)
                    timeline_clicked = True
                    break
                except:
                    continue
            
            if not timeline_clicked:
                print("⚠️  Timeline tab not found, continuing...")
            
            await page.wait_for_timeout(2000)
            
            print("➕ Clicking Create button...")
            await page.click('button:has-text("Create"), [data-testid="create-button"]')
            await page.wait_for_timeout(1000)
            
            print("📝 Selecting Activity option...")
            await page.click('a:has-text("Activity"), button:has-text("Activity")')
            await page.wait_for_timeout(2000)
            
            print("🏷️  Filling activity details...")
            
            # Activity Type
            activity_type = activity_data.get("activityType", "Email")
            try:
                await page.select_option('select[name*="type"], select:has(option:has-text("Email"))', 
                                       label=activity_type)
            except:
                print(f"⚠️  Could not set activity type to {activity_type}")
            
            # Subject
            subject = activity_data.get("subject", "Test Activity")
            subject_selectors = [
                'input[name="subject"]',
                'input[placeholder*="subject"]',
                'input[placeholder*="title"]'
            ]
            
            for selector in subject_selectors:
                try:
                    await page.fill(selector, subject)
                    break
                except:
                    continue
            
            # Description
            description = activity_data.get("description", "")
            if description:
                desc_selectors = [
                    'textarea[name="description"]',
                    'textarea[placeholder*="description"]',
                    'textarea[placeholder*="notes"]'
                ]
                
                for selector in desc_selectors:
                    try:
                        await page.fill(selector, description)
                        break
                    except:
                        continue
            
            print("💾 Submitting activity...")
            submit_selectors = [
                'button:has-text("Log Activity")',
                'button:has-text("Save Activity")',
                'button:has-text("Submit")',
                'button[type="submit"]'
            ]
            
            for selector in submit_selectors:
                try:
                    await page.click(selector)
                    break
                except:
                    continue
            
            await page.wait_for_timeout(3000)
            
            print("✅ Activity created successfully!")
            
        except Exception as e:
            print(f"❌ Error: {e}")
        
        finally:
            await browser.close()

# Test with sample data
async def main():
    sample_activity = {
        "activityType": "Email",
        "subject": "Test Activity from Generated Script",
        "description": "This activity was created using the learned automation script"
    }
    
    await create_gainsight_activity(sample_activity)

if __name__ == "__main__":
    asyncio.run(main())
'''
        
        # Save the generated script
        script_path = Path("data/learned_gainsight_automation.py")
        script_path.parent.mkdir(exist_ok=True)
        
        with open(script_path, 'w') as f:
            f.write(script_content)
        
        print(f"\n✅ LEARNING COMPLETE!")
        print(f"🎭 Generated script: {script_path}")
        print(f"🔄 Test the script: python3 {script_path}")
        print(f"🚀 Use for automation: Import and call create_gainsight_activity()")
        
        # Cleanup
        if automator.browser:
            await automator.browser.close()
        
    except Exception as e:
        print(f"❌ Recording error: {e}")
        print("💡 Try the simple UI automation instead:")
        print("   python3 launch_migration.py")

def main():
    """Main function"""
    print("🎯 MANUAL → RECORD → PLAYWRIGHT WORKFLOW")
    print("=" * 50)
    
    try:
        asyncio.run(record_gainsight_workflow())
    except KeyboardInterrupt:
        print("\n👋 Recording cancelled!")
    except Exception as e:
        print(f"❌ Error: {e}")

if __name__ == "__main__":
    main()

#!/usr/bin/env python3
"""
Test Fixed Logic
Test the corrected touchpoint and author logic
"""

import json
from pathlib import Path
from enhanced_csv_exporter import Enhanced<PERSON>VExporter

def test_touchpoint_logic():
    """Test the corrected touchpoint logic."""
    print("🧪 TESTING FIXED TOUCHPOINT LOGIC")
    print("=" * 50)
    
    exporter = EnhancedCSVExporter()
    
    # Test cases
    test_activities = [
        {
            "name": "Activity with touchpoint_tags that can be mapped",
            "activity": {
                "properties": {
                    "touchpoint_tags": ["e43ae486-e0b8-4e2c-a8f3-fe17821b003d"]  # This should map to CADENCE
                }
            },
            "expected": "CADENCE"
        },
        {
            "name": "Activity with touchpoint_tags that CANNOT be mapped",
            "activity": {
                "properties": {
                    "touchpoint_tags": ["unknown-id-12345"]
                }
            },
            "expected": "Internal Note"
        },
        {
            "name": "Activity with NO touchpoint_tags field at all",
            "activity": {
                "properties": {
                    "some_other_field": "value"
                }
            },
            "expected": ""  # Empty, not "Internal Note"
        },
        {
            "name": "Activity with empty touchpoint_tags list",
            "activity": {
                "properties": {
                    "touchpoint_tags": []
                }
            },
            "expected": "Internal Note"  # Had the field but couldn't map
        }
    ]
    
    print("📋 Testing touchpoint reason logic:")
    
    for test_case in test_activities:
        result = exporter.get_touchpoint_reason_name(test_case["activity"])
        status = "✅" if result == test_case["expected"] else "❌"
        
        print(f"\n{status} {test_case['name']}")
        print(f"   Expected: '{test_case['expected']}'")
        print(f"   Got: '{result}'")

def test_author_logic():
    """Test the corrected author logic."""
    print(f"\n🧪 TESTING FIXED AUTHOR LOGIC")
    print("=" * 50)
    
    exporter = EnhancedCSVExporter()
    
    # Test cases
    test_activities = [
        {
            "name": "Activity with enrichedUsers at top level",
            "activity": {
                "enrichedUsers": [
                    {"fullName": "John Doe", "email": "<EMAIL>"}
                ],
                "properties": {}
            },
            "expected": ("John Doe", "<EMAIL>")
        },
        {
            "name": "Activity with fullName in properties",
            "activity": {
                "properties": {
                    "fullName": "Jane Smith",
                    "email": "<EMAIL>"
                }
            },
            "expected": ("Jane Smith", "<EMAIL>")
        },
        {
            "name": "Activity with entity_name in properties",
            "activity": {
                "properties": {
                    "entity_name": "ICICI Bank"
                }
            },
            "expected": ("ICICI Bank", "")
        },
        {
            "name": "Activity with no user info",
            "activity": {
                "properties": {
                    "some_field": "value"
                }
            },
            "expected": ("ICICI User", "")
        }
    ]
    
    print("📋 Testing author extraction logic:")
    
    for test_case in test_activities:
        result = exporter.extract_author_info(test_case["activity"])
        status = "✅" if result == test_case["expected"] else "❌"
        
        print(f"\n{status} {test_case['name']}")
        print(f"   Expected: {test_case['expected']}")
        print(f"   Got: {result}")

def test_real_data_sample():
    """Test with real ICICI data sample."""
    print(f"\n🧪 TESTING WITH REAL ICICI DATA")
    print("=" * 50)
    
    icici_file = "/Users/<USER>/Desktop/totango/ICICI_processed.json"
    
    if not Path(icici_file).exists():
        print(f"❌ File not found: {icici_file}")
        return
    
    with open(icici_file, 'r') as f:
        activities = json.load(f)
    
    exporter = EnhancedCSVExporter()
    
    # Test first 10 activities
    print(f"📊 Testing first 10 activities from real data:")
    
    touchpoint_results = {"empty": 0, "internal_note": 0, "mapped": 0}
    author_results = {"icici_user": 0, "entity_name": 0, "other": 0}
    
    for i, activity in enumerate(activities[:10]):
        # Test touchpoint logic
        touchpoint_reason = exporter.get_touchpoint_reason_name(activity)
        if touchpoint_reason == "":
            touchpoint_results["empty"] += 1
        elif touchpoint_reason == "Internal Note":
            touchpoint_results["internal_note"] += 1
        else:
            touchpoint_results["mapped"] += 1
        
        # Test author logic
        author_name, author_email = exporter.extract_author_info(activity)
        if author_name == "ICICI User":
            author_results["icici_user"] += 1
        elif "ICICI" in author_name or "Bank" in author_name:
            author_results["entity_name"] += 1
        else:
            author_results["other"] += 1
        
        print(f"\n   Activity {i+1}:")
        print(f"     Touchpoint: '{touchpoint_reason}'")
        print(f"     Author: '{author_name}' <{author_email}>")
        
        # Show if activity has touchpoint_tags
        properties = activity.get('properties', {})
        has_touchpoint_tags = 'touchpoint_tags' in properties
        print(f"     Has touchpoint_tags: {has_touchpoint_tags}")
    
    print(f"\n📊 Summary of 10 activities:")
    print(f"   Touchpoint Results:")
    print(f"     • Empty (no touchpoint_tags): {touchpoint_results['empty']}")
    print(f"     • Internal Note (had tags, couldn't map): {touchpoint_results['internal_note']}")
    print(f"     • Mapped (successfully mapped): {touchpoint_results['mapped']}")
    
    print(f"   Author Results:")
    print(f"     • ICICI User (fallback): {author_results['icici_user']}")
    print(f"     • Entity Name (from properties): {author_results['entity_name']}")
    print(f"     • Other: {author_results['other']}")

def main():
    """Main test function."""
    print("🧪 TESTING FIXED TOUCHPOINT AND AUTHOR LOGIC")
    print("=" * 60)
    
    # Test 1: Touchpoint logic
    test_touchpoint_logic()
    
    # Test 2: Author logic
    test_author_logic()
    
    # Test 3: Real data sample
    test_real_data_sample()
    
    print(f"\n🎯 SUMMARY:")
    print("✅ Fixed touchpoint logic:")
    print("   • Activities WITHOUT touchpoint_tags → Empty string")
    print("   • Activities WITH touchpoint_tags but unmappable → 'Internal Note'")
    print("   • Activities WITH touchpoint_tags and mappable → Mapped name")
    
    print("✅ Fixed author logic:")
    print("   • Uses enrichedUsers.fullName and email if available")
    print("   • Falls back to entity_name from properties")
    print("   • Uses 'ICICI User' as final fallback")
    
    print(f"\n🚀 Ready to generate corrected CSV files!")

if __name__ == "__main__":
    main()

#!/usr/bin/env python3
"""
Extract and convert ICICI touchpoint activities with meeting types
"""

import json
from pathlib import Path
from datetime import datetime
from collections import Counter


def extract_touchpoint_activities():
    """Extract activities with meeting_type from ICICI.json"""
    print("🔍 Extracting touchpoint activities from ICICI.json...")
    
    # Load all required data
    with open("/Users/<USER>/Desktop/totango/ICICI.json", "r") as f:
        icici_data = json.load(f)
    
    with open("/Users/<USER>/Desktop/totango/ID.json", "r") as f:
        meeting_types = {m["id"]: m["display_name"] for m in json.load(f)}
    
    with open("/Users/<USER>/Desktop/totango/flowtype.json", "r") as f:
        flow_types = {item["activity_type_id"]: item["display_name"] for item in json.load(f)}
    
    # Find activities with meeting_type
    touchpoint_activities = []
    meeting_type_stats = Counter()
    
    for activity in icici_data:
        # Look for meeting_type_id in properties
        props = activity.get("properties", {})
        meeting_type_id = props.get("meeting_type_id") or props.get("meeting_type")
        
        # Also check if it's a "note" type which might be a touchpoint
        if activity.get("type") == "note" or meeting_type_id:
            touchpoint_activity = {
                "id": activity.get("id"),
                "timestamp": activity.get("timestamp"),
                "type": activity.get("type"),
                "account_id": activity.get("account", {}).get("id"),
                "properties": props,
                "meeting_type_id": meeting_type_id,
                "activity_type_id": props.get("activity_type_id")
            }
            touchpoint_activities.append(touchpoint_activity)
            
            if meeting_type_id:
                meeting_type_name = meeting_types.get(meeting_type_id, f"Unknown ({meeting_type_id})")
                meeting_type_stats[meeting_type_name] += 1
    
    print(f"\n✅ Found {len(touchpoint_activities)} touchpoint activities")
    
    if meeting_type_stats:
        print("\n📊 Meeting type distribution:")
        for mtype, count in meeting_type_stats.most_common():
            print(f"   • {mtype}: {count}")
    
    # Save extracted touchpoints
    output_path = Path("/Users/<USER>/Desktop/wildweasel/Browser/data/icici_touchpoints.json")
    output_path.parent.mkdir(exist_ok=True)
    with open(output_path, "w") as f:
        json.dump(touchpoint_activities, f, indent=2)
    print(f"\n💾 Saved touchpoints to: {output_path}")
    
    # Show sample touchpoints
    print("\n📝 Sample touchpoint activities:")
    for i, activity in enumerate(touchpoint_activities[:5]):
        print(f"\n--- Touchpoint {i+1} ---")
        print(f"Type: {activity['type']}")
        print(f"Date: {datetime.fromtimestamp(activity['timestamp']/1000).strftime('%Y-%m-%d %H:%M')}")
        if activity['meeting_type_id']:
            print(f"Meeting Type: {meeting_types.get(activity['meeting_type_id'], 'Unknown')}")
        activity_type = flow_types.get(activity['activity_type_id'], 'Unknown')
        print(f"Activity Type: {activity_type}")
        
        # Extract meaningful content
        props = activity['properties']
        if 'subject' in props:
            print(f"Subject: {props['subject']}")
        elif 'name' in props:
            print(f"Name: {props['name']}")
        elif 'display_name' in props:
            print(f"Display Name: {props['display_name']}")
    
    return touchpoint_activities


def convert_to_gainsight_batch(touchpoint_activities):
    """Convert touchpoint activities to Gainsight format"""
    print("\n🔄 Converting touchpoints to Gainsight format...")
    
    # Load required data
    with open("/Users/<USER>/Desktop/totango/ID.json", "r") as f:
        meeting_types = {m["id"]: m["display_name"] for m in json.load(f)}
    
    with open("/Users/<USER>/Desktop/wildweasel/Browser/Gainsight_payload.json", "r") as f:
        template = json.load(f)
    
    converted_activities = []
    
    for activity in touchpoint_activities:
        props = activity['properties']
        
        # Determine Gainsight activity type
        gainsight_type = "NOTE"  # Default
        
        if activity['meeting_type_id']:
            meeting_type_name = meeting_types.get(activity['meeting_type_id'], "").lower()
            if "email" in meeting_type_name:
                gainsight_type = "EMAIL"
            elif "call" in meeting_type_name or "phone" in meeting_type_name:
                gainsight_type = "CALL"
            elif "meeting" in meeting_type_name:
                gainsight_type = "MEETING"
        
        # Extract subject/title
        subject = (props.get('subject') or 
                  props.get('name') or 
                  props.get('display_name') or
                  props.get('title') or
                  f"{activity['type']} - {datetime.fromtimestamp(activity['timestamp']/1000).strftime('%Y-%m-%d')}")
        
        # Extract content
        content_parts = []
        if props.get('description'):
            content_parts.append(props['description'])
        if props.get('content'):
            content_parts.append(props['content'])
        if props.get('note'):
            content_parts.append(props['note'])
        
        # Add other relevant properties
        for key, value in props.items():
            if key not in ['subject', 'name', 'description', 'content', 'note', 
                          'last_updated_internal', 'job_id', 'trigger_id'] and value:
                content_parts.append(f"{key}: {value}")
        
        content = "<br>".join(content_parts) if content_parts else f"Activity from Totango - Type: {activity['type']}"
        
        # Build Gainsight payload
        gainsight_activity = json.loads(json.dumps(template))  # Deep copy
        gainsight_activity["note"].update({
            "type": gainsight_type,
            "subject": subject[:255],  # Limit subject length
            "activityDate": activity['timestamp'],
            "content": f"<p>{content}</p>",
            "plainText": content.replace("<br>", "\n")
        })
        
        # Update custom fields
        gainsight_activity["note"]["customFields"].update({
            "totango_id": activity['id'],
            "totango_type": activity['type'],
            "totango_activity_type": activity.get('activity_type_id')
        })
        
        # Update contexts
        gainsight_activity["contexts"] = [{
            "id": activity['account_id'],
            "obj": "Company",
            "lbl": "ICICI Bank",
            "dsp": True,
            "base": True
        }]
        
        converted_activities.append(gainsight_activity)
    
    # Save converted activities  
    output_path = Path("/Users/<USER>/Desktop/wildweasel/Browser/data/icici_gainsight_ready.json")
    with open(output_path, "w") as f:
        json.dump(converted_activities, f, indent=2)
    
    print(f"✅ Converted {len(converted_activities)} activities")
    print(f"💾 Saved to: {output_path}")
    
    # Show conversion summary
    type_counts = Counter(a["note"]["type"] for a in converted_activities)
    print("\n📊 Gainsight activity type distribution:")
    for gtype, count in type_counts.most_common():
        print(f"   • {gtype}: {count}")
    
    return converted_activities


def create_migration_script(converted_activities):
    """Create a script for UI automation or API posting"""
    print("\n📝 Creating migration execution script...")
    
    script_content = f'''#!/usr/bin/env python3
"""
Auto-generated migration script for ICICI activities to Gainsight
Generated on: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
Total activities: {len(converted_activities)}
"""

import json
import asyncio
from pathlib import Path

# Load the converted activities
activities_path = Path("/Users/<USER>/Desktop/wildweasel/Browser/data/icici_gainsight_ready.json")
with open(activities_path, "r") as f:
    activities = json.load(f)

print(f"📊 Loaded {{len(activities)}} activities for migration")

# Option 1: API Migration (requires API key)
async def migrate_via_api(api_key):
    """Post activities to Gainsight via API"""
    import aiohttp
    
    api_url = "https://demo-emea1.gainsightcloud.com/v1/ant/v2/activity"
    headers = {{
        "Authorization": f"Bearer {{api_key}}",
        "Content-Type": "application/json"
    }}
    
    async with aiohttp.ClientSession() as session:
        for i, activity in enumerate(activities):
            print(f"Posting activity {{i+1}}/{{len(activities)}}: {{activity['note']['subject'][:50]}}...")
            
            async with session.post(api_url, json=activity, headers=headers) as response:
                if response.status == 200:
                    print(f"✅ Success")
                else:
                    print(f"❌ Failed: {{response.status}}")

# Option 2: UI Migration (using browser automation)
async def migrate_via_ui():
    """Create activities via Gainsight UI"""
    import sys
    sys.path.insert(0, "/Users/<USER>/Desktop/wildweasel/Browser")
    from orchestrator import orchestrator
    
    await orchestrator.initialize()
    
    # Login to Gainsight
    await orchestrator.execute_task(
        "Navigate to https://demo-emea1.gainsightcloud.com and login",
        context={{}},
        record_session=True
    )
    
    # Create each activity
    for i, activity in enumerate(activities[:10]):  # Limit to 10 for testing
        print(f"\\nCreating activity {{i+1}}: {{activity['note']['subject'][:50]}}...")
        
        task = f"""
        Create a new {{activity['note']['type']}} activity with:
        - Subject: {{activity['note']['subject']}}
        - Content: {{activity['note']['plainText'][:100]}}...
        - Company: ICICI Bank
        """
        
        await orchestrator.execute_task(
            task,
            context={{"activity": activity}},
            learn_from_execution=True,
            use_playwright_optimization=True
        )
    
    await orchestrator.cleanup()

# Main execution
async def main():
    print("🚀 ICICI to Gainsight Migration")
    print("Choose migration method:")
    print("1. API (requires API key)")
    print("2. UI Automation")
    
    choice = input("Enter choice (1 or 2): ")
    
    if choice == "1":
        api_key = input("Enter Gainsight API key: ")
        await migrate_via_api(api_key)
    else:
        await migrate_via_ui()
    
    print("\\n✅ Migration complete!")

if __name__ == "__main__":
    asyncio.run(main())
'''
    
    script_path = Path("/Users/<USER>/Desktop/wildweasel/Browser/migrate_icici_to_gainsight.py")
    with open(script_path, "w") as f:
        f.write(script_content)
    
    # Make script executable
    script_path.chmod(0o755)
    
    print(f"✅ Created migration script: {script_path}")
    print("\n🚀 To run the migration:")
    print(f"   python3 {script_path}")


def main():
    """Main execution"""
    print("🚀 ICICI Touchpoint Extraction and Conversion")
    print("=" * 60)
    
    # Extract touchpoint activities
    touchpoints = extract_touchpoint_activities()
    
    if touchpoints:
        # Convert to Gainsight format
        converted = convert_to_gainsight_batch(touchpoints)
        
        # Create migration script
        create_migration_script(converted)
        
        print("\n" + "=" * 60)
        print("✅ Process complete!")
        print(f"\n📁 Generated files:")
        print(f"   1. icici_touchpoints.json - Extracted touchpoint activities")
        print(f"   2. icici_gainsight_ready.json - Converted to Gainsight format")
        print(f"   3. migrate_icici_to_gainsight.py - Ready-to-run migration script")
        print(f"\n💡 Next step: Run the migration script to post activities to Gainsight")
    else:
        print("\n❌ No touchpoint activities found in ICICI.json")
        print("💡 The file appears to contain only automated system activities")


if __name__ == "__main__":
    main()

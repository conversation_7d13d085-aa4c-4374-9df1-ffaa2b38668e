#!/usr/bin/env python3
"""
Test Enhanced ICICI to Gainsight Converter
Shows how the enhanced converter maps meeting types to Gainsight activity types
"""

import json
import os
from pathlib import Path
from icici_to_gainsight_enhanced import ICICIToGainsightConverter

def test_mapping_functionality():
    """Test the Totango to Gainsight mapping functionality"""
    print("🧪 TESTING TOTANGO → GAINSIGHT MAPPING")
    print("=" * 50)
    
    converter = ICICIToGainsightConverter()
    
    # Test cases for mapping
    test_cases = [
        # Default Gainsight Types
        ("Email", "Email"),
        ("Telephone Call", "Call"),
        ("Web Meeting", "Meeting"),
        ("Internal Note", "Update"),
        
        # Custom Gainsight Types
        ("In-Person Meeting", "In-Person Meeting"),
        ("Gong Call", "Gong Call"),
        ("Feedback", "Feedback"),
        ("Inbound", "Inbound"),
        ("Slack", "Slack"),
        
        # Case variations
        ("email", "Email"),
        ("TELEPHONE CALL", "Call"),
        ("web meeting", "Meeting"),
        ("internal note", "Update"),
        
        # Pattern matching
        ("Email Campaign", "Email"),
        ("Phone Call", "Call"),
        ("Video Conference", "Meeting"),
        ("Chat Message", "Slack"),
        ("Customer Feedback", "Feedback"),
        ("Support Request", "Inbound"),
        ("Gong Recording", "Gong Call"),
        ("In-Person Client Meeting", "In-Person Meeting"),
        
        # Unknown types (should default to Update)
        ("Unknown Activity", "Update"),
        ("Random Type", "Update"),
        ("", "Update")
    ]
    
    print("📋 Mapping Test Results:")
    success_count = 0
    
    for input_type, expected_output in test_cases:
        actual_output = converter.map_totango_to_gainsight_type(input_type)
        status = "✅" if actual_output == expected_output else "❌"
        print(f"   {status} '{input_type}' → '{actual_output}' (expected: '{expected_output}')")
        
        if actual_output == expected_output:
            success_count += 1
    
    print(f"\n📊 Mapping Test Summary:")
    print(f"   • Total tests: {len(test_cases)}")
    print(f"   • Successful: {success_count}")
    print(f"   • Success rate: {success_count/len(test_cases)*100:.1f}%")
    
    return success_count == len(test_cases)

def analyze_sample_data():
    """Analyze a sample of real ICICI data"""
    print("\n🔍 ANALYZING SAMPLE ICICI DATA")
    print("=" * 50)
    
    icici_file = "/Users/<USER>/Desktop/totango/ICICI.json"
    
    if not Path(icici_file).exists():
        print(f"❌ ICICI file not found: {icici_file}")
        print("   Please ensure the file exists at the correct path")
        return False
    
    try:
        with open(icici_file, 'r', encoding='utf-8') as f:
            icici_data = json.load(f)
        
        print(f"📊 Found {len(icici_data)} total activities")
        
        # Analyze first 20 activities
        sample_size = min(20, len(icici_data))
        sample_activities = icici_data[:sample_size]
        
        converter = ICICIToGainsightConverter()
        
        # Load mappings to analyze properly
        try:
            converter.load_mappings()
        except Exception as e:
            print(f"⚠️  Could not load mapping files: {e}")
            print("   Will show structure analysis only")
        
        meeting_types_found = {}
        activities_with_meeting_type = 0
        
        print(f"\n🔍 Analyzing sample of {sample_size} activities:")
        
        for i, activity in enumerate(sample_activities):
            properties = activity.get('properties', {})
            
            if 'meeting_type' in properties:
                activities_with_meeting_type += 1
                meeting_type_id = properties['meeting_type']
                
                # Try to map the meeting type
                if hasattr(converter, 'meeting_type_mapping') and converter.meeting_type_mapping:
                    totango_type = converter.meeting_type_mapping.get(meeting_type_id, f"UNMAPPED_{meeting_type_id}")
                    gainsight_type = converter.map_totango_to_gainsight_type(totango_type)
                    
                    mapping_key = f"{totango_type} → {gainsight_type}"
                    meeting_types_found[mapping_key] = meeting_types_found.get(mapping_key, 0) + 1
                    
                    if i < 5:  # Show first 5 examples
                        print(f"   {i+1}. ID: {meeting_type_id}")
                        print(f"      Totango Type: {totango_type}")
                        print(f"      Gainsight Type: {gainsight_type}")
                else:
                    if i < 5:
                        print(f"   {i+1}. Meeting Type ID: {meeting_type_id} (mapping not loaded)")
        
        print(f"\n📊 Sample Analysis Results:")
        print(f"   • Activities with meeting_type: {activities_with_meeting_type}/{sample_size}")
        
        if meeting_types_found:
            print(f"   • Meeting type mappings found:")
            for mapping, count in meeting_types_found.items():
                print(f"     • {mapping}: {count} activities")
        
        return True
        
    except Exception as e:
        print(f"❌ Error analyzing data: {e}")
        return False

def show_expected_output_structure():
    """Show what the enhanced output structure looks like"""
    print("\n📋 EXPECTED OUTPUT STRUCTURE")
    print("=" * 50)
    
    sample_output = {
        "id": "activity_123",
        "timestamp": 1640995200000,
        "type": "campaign_touch",
        "properties": {
            "meeting_type": "1I00ABC123DEF456GHI789",
            "meeting_type_id": "1I00ABC123DEF456GHI789",
            "meeting_type_name": "Email",  # Original Totango type
            "gainsight_activity_type": "Email",  # Mapped Gainsight type
            "touchpoint_tags": ["1I00XYZ789"],
            "touchpoint_tags_ids": ["1I00XYZ789"],
            "touchpoint_tags_names": ["Customer Onboarding"],
            "name": "Welcome Email Campaign",
            "description": "Welcome email sent to new customers"
        }
    }
    
    print("📄 Sample enhanced activity structure:")
    print(json.dumps(sample_output, indent=2))
    
    print(f"\n🔍 Key enhancements:")
    print(f"   • meeting_type_name: Original Totango meeting type name")
    print(f"   • gainsight_activity_type: Mapped Gainsight activity type")
    print(f"   • touchpoint_tags_names: Human-readable touchpoint tag names")
    
    print(f"\n🎯 Gainsight Activity Type Mappings:")
    mappings = [
        "Email → Email",
        "Telephone Call → Call", 
        "Web Meeting → Meeting",
        "Internal Note → Update",
        "In-Person Meeting → In-Person Meeting",
        "Gong Call → Gong Call",
        "Feedback → Feedback",
        "Inbound → Inbound",
        "Slack → Slack"
    ]
    
    for mapping in mappings:
        print(f"   • {mapping}")

def main():
    """Main test function"""
    print("🧪 ENHANCED ICICI TO GAINSIGHT CONVERTER TEST")
    print("=" * 60)
    
    # Test 1: Mapping functionality
    mapping_success = test_mapping_functionality()
    
    # Test 2: Analyze sample data
    analysis_success = analyze_sample_data()
    
    # Test 3: Show expected output
    show_expected_output_structure()
    
    # Summary
    print(f"\n🎯 TEST SUMMARY:")
    print("=" * 30)
    
    if mapping_success:
        print("✅ Totango → Gainsight mapping: WORKING")
    else:
        print("❌ Totango → Gainsight mapping: FAILED")
    
    if analysis_success:
        print("✅ Data analysis: WORKING")
    else:
        print("⚠️  Data analysis: ISSUES (check file paths)")
    
    print(f"\n🚀 NEXT STEPS:")
    if mapping_success:
        print("   1. Run the enhanced converter:")
        print("      python icici_to_gainsight_enhanced.py")
        print("   2. Check output file: ICICI_gainsight_ready.json")
        print("   3. Verify 'gainsight_activity_type' fields are properly set")
        print("   4. Use the converted data for Gainsight migration")
    else:
        print("   1. Fix mapping issues")
        print("   2. Re-run this test")
    
    print("\n🎉 Test completed!")

if __name__ == "__main__":
    main()

#!/usr/bin/env python3
"""
Playwright Integration Example

Shows practical usage of the Playwright integration for token-efficient browser automation.
"""

import asyncio
import logging
from pathlib import Path

# Add the parent directory to the path
import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from orchestrator import orchestrator

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

async def example_basic_usage():
    """Basic example showing optimal task execution"""
    print("🎭 Basic Playwright Integration Usage")
    print("=" * 50)
    
    # Initialize system
    print("🚀 Initializing system...")
    success = await orchestrator.initialize()
    if not success:
        print("❌ Initialization failed")
        return
    
    # Execute a task - system will automatically decide LLM vs Playwright
    print("\n📝 Executing task with automatic optimization...")
    task = "Go to example.com and extract the page title"
    
    result = await orchestrator.execute_task(
        task_description=task,
        use_playwright_optimization=True,  # Enable optimization (default)
        learn_from_execution=True  # Learn from this execution
    )
    
    if result["success"]:
        print(f"✅ Task completed successfully!")
        print(f"   Method used: {result.get('execution_method', 'unknown')}")
        print(f"   Execution time: {result.get('execution_time', 0):.1f}s")
        
        # Show decision info if available
        decision_info = result.get("decision_info", {})
        if decision_info:
            print(f"   Decision reason: {decision_info.get('reason', 'N/A')}")
            tokens_saved = decision_info.get('estimated_tokens_saved', 0)
            if tokens_saved > 0:
                print(f"   Tokens saved: {tokens_saved}")
    else:
        print(f"❌ Task failed: {result.get('error', 'Unknown error')}")

async def example_pattern_optimization():
    """Example showing pattern optimization workflow"""
    print("\n🔧 Pattern Optimization Example")
    print("=" * 50)
    
    # Execute multiple similar tasks to create patterns
    similar_tasks = [
        "Go to example.com and get the page title",
        "Visit httpbin.org and extract the main heading", 
        "Navigate to example.com and check for links"
    ]
    
    print("📝 Executing similar tasks to create patterns...")
    session_data_list = []
    
    for i, task in enumerate(similar_tasks, 1):
        print(f"\n🔸 Task {i}: {task}")
        
        result = await orchestrator.execute_task(
            task_description=task,
            use_playwright_optimization=False,  # Force LLM to record patterns
            record_session=True
        )
        
        if result["success"] and result.get("recording_path"):
            print(f"   ✅ Completed and recorded")
            
            # Load session data for optimization
            try:
                recording_path = result["recording_path"]
                if Path(recording_path).exists():
                    import json
                    with open(recording_path, 'r') as f:
                        session_data = json.load(f)
                        session_data['task_description'] = task
                        session_data_list.append(session_data)
            except Exception as e:
                logger.debug(f"Could not load session data: {e}")
        else:
            print(f"   ❌ Failed or not recorded")
    
    # Optimize patterns
    if session_data_list:
        print(f"\n🔧 Optimizing patterns from {len(session_data_list)} sessions...")
        
        optimization_result = await orchestrator.optimize_task_patterns(session_data_list)
        
        if not optimization_result.get("error"):
            print("✅ Pattern optimization completed!")
            print(f"   Patterns discovered: {optimization_result.get('patterns_discovered', 0)}")
            print(f"   Reusable functions: {optimization_result.get('reusable_functions', 0)}")
            
            potential_savings = optimization_result.get('potential_token_savings', {})
            total_savings = potential_savings.get('total_estimated_tokens_saved', 0)
            if total_savings > 0:
                print(f"   Potential token savings: {total_savings}")
            
            # Show recommendations
            recommendations = optimization_result.get('recommendations', [])
            if recommendations:
                print("   💡 Recommendations:")
                for rec in recommendations[:2]:
                    print(f"      • {rec}")
        else:
            print(f"❌ Pattern optimization failed: {optimization_result['error']}")

async def example_performance_monitoring():
    """Example showing performance monitoring"""
    print("\n📊 Performance Monitoring Example")
    print("=" * 50)
    
    # Get current performance statistics
    print("📈 Current system performance:")
    
    status = await orchestrator.get_system_status()
    
    # Browser automation stats
    browser_stats = status.get("browser_automation", {})
    print(f"   🤖 Browser tasks: {browser_stats.get('total_tasks', 0)} total")
    print(f"      Success rate: {browser_stats.get('success_rate', 0):.1%}")
    
    # Playwright integration stats
    playwright_stats = status.get("playwright_integration", {})
    if not playwright_stats.get("error"):
        total_executions = playwright_stats.get('total_executions', 0)
        playwright_executions = playwright_stats.get('playwright_executions', 0)
        tokens_saved = playwright_stats.get('total_tokens_saved', 0)
        
        print(f"   🎭 Playwright optimization:")
        print(f"      Total executions: {total_executions}")
        if total_executions > 0:
            optimization_rate = playwright_executions / total_executions * 100
            print(f"      Optimization rate: {optimization_rate:.1f}%")
        print(f"      Tokens saved: {tokens_saved}")
    else:
        print(f"   ⚠️  Playwright integration: {playwright_stats.get('error', 'Not available')}")
    
    # Memory and session info
    memory_stats = status.get("memory_system", {})
    orchestrator_stats = status.get("orchestrator", {})
    
    print(f"   🧠 Memory blocks: {memory_stats.get('memory_blocks_count', 0)}")
    print(f"   📝 Session entries: {orchestrator_stats.get('session_history_count', 0)}")

async def example_manual_script_generation():
    """Example showing manual script generation from sessions"""
    print("\n🎭 Manual Script Generation Example")
    print("=" * 50)
    
    # Execute a task and generate script manually
    print("📝 Executing task for script generation...")
    
    task = "Go to httpbin.org/json and extract the JSON data"
    result = await orchestrator.execute_task(
        task_description=task,
        use_playwright_optimization=False,  # Force LLM execution
        record_session=True
    )
    
    if result["success"] and result.get("recording_path"):
        print("✅ Task completed and recorded")
        
        # Generate Playwright script manually
        try:
            recording_path = result["recording_path"]
            if Path(recording_path).exists():
                import json
                with open(recording_path, 'r') as f:
                    session_data = json.load(f)
                
                print("🎭 Generating Playwright script...")
                script_result = await orchestrator.generate_playwright_script(session_data)
                
                if not script_result.get("error"):
                    print("✅ Script generated successfully!")
                    print(f"   Script path: {script_result.get('script_path', 'unknown')}")
                    print(f"   Patterns extracted: {script_result.get('patterns_extracted', 0)}")
                    print(f"   Estimated tokens saved: {script_result.get('estimated_tokens_saved', 0)}")
                    
                    # Show script content preview
                    script_path = script_result.get('script_path')
                    if script_path and Path(script_path).exists():
                        print("\n📄 Script preview (first 10 lines):")
                        with open(script_path, 'r') as f:
                            lines = f.readlines()[:10]
                            for i, line in enumerate(lines, 1):
                                print(f"   {i:2d}: {line.rstrip()}")
                        if len(lines) == 10:
                            print("   ... (truncated)")
                else:
                    print(f"❌ Script generation failed: {script_result['error']}")
            
        except Exception as e:
            print(f"❌ Error in script generation: {e}")
    else:
        print("❌ Task failed or was not recorded")

async def main():
    """Main example runner"""
    print("🎭 PLAYWRIGHT INTEGRATION EXAMPLES")
    print("=" * 60)
    print("This demonstrates practical usage of Playwright integration")
    print("for token-efficient browser automation.")
    print("=" * 60)
    
    try:
        # Run examples
        await example_basic_usage()
        await example_pattern_optimization()
        await example_performance_monitoring()
        await example_manual_script_generation()
        
        print("\n🎉 All examples completed successfully!")
        print("\nKey Benefits Demonstrated:")
        print("• 🚀 Automatic optimization (LLM vs Playwright)")
        print("• 💰 Token savings through pattern reuse")
        print("• 🔧 Pattern discovery and optimization")
        print("• 📊 Performance monitoring and analytics")
        print("• 🎭 Manual script generation capabilities")
        
    except KeyboardInterrupt:
        print("\n👋 Examples interrupted by user")
    except Exception as e:
        print(f"\n❌ Examples failed: {e}")
        import traceback
        traceback.print_exc()
    finally:
        # Cleanup
        try:
            await orchestrator.cleanup()
        except:
            pass

if __name__ == "__main__":
    asyncio.run(main())

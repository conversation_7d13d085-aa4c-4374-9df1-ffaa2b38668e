#!/usr/bin/env python3
"""
Fixed ICICI Totango to Gainsight Data Converter
Converts Totango activities to Gainsight format with proper content mapping
"""

import json
import re
from datetime import datetime
from pathlib import Path
from typing import Dict, List, Any, Optional


def extract_meaningful_content(totango_activity: Dict[str, Any]) -> tuple[str, str]:
    """Extract meaningful content from Totango activity"""
    props = totango_activity.get("properties", {})
    activity_type = totango_activity.get("type", "")
    
    content = ""
    
    # Priority 1: Direct description/content fields
    if props.get("description"):
        content = props["description"]
    elif props.get("title"):
        content = props["title"]
    elif props.get("note_content", {}).get("text"):
        # For notes with note_content structure
        content = props["note_content"]["text"]
    elif props.get("json_content"):
        # Try to parse json_content for meaningful text
        try:
            if isinstance(props["json_content"], str):
                json_data = json.loads(props["json_content"])
            else:
                json_data = props["json_content"]
            if isinstance(json_data, dict):
                content = json_data.get("message", "") or json_data.get("text", "") or json_data.get("content", "")
            else:
                content = str(json_data)
        except:
            content = str(props["json_content"])
    else:
        # Build meaningful content from available fields
        content_parts = []
        
        # Add subject/name if available
        if props.get("name"):
            content_parts.append(props["name"])
        
        # Add specific content based on activity type
        if activity_type == "campaign_touch":
            if props.get("campaign_description"):
                content_parts.append(f"Campaign: {props['campaign_description']}")
            if props.get("targeted_users_count"):
                content_parts.append(f"Sent to {props['targeted_users_count']} users")
                
        elif activity_type == "account_alert":
            if props.get("background_img"):
                content_parts.append(f"Alert type: {props['background_img']}")
            if props.get("from_user"):
                content_parts.append(f"From: {props['from_user']}")
                
        elif activity_type == "automated_attribute_change":
            if props.get("action") and props.get("prev_value") and props.get("new_value"):
                content_parts.append(f"Changed {props.get('display_name', 'attribute')} from '{props['prev_value']}' to '{props['new_value']}'")
            elif props.get("display_name"):
                content_parts.append(f"Updated {props['display_name']}")
                
        elif activity_type == "webhook":
            if props.get("request_type") and props.get("request_url"):
                content_parts.append(f"{props['request_type']} request to {props['request_url']}")
            if props.get("status"):
                content_parts.append(f"Status: {props['status']}")
        
        # If we have meaningful parts, join them
        if content_parts:
            content = ". ".join(content_parts)
        else:
            # Last resort: use subject or activity type
            subject = props.get("subject", "") or props.get("name", "") or props.get("display_name", "")
            content = subject or f"Activity of type {activity_type}"
    
    # Clean and format content
    # Remove HTML tags for plain text version
    plain_text_content = re.sub(r'<[^>]+>', '', content)
    plain_text_content = plain_text_content.replace('&nbsp;', ' ').strip()
    
    # Ensure content has basic HTML formatting
    if content and not content.startswith('<'):
        html_content = f"<p>{content}</p>"
    elif not content:
        html_content = f"<p>{plain_text_content}</p>"
    else:
        html_content = content
    
    return html_content, plain_text_content


def fix_icici_mapping():
    """Fix the ICICI mapping with proper content extraction"""
    
    # Load the current converted data
    data_path = Path("/Users/<USER>/Desktop/wildweasel/Browser/data/icici_gainsight_ready.json")
    
    with open(data_path, "r") as f:
        current_activities = json.load(f)
    
    # Load original ICICI data to get the source content
    original_path = Path("/Users/<USER>/Desktop/totango/ICICI.json")
    with open(original_path, "r") as f:
        original_icici_data = json.load(f)
    
    # Load the touchpoints data to get the filtered activities
    touchpoints_path = Path("/Users/<USER>/Desktop/wildweasel/Browser/data/icici_touchpoints.json")
    with open(touchpoints_path, "r") as f:
        touchpoint_activities = json.load(f)
    
    print(f"📊 Found {len(current_activities)} current activities")
    print(f"📊 Found {len(touchpoint_activities)} touchpoint activities")
    
    # Create mapping from totango_id to original activity
    original_mapping = {activity["id"]: activity for activity in original_icici_data}
    
    # Fix each activity
    fixed_activities = []
    
    for activity in current_activities:
        totango_id = activity["note"]["customFields"]["totango_id"]
        
        if totango_id in original_mapping:
            original_activity = original_mapping[totango_id]
            
            # Extract proper content
            html_content, plain_text = extract_meaningful_content(original_activity)
            
            # Update the activity with fixed content
            activity["note"]["content"] = html_content
            activity["note"]["plainText"] = plain_text
            
            print(f"✅ Fixed content for: {activity['note']['subject']}")
            print(f"   Content: {plain_text[:100]}{'...' if len(plain_text) > 100 else ''}")
        
        fixed_activities.append(activity)
    
    # Save the fixed activities
    output_path = Path("/Users/<USER>/Desktop/wildweasel/Browser/data/icici_gainsight_ready.json")
    with open(output_path, "w") as f:
        json.dump(fixed_activities, f, indent=2)
    
    print(f"\n💾 Saved {len(fixed_activities)} fixed activities to: {output_path}")
    
    # Show a sample of the fixed content
    print(f"\n📄 Sample fixed activity:")
    sample_activity = fixed_activities[0]
    print(f"   Subject: {sample_activity['note']['subject']}")
    print(f"   Content (HTML): {sample_activity['note']['content']}")
    print(f"   Content (Plain): {sample_activity['note']['plainText']}")
    
    return fixed_activities


if __name__ == "__main__":
    print("🔧 Fixing ICICI content mapping...")
    print("=" * 50)
    
    fixed_activities = fix_icici_mapping()
    
    print("\n✅ Content mapping fixed successfully!")
    print(f"✅ All {len(fixed_activities)} activities now have proper content mapping")
    print("\nThe content field now contains meaningful text instead of metadata.")

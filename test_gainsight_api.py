#!/usr/bin/env python3
"""
Simple API Test Script for Gainsight Migration
Test the drafts API and activity creation
"""

import json
import requests
from pathlib import Path

def test_gainsight_api(api_key):
    """Test Gainsight API connectivity and drafts endpoint"""
    
    # Load a sample activity
    activities_path = Path("data/icici_gainsight_ready.json")
    with open(activities_path, "r") as f:
        activities = json.load(f)
    
    sample_activity = activities[0]  # Test with first activity
    
    print(f"🧪 Testing Gainsight API with sample activity:")
    print(f"   Subject: {sample_activity['note']['subject']}")
    
    # Test drafts API
    drafts_url = "https://demo-emea1.gainsightcloud.com/v1/ant/v2/activity/drafts"
    headers = {
        "Authorization": f"Bearer {api_key}",
        "Content-Type": "application/json"
    }
    
    print(f"\n📡 Testing drafts API...")
    try:
        response = requests.post(drafts_url, json=sample_activity, headers=headers)
        if response.status_code == 200:
            draft_data = response.json()
            draft_id = draft_data.get("id")
            print(f"✅ Drafts API success! Draft ID: {draft_id}")
            
            # Test activity creation with draft ID
            sample_activity["id"] = draft_id
            activity_url = "https://demo-emea1.gainsightcloud.com/v1/ant/v2/activity"
            
            print(f"\n📝 Testing activity creation...")
            activity_response = requests.post(activity_url, json=sample_activity, headers=headers)
            
            if activity_response.status_code == 200:
                print(f"✅ Activity creation success!")
                return True
            else:
                print(f"❌ Activity creation failed: {activity_response.status_code}")
                print(f"   Response: {activity_response.text}")
        else:
            print(f"❌ Drafts API failed: {response.status_code}")
            print(f"   Response: {response.text}")
    
    except Exception as e:
        print(f"❌ API test error: {e}")
    
    return False

if __name__ == "__main__":
    api_key = input("Enter Gainsight API key: ").strip()
    if api_key:
        test_gainsight_api(api_key)
    else:
        print("❌ API key required")

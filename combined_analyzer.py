import json
from pathlib import Path
from collections import Counter

def load_json_file(file_path_str):
    """
    Loads a JSON file from the given file path string.

    Args:
        file_path_str (str): The path to the JSON file.

    Returns:
        data (list or dict): The loaded JSON data, or None if an error occurs.
    """
    file_path = Path(file_path_str)
    if not file_path.exists():
        print(f"❌ File not found: {file_path_str}")
        return None
    try:
        with open(file_path, "r", encoding='utf-8') as f:
            data = json.load(f)
            return data
    except json.JSONDecodeError as e:
        print(f"❌ Error decoding JSON from {file_path_str}: {e}")
        return None
    except Exception as e:
        print(f"❌ An unexpected error occurred while reading {file_path_str}: {e}")
        return None

def generate_combined_analysis(icici_file_path, flowtype_file_path):
    """
    Analyzes ICICI.json for top-level 'type' and 'properties.activity_type_id',
    maps activity_type_id to display_names from flowtype.json, and provides a combined summary.

    Args:
        icici_file_path (str): Path to the ICICI JSON file.
        flowtype_file_path (str): Path to the flowtype JSON file.

    Returns:
        str: A string containing the combined analysis summary in Markdown format.
    """
    icici_filename = Path(icici_file_path).name
    flowtype_filename = Path(flowtype_file_path).name

    # Load flowtype.json for mapping activity_type_id to display_name
    flow_type_data = load_json_file(flowtype_file_path)
    if not flow_type_data:
        return f"Error: Could not load '{flowtype_filename}'. Analysis cannot proceed."
    flow_type_map = {item['activity_type_id']: item['display_name'] for item in flow_type_data}

    # Load ICICI.json
    icici_data = load_json_file(icici_file_path)
    if not icici_data:
        return f"Error: Could not load '{icici_filename}'. Analysis cannot proceed."

    # Initialize counters
    top_level_type_counts = Counter()
    activity_id_counts = Counter()
    
    total_records_in_icici = len(icici_data)
    records_with_top_level_type = 0
    records_with_mappable_activity_id = 0

    # Process each record in ICICI.json
    for record in icici_data:
        # 1. Analyze top-level 'type' (Touchpoint Type)
        top_level_type = record.get("type")
        if top_level_type:
            top_level_type_counts[top_level_type] += 1
            records_with_top_level_type += 1
        
        # 2. Analyze 'properties.activity_type_id' (Flow Type)
        properties = record.get("properties", {})
        if properties and isinstance(properties, dict):
            activity_id = properties.get("activity_type_id")
            if activity_id and activity_id in flow_type_map: # Only count if it's a known/mappable ID
                activity_id_counts[activity_id] += 1
                records_with_mappable_activity_id += 1
            elif activity_id: # Optional: count unmapped activity_ids if needed later
                pass


    # Prepare the Markdown output
    output_lines = ["**Combined Activity and Touchpoint Type Analysis:**\n"]
    output_lines.append(f"This report analyzes records from `{icici_filename}` for both top-level `type` (referred to as Touchpoint Types) and `properties.activity_type_id` (referred to as Flow Types, mapped via `{flowtype_filename}`).\n")

    # Overall Summary
    output_lines.append(f"**1. Overall Record Summary:**")
    output_lines.append(f"- Total records processed from `{icici_filename}`: **{total_records_in_icici}**\n")

    # Top-Level 'type' (Touchpoint Type) Analysis
    output_lines.append(f"**2. Top-Level `type` (Touchpoint Type) Analysis:**")
    num_distinct_top_level_types = len(top_level_type_counts)
    sum_top_level_type_occurrences = sum(top_level_type_counts.values())
    
    output_lines.append(f"- Found **{num_distinct_top_level_types}** distinct top-level `type` values.")
    output_lines.append(f"- These types appeared across **{records_with_top_level_type}** records (out of {total_records_in_icici} total).")
    output_lines.append("\n  **Breakdown of Top-Level `type` Occurrences:**")
    if top_level_type_counts:
        for item_type, count in top_level_type_counts.most_common():
            output_lines.append(f"  - **{item_type}**: {count} occurrences")
    else:
        output_lines.append("  - No top-level `type` values were found or counted.")
    
    if sum_top_level_type_occurrences == total_records_in_icici:
        output_lines.append(f"\n  - *Coverage Note:* The sum of top-level `type` occurrences ({sum_top_level_type_occurrences}) matches the total record count, indicating all records had a `type` field.\n")
    else:
        output_lines.append(f"\n  - *Coverage Note:* The sum of top-level `type` occurrences ({sum_top_level_type_occurrences}) does not match the total record count ({total_records_in_icici}). This suggests {total_records_in_icici - sum_top_level_type_occurrences} records might be missing a `type` field or were not counted.\n")

    # 'properties.activity_type_id' (Flow Type) Analysis
    output_lines.append(f"**3. `properties.activity_type_id` (Flow Type) Analysis:**")
    num_distinct_flow_types_found = len(activity_id_counts)
    sum_mapped_activity_id_occurrences = sum(activity_id_counts.values())

    output_lines.append(f"- Found **{num_distinct_flow_types_found}** distinct `activity_type_id` values that were successfully mapped to display names from `{flowtype_filename}`.")
    output_lines.append(f"- These mapped flow types appeared across **{records_with_mappable_activity_id}** records (out of {total_records_in_icici} total).")
    
    output_lines.append("\n  **Breakdown of Mapped `activity_type_id` Occurrences:**")
    if activity_id_counts:
        sorted_activity_counts = sorted(activity_id_counts.items(), key=lambda item: item[1], reverse=True)
        for activity_id, count in sorted_activity_counts:
            display_name = flow_type_map.get(activity_id, f"Unknown Type ({activity_id})")
            output_lines.append(f"  - **{display_name}** (ID: `{activity_id}`): {count} occurrences")
    else:
        output_lines.append(f"  - No records with mappable `activity_type_id` values were found.")
    
    output_lines.append(f"\n  - *Coverage Note:* The sum of mapped `activity_type_id` occurrences is **{sum_mapped_activity_id_occurrences}**. This means {records_with_mappable_activity_id} out of {total_records_in_icici} total records had a `properties.activity_type_id` that could be mapped via `{flowtype_filename}`.\n")
    
    return "\n".join(output_lines)

if __name__ == "__main__":
    icici_actual_path = "/Users/<USER>/Desktop/totango/ICICI.json"
    flowtype_actual_path = "/Users/<USER>/Desktop/totango/flowtype.json"
    
    combined_analysis_summary = generate_combined_analysis(
        icici_file_path=icici_actual_path,
        flowtype_file_path=flowtype_actual_path
    )
    print(combined_analysis_summary)


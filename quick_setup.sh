#!/bin/bash

echo "🚀 Browser Automation Agent - Quick Setup & Fix"
echo "=============================================="

# Check if virtual environment is activated
if [[ "$VIRTUAL_ENV" == "" ]]; then
    echo "⚠️  Virtual environment not detected. Activating..."
    if [ -d "venv" ]; then
        source venv/bin/activate
        echo "✅ Virtual environment activated"
    else
        echo "❌ Virtual environment not found. Creating one..."
        python3 -m venv venv
        source venv/bin/activate
        echo "✅ Virtual environment created and activated"
    fi
else
    echo "✅ Virtual environment already active: $VIRTUAL_ENV"
fi

# Fix dependencies
echo ""
echo "📦 Installing/Fixing Dependencies..."
python3 fix_dependencies.py

# Check API keys
echo ""
echo "🔑 Checking API Configuration..."
if [ -f ".env" ]; then
    echo "✅ .env file found"
    
    # Check for API keys without exposing them
    if grep -q "OPENAI_API_KEY=" .env && ! grep -q "OPENAI_API_KEY=$" .env && ! grep -q "OPENAI_API_KEY=\"\"" .env; then
        echo "✅ OpenAI API key configured"
    else
        echo "⚠️  OpenAI API key not configured in .env"
    fi
    
    if grep -q "GOOGLE_API_KEY=" .env && ! grep -q "GOOGLE_API_KEY=$" .env && ! grep -q "GOOGLE_API_KEY=\"\"" .env; then
        echo "✅ Google API key configured"
    else
        echo "⚠️  Google API key not configured in .env"
    fi
    
    if grep -q "ANTHROPIC_API_KEY=" .env && ! grep -q "ANTHROPIC_API_KEY=$" .env && ! grep -q "ANTHROPIC_API_KEY=\"\"" .env; then
        echo "✅ Anthropic API key configured"
    else
        echo "⚠️  Anthropic API key not configured in .env"
    fi
else
    echo "⚠️  .env file not found. Creating template..."
    cat > .env << 'EOF'
# LLM API Keys (Add at least one)
OPENAI_API_KEY=your_openai_api_key_here
GOOGLE_API_KEY=your_google_api_key_here  
ANTHROPIC_API_KEY=your_anthropic_api_key_here

# Optional: Database URLs
POSTGRES_URL=postgresql://localhost/agent_memory
REDIS_URL=redis://localhost:6379

# Optional: Vector Database
PINECONE_API_KEY=your_pinecone_key_here
PINECONE_ENVIRONMENT=your_pinecone_env_here

# Development Settings
ENVIRONMENT=development
EOF
    echo "✅ .env template created - please add your API keys"
fi

# Test the system
echo ""
echo "🧪 Testing System Status..."
python3 main.py --integrations

echo ""
echo "🎯 System Status Check Complete!"
echo ""
echo "📋 Next Steps:"
echo "1. Add your API keys to the .env file if not already done"
echo "2. Test with: python3 main.py -t 'Navigate to Google'"
echo "3. For interactive mode: python3 main.py"
echo "4. For help: python3 main.py --help"
echo ""
echo "🚀 Your Browser Automation Agent is ready!"

import json
from pathlib import Path
from collections import Counter

def load_json_file(file_path_str):
    """
    Loads a JSON file from the given file path string.

    Args:
        file_path_str (str): The path to the JSON file.

    Returns:
        data (list or dict): The loaded JSON data, or None if an error occurs.
    """
    file_path = Path(file_path_str)
    if not file_path.exists():
        print(f"❌ File not found: {file_path_str}") # This specific message should appear if file is not found
        return None
    try:
        with open(file_path, "r", encoding='utf-8') as f:
            data = json.load(f)
            return data
    except json.JSONDecodeError as e:
        print(f"❌ Error decoding JSON from {file_path_str}: {e}") # This specific message for JSON errors
        return None
    except Exception as e:
        print(f"❌ An unexpected error occurred while reading {file_path_str}: {e}")
        return None

def analyze_activity_flow_types(icici_file_path="ICICI.json", flowtype_file_path="flowtype.json"):
    """
    Analyzes activity_type_id occurrences in ICICI.json and maps them to display_names
    from flowtype.json.

    Args:
        icici_file_path (str): Path to the ICICI JSON file.
        flowtype_file_path (str): Path to the flowtype JSON file.

    Returns:
        str: A string containing the analysis summary in Markdown format.
    """
    # Get the actual filenames for more accurate error reporting
    icici_filename = Path(icici_file_path).name
    flowtype_filename = Path(flowtype_file_path).name

    # Load flowtype.json to create a mapping
    flow_type_data = load_json_file(flowtype_file_path)
    if not flow_type_data:
        # Use the actual filename in the error message
        return f"Error: Could not load '{flowtype_filename}'. Analysis cannot proceed. Please check the path and file content."

    flow_type_map = {item['activity_type_id']: item['display_name'] for item in flow_type_data}

    # Load ICICI.json
    icici_data = load_json_file(icici_file_path)
    if not icici_data:
        # Use the actual filename in the error message
        return f"Error: Could not load '{icici_filename}'. Analysis cannot proceed. Please check the path and file content."

    activity_id_counts = Counter()
    total_activities_with_mappable_id = 0

    for record in icici_data:
        properties = record.get("properties", {})
        if properties and isinstance(properties, dict) and "activity_type_id" in properties:
            activity_id = properties["activity_type_id"]
            if activity_id in flow_type_map:
                activity_id_counts[activity_id] += 1
                total_activities_with_mappable_id += 1

    output_lines = []
    output_lines.append("**Activity Flow Type Analysis (Based on `activity_type_id`):**")
    output_lines.append(f"\nThis analysis focuses on the `activity_type_id` field found within the `properties` object of records in the `{icici_filename}` file. Each found `activity_type_id` is then mapped to its corresponding `display_name` as defined in `{flowtype_filename}` to provide a count of occurrences for each recognized flow type.")

    if not activity_id_counts:
        output_lines.append(f"\n- No activities with `activity_type_id` values mappable to `{flowtype_filename}` were found in `{icici_filename}`.")
        return "\n".join(output_lines)

    num_distinct_flow_types_found = len(activity_id_counts)

    output_lines.append(f"\n- **{num_distinct_flow_types_found}** distinct flow/activity types (derived from `activity_type_id`) were identified in `{icici_filename}` and successfully mapped using `{flowtype_filename}`.")
    output_lines.append(f"- A total of **{total_activities_with_mappable_id}** records in `{icici_filename}` contained an `activity_type_id` that could be mapped to a display name from `{flowtype_filename}`.")

    output_lines.append("\n**Breakdown of Mapped Activity Flow Types:**\n")
    
    sorted_activity_counts = sorted(activity_id_counts.items(), key=lambda item: item[1], reverse=True)

    for activity_id, count in sorted_activity_counts:
        display_name = flow_type_map.get(activity_id, f"Unknown Type ({activity_id})")
        output_lines.append(f"- **{display_name}** (ID: `{activity_id}`): {count} occurrences")
    
    return "\n".join(output_lines)

if __name__ == "__main__":
    # Using the exact file paths you provided
    icici_actual_path = "/Users/<USER>/Desktop/totango/ICICI.json"
    flowtype_actual_path = "/Users/<USER>/Desktop/totango/flowtype.json"
    
    analysis_summary = analyze_activity_flow_types(
        icici_file_path=icici_actual_path,
        flowtype_file_path=flowtype_actual_path
    )
    print(analysis_summary)

#!/usr/bin/env python3
"""
🔧 FIXED COMPACT LAUNCHER
No async errors, proper browser cleanup, compact display
"""

import asyncio
import subprocess
import sys
import json
from pathlib import Path

def show_compact_menu():
    """Ultra compact menu"""
    print("\n🎯 AUTOMATION")
    print("1. UI Demo (1)")
    print("2. UI Batch (3)")  
    print("3. Record Workflow")
    print("4. Status")
    print("5. LLM Test")
    print("0. Exit")

async def run_fixed_ui_automation(max_activities=1):
    """Fixed UI automation with proper cleanup"""
    print(f"\n🌐 UI AUTOMATION ({max_activities})")
    
    try:
        from playwright.async_api import async_playwright
        
        async with async_playwright() as p:
            browser = await p.chromium.launch(headless=False)
            page = await browser.new_page()
            
            try:
                print("🌐 Login to Gainsight...")
                await page.goto("https://auth.gainsightcloud.com/login?lc=en")
                await page.wait_for_timeout(3000)
                
                # Handle domain input
                print("🏢 Domain: demo-emea1")
                domain_selectors = [
                    'input[placeholder*="subdomain"]',
                    'input[name*="subdomain"]',
                    'input[type="text"]'
                ]
                
                for selector in domain_selectors:
                    try:
                        if await page.locator(selector).count() > 0:
                            await page.fill(selector, "demo-emea1")
                            await page.click('button:has-text("Continue"), button[type="submit"]')
                            await page.wait_for_timeout(2000)
                            break
                    except:
                        continue
                
                # Login
                print("🔐 Credentials...")
                await page.fill('input[name="username"], input[type="email"]', "<EMAIL>")
                await page.fill('input[type="password"]', "@Ramprasad826ie")
                await page.click('button[type="submit"], button:has-text("Sign")') 
                await page.wait_for_timeout(5000)
                
                # Timeline
                print("🌐 Timeline...")
                timeline_url = "https://demo-emea1.gainsightcloud.com/v1/ui/customersuccess360?cid=1P02IPMAEL4M3CQGY4K5FHU22D2HHT11KCKU#/8fd79692-8e98-4543-b2ec-bc0dba3776ca"
                await page.goto(timeline_url)
                await page.wait_for_timeout(3000)
                
                # Timeline tab
                try:
                    await page.click('a:has-text("Timeline"), button:has-text("Timeline")')
                    await page.wait_for_timeout(2000)
                except:
                    print("⚠️  Timeline tab not found")
                
                # Create activities
                success_count = 0
                for i in range(max_activities):
                    print(f"📝 Activity {i+1}...")
                    
                    try:
                        # Create -> Activity
                        await page.click('button:has-text("Create")')
                        await page.wait_for_timeout(1000)
                        await page.click('a:has-text("Activity")')
                        await page.wait_for_timeout(2000)
                        
                        # Fill details
                        subject = f"Auto Activity {i+1}"
                        await page.fill('input[name="subject"], input[placeholder*="subject"]', subject)
                        
                        # Submit
                        await page.click('button:has-text("Log Activity"), button:has-text("Save")')
                        await page.wait_for_timeout(2000)
                        
                        success_count += 1
                        print(f"✅ Activity {i+1} created")
                        
                    except Exception as e:
                        print(f"❌ Activity {i+1} failed: {e}")
                
                print(f"\n📊 Results: {success_count}/{max_activities} created")
                
            finally:
                # Proper cleanup
                await page.close()
                await browser.close()
                
    except Exception as e:
        print(f"❌ Error: {e}")

async def run_record_workflow():
    """Simple recording workflow"""
    print("\n🎭 RECORD WORKFLOW")
    print("Manual → Script Generation")
    
    try:
        from playwright.async_api import async_playwright
        
        async with async_playwright() as p:
            browser = await p.chromium.launch(headless=False)
            page = await browser.new_page()
            
            try:
                print("🌐 Opening Gainsight...")
                await page.goto("https://auth.gainsightcloud.com/login?lc=en")
                
                print("\n📝 MANUAL STEPS:")
                print("1. Domain: demo-emea1")
                print("2. Login with your credentials")
                print("3. Go to timeline")
                print("4. Create ONE activity")
                print("5. Press Enter when done...")
                
                input()  # Wait for manual completion
                
                # Generate script
                script_content = '''#!/usr/bin/env python3
"""
Generated Gainsight Automation Script
"""
import asyncio
from playwright.async_api import async_playwright

async def create_activity(subject="Generated Activity"):
    async with async_playwright() as p:
        browser = await p.chromium.launch(headless=False)
        page = await browser.new_page()
        
        try:
            # Login
            await page.goto("https://auth.gainsightcloud.com/login?lc=en")
            await page.wait_for_timeout(2000)
            
            # Domain
            await page.fill('input[type="text"]', "demo-emea1")
            await page.click('button[type="submit"]')
            await page.wait_for_timeout(2000)
            
            # Credentials
            await page.fill('input[type="email"]', "<EMAIL>")
            await page.fill('input[type="password"]', "@Ramprasad826ie")
            await page.click('button[type="submit"]')
            await page.wait_for_timeout(4000)
            
            # Timeline
            timeline_url = "https://demo-emea1.gainsightcloud.com/v1/ui/customersuccess360?cid=1P02IPMAEL4M3CQGY4K5FHU22D2HHT11KCKU#/8fd79692-8e98-4543-b2ec-bc0dba3776ca"
            await page.goto(timeline_url)
            await page.wait_for_timeout(3000)
            
            # Create activity
            await page.click('button:has-text("Create")')
            await page.wait_for_timeout(1000)
            await page.click('a:has-text("Activity")')
            await page.wait_for_timeout(2000)
            await page.fill('input[name="subject"]', subject)
            await page.click('button:has-text("Log Activity")')
            await page.wait_for_timeout(2000)
            
            print("✅ Activity created!")
            
        finally:
            await browser.close()

if __name__ == "__main__":
    asyncio.run(create_activity("Test from Generated Script"))
'''
                
                script_path = Path("data/generated_automation.py")
                script_path.parent.mkdir(exist_ok=True)
                with open(script_path, 'w') as f:
                    f.write(script_content)
                
                print(f"✅ Script generated: {script_path}")
                print(f"🔧 Test: python3 {script_path}")
                
            finally:
                await page.close()
                await browser.close()
                
    except Exception as e:
        print(f"❌ Error: {e}")

def run_status_check():
    """Quick status check"""
    print("\n📊 STATUS CHECK")
    try:
        subprocess.run([sys.executable, "quick_status_check.py"], 
                      cwd="/Users/<USER>/Desktop/wildweasel/Browser")
    except:
        print("❌ Status check failed")

def run_llm_test():
    """Test LLM"""
    print("\n🤖 LLM TEST")
    try:
        subprocess.run([sys.executable, "-c", 
            "from enhanced_llm_client import test_llm_client; import asyncio; asyncio.run(test_llm_client())"],
            cwd="/Users/<USER>/Desktop/wildweasel/Browser")
    except:
        print("❌ LLM test failed")

def main():
    """Fixed main launcher"""
    print("🎯 FIXED AUTOMATION LAUNCHER")
    print("No async errors, proper cleanup")
    
    while True:
        try:
            show_compact_menu()
            choice = input("Choice: ").strip()
            
            if choice == "0":
                print("👋 Bye!")
                break
            elif choice == "1":
                asyncio.run(run_fixed_ui_automation(1))
            elif choice == "2":
                asyncio.run(run_fixed_ui_automation(3))
            elif choice == "3":
                asyncio.run(run_record_workflow())
            elif choice == "4":
                run_status_check()
            elif choice == "5":
                run_llm_test()
            else:
                print("❌ Invalid")
            
            if choice != "0":
                input("\nPress Enter...")
        
        except KeyboardInterrupt:
            print("\n👋 Bye!")
            break
        except Exception as e:
            print(f"❌ Error: {e}")

if __name__ == "__main__":
    main()

#!/usr/bin/env python3
"""
Playwright Integration Demo

This script demonstrates the new Playwright script generation and optimization features
that reduce LLM token usage by converting successful browser automation patterns 
into reusable Playwright scripts.
"""

import asyncio
import json
import logging
from pathlib import Path
from datetime import datetime

# Add the parent directory to the path
import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from orchestrator import orchestrator

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class PlaywrightIntegrationDemo:
    """
    Demonstrates Playwright integration capabilities
    """
    
    def __init__(self):
        self.demo_tasks = [
            "Go to example.com and extract the page title",
            "Navigate to httpbin.org and click on the /json endpoint",
            "Visit example.com and check if there are any links on the page",
            "Go to httpbin.org/html and extract the main heading"
        ]
        self.results = []
        
    async def run_demo(self):
        """Run the complete Playwright integration demo"""
        print("🎭 PLAYWRIGHT INTEGRATION DEMO")
        print("=" * 60)
        print("This demo shows how browser automation tasks are optimized")
        print("by converting LLM actions into reusable Playwright scripts.")
        print("=" * 60)
        
        # Initialize the system
        await self._initialize_system()
        
        # Phase 1: Execute tasks and record patterns
        await self._phase_1_record_patterns()
        
        # Phase 2: Generate Playwright scripts
        await self._phase_2_generate_scripts() 
        
        # Phase 3: Optimize patterns
        await self._phase_3_optimize_patterns()
        
        # Phase 4: Demonstrate optimized execution
        await self._phase_4_demonstrate_optimization()
        
        # Phase 5: Show performance statistics
        await self._phase_5_show_statistics()
        
        print("\n🎉 Demo completed successfully!")
        print("Check the 'data' directory for generated scripts and patterns.")
    
    async def _initialize_system(self):
        """Initialize the orchestrator system"""
        print("\n🚀 PHASE 1: System Initialization")
        print("-" * 40)
        
        success = await orchestrator.initialize()
        if success:
            print("✅ System initialized successfully")
            
            # Get initial status
            status = await orchestrator.get_system_status()
            print(f"📊 Browser automation ready: {status['browser_automation']['total_tasks']} tasks completed")
            print(f"🧠 Memory system: {status['memory_system']['memory_blocks_count']} memory blocks")
            print(f"🎭 Playwright integration: {'enabled' if status['configuration']['playwright_optimization_enabled'] else 'disabled'}")
        else:
            print("❌ System initialization failed")
            return False
        
        return True
    
    async def _phase_1_record_patterns(self):
        """Execute demo tasks to record patterns"""
        print("\n📝 PHASE 2: Recording Task Patterns")
        print("-" * 40)
        print("Executing tasks with LLM to establish baseline patterns...")
        
        for i, task in enumerate(self.demo_tasks, 1):
            print(f"\n🔸 Task {i}: {task}")
            
            try:
                # Execute with Playwright optimization disabled initially to record patterns
                result = await orchestrator.execute_task(
                    task, 
                    use_playwright_optimization=False,  # Force LLM execution for pattern recording
                    learn_from_execution=True,
                    record_session=True
                )
                
                if result["success"]:
                    print(f"   ✅ Completed successfully in {result.get('execution_time', 0):.1f}s")
                    print(f"   📊 Execution method: {result.get('execution_method', 'unknown')}")
                    
                    # Try to generate Playwright script from this session
                    if result.get("recording_path"):
                        print(f"   📹 Session recorded: {Path(result['recording_path']).name}")
                else:
                    print(f"   ❌ Task failed: {result.get('error', 'Unknown error')}")
                
                self.results.append({
                    "task": task,
                    "result": result,
                    "phase": "pattern_recording"
                })
                
                # Brief delay between tasks
                await asyncio.sleep(1)
                
            except Exception as e:
                print(f"   ❌ Task execution failed: {e}")
                self.results.append({
                    "task": task,
                    "result": {"success": False, "error": str(e)},
                    "phase": "pattern_recording"
                })
        
        successful_tasks = len([r for r in self.results if r["result"].get("success")])
        print(f"\n📈 Pattern Recording Summary:")
        print(f"   • Tasks executed: {len(self.demo_tasks)}")
        print(f"   • Successful: {successful_tasks}")
        print(f"   • Success rate: {successful_tasks/len(self.demo_tasks)*100:.1f}%")
    
    async def _phase_2_generate_scripts(self):
        """Generate Playwright scripts from recorded sessions"""
        print("\n🎭 PHASE 3: Generating Playwright Scripts")
        print("-" * 40)
        
        scripts_generated = 0
        
        for result_data in self.results:
            result = result_data["result"]
            task = result_data["task"]
            
            if result.get("success") and result.get("recording_path"):
                try:
                    # Load session data
                    recording_path = result["recording_path"]
                    if Path(recording_path).exists():
                        with open(recording_path, 'r') as f:
                            session_data = json.load(f)
                        
                        print(f"\n🔸 Generating script for: {task}")
                        
                        # Generate Playwright script
                        script_result = await orchestrator.generate_playwright_script(session_data)
                        
                        if not script_result.get("error"):
                            scripts_generated += 1
                            print(f"   ✅ Script generated: {script_result.get('script_path', 'unknown')}")
                            print(f"   📊 Patterns extracted: {script_result.get('patterns_extracted', 0)}")
                            print(f"   💰 Estimated tokens saved: {script_result.get('estimated_tokens_saved', 0)}")
                        else:
                            print(f"   ❌ Script generation failed: {script_result['error']}")
                    
                except Exception as e:
                    print(f"   ❌ Error generating script: {e}")
        
        print(f"\n📈 Script Generation Summary:")
        print(f"   • Scripts generated: {scripts_generated}")
        print(f"   • Success rate: {scripts_generated/len([r for r in self.results if r['result'].get('success')])*100:.1f}%")
    
    async def _phase_3_optimize_patterns(self):
        """Optimize patterns across multiple sessions"""
        print("\n🔧 PHASE 4: Pattern Optimization")
        print("-" * 40)
        
        # Collect all successful sessions
        successful_sessions = []
        for result_data in self.results:
            result = result_data["result"] 
            if result.get("success") and result.get("recording_path"):
                try:
                    recording_path = result["recording_path"]
                    if Path(recording_path).exists():
                        with open(recording_path, 'r') as f:
                            session_data = json.load(f)
                            session_data['task_description'] = result_data["task"]
                            successful_sessions.append(session_data)
                except Exception as e:
                    logger.debug(f"Failed to load session {recording_path}: {e}")
        
        if successful_sessions:
            print(f"🔸 Analyzing {len(successful_sessions)} successful sessions for patterns...")
            
            try:
                optimization_result = await orchestrator.optimize_task_patterns(successful_sessions)
                
                if not optimization_result.get("error"):
                    print("✅ Pattern optimization completed")
                    print(f"   📊 Sessions analyzed: {optimization_result.get('sessions_analyzed', 0)}")
                    print(f"   🎯 Patterns discovered: {optimization_result.get('patterns_discovered', 0)}")
                    print(f"   🔧 Reusable functions: {optimization_result.get('reusable_functions', 0)}")
                    print(f"   💰 Potential token savings: {optimization_result.get('potential_token_savings', {}).get('total_estimated_tokens_saved', 0)}")
                    
                    # Show recommendations
                    recommendations = optimization_result.get('recommendations', [])
                    if recommendations:
                        print("   💡 Recommendations:")
                        for rec in recommendations[:3]:
                            print(f"      • {rec}")
                else:
                    print(f"❌ Pattern optimization failed: {optimization_result['error']}")
                    
            except Exception as e:
                print(f"❌ Pattern optimization error: {e}")
        
        else:
            print("⚠️ No successful sessions available for pattern optimization")
    
    async def _phase_4_demonstrate_optimization(self):
        """Demonstrate optimized execution using Playwright"""
        print("\n⚡ PHASE 5: Optimized Task Execution")
        print("-" * 40)
        print("Re-executing tasks with Playwright optimization enabled...")
        
        optimized_results = []
        
        for i, task in enumerate(self.demo_tasks[:2], 1):  # Test first 2 tasks
            print(f"\n🔸 Optimized Task {i}: {task}")
            
            try:
                # Execute with Playwright optimization enabled
                result = await orchestrator.execute_task(
                    task,
                    use_playwright_optimization=True,  # Enable optimization
                    learn_from_execution=True
                )
                
                if result["success"]:
                    execution_method = result.get("execution_method", "unknown")
                    execution_time = result.get("execution_time", 0)
                    tokens_saved = result.get("decision_info", {}).get("estimated_tokens_saved", 0)
                    
                    print(f"   ✅ Completed successfully in {execution_time:.1f}s")
                    print(f"   🎭 Execution method: {execution_method}")
                    if tokens_saved > 0:
                        print(f"   💰 Tokens saved: {tokens_saved}")
                    
                    if execution_method == "playwright":
                        print("   🚀 Task executed using generated Playwright script!")
                    elif execution_method == "llm":
                        decision_info = result.get("decision_info", {})
                        print(f"   💭 LLM used: {decision_info.get('reason', 'No suitable patterns found')}")
                    
                else:
                    print(f"   ❌ Task failed: {result.get('error', 'Unknown error')}")
                
                optimized_results.append(result)
                
            except Exception as e:
                print(f"   ❌ Optimized execution failed: {e}")
        
        print(f"\n📈 Optimization Demonstration Summary:")
        successful_optimized = len([r for r in optimized_results if r.get("success")])
        playwright_executions = len([r for r in optimized_results if r.get("execution_method") == "playwright"])
        
        print(f"   • Tasks executed: {len(optimized_results)}")
        print(f"   • Successful: {successful_optimized}")
        print(f"   • Playwright executions: {playwright_executions}")
        print(f"   • Optimization rate: {playwright_executions/len(optimized_results)*100:.1f}%" if optimized_results else "0%")
    
    async def _phase_5_show_statistics(self):
        """Show final performance statistics"""
        print("\n📊 PHASE 6: Performance Statistics")
        print("-" * 40)
        
        try:
            # Get system status
            status = await orchestrator.get_system_status()
            
            # Browser automation stats
            browser_stats = status.get("browser_automation", {})
            print("🤖 Browser Automation:")
            print(f"   • Total tasks: {browser_stats.get('total_tasks', 0)}")
            print(f"   • Success rate: {browser_stats.get('success_rate', 0):.1%}")
            
            # Playwright integration stats  
            playwright_stats = status.get("playwright_integration", {})
            if not playwright_stats.get("error"):
                print("\n🎭 Playwright Integration:")
                print(f"   • Total executions: {playwright_stats.get('total_executions', 0)}")
                print(f"   • LLM executions: {playwright_stats.get('llm_executions', 0)}")
                print(f"   • Playwright executions: {playwright_stats.get('playwright_executions', 0)}")
                print(f"   • Playwright usage rate: {playwright_stats.get('playwright_usage_rate', 0):.1%}")
                print(f"   • Total tokens saved: {playwright_stats.get('total_tokens_saved', 0)}")
                print(f"   • Time saved: {playwright_stats.get('total_time_saved_seconds', 0):.1f}s")
            else:
                print(f"\n⚠️ Playwright integration: {playwright_stats.get('error', 'Unknown error')}")
            
            # Session history
            orchestrator_stats = status.get("orchestrator", {})
            print(f"\n📝 Session History: {orchestrator_stats.get('session_history_count', 0)} entries")
            
            # Memory system
            memory_stats = status.get("memory_system", {})
            print(f"🧠 Memory System: {memory_stats.get('memory_blocks_count', 0)} memory blocks")
            
        except Exception as e:
            print(f"❌ Failed to get statistics: {e}")

async def main():
    """Main demo entry point"""
    demo = PlaywrightIntegrationDemo()
    
    try:
        await demo.run_demo()
    except KeyboardInterrupt:
        print("\n👋 Demo interrupted by user")
    except Exception as e:
        print(f"\n❌ Demo failed: {e}")
        import traceback
        traceback.print_exc()
    finally:
        # Cleanup
        try:
            await orchestrator.cleanup()
        except:
            pass

if __name__ == "__main__":
    asyncio.run(main())

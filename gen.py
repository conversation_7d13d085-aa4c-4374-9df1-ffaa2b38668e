#!/usr/bin/env python3
"""
ICICI to Gainsight Activity Converter
====================================

This script processes ICICI company activities from JSON format and maps:
1. meeting_type IDs to their display names using ID.json
2. touchpoint_tags IDs to their display names using Touchpoint_reason.JSON

Author: <PERSON><PERSON><PERSON> Somaraju
Date: May 29, 2025
"""

import json
import os
from typing import Dict, List, Any, Optional
from collections import defaultdict, Counter

class ICICIActivityConverter:
    def __init__(self, base_path: str = "/Users/<USER>/Desktop/totango"):
        """Initialize the converter with file paths."""
        self.base_path = base_path
        self.icici_file = os.path.join(base_path, "ICICI.json")
        self.id_mapping_file = os.path.join(base_path, "ID.json")
        self.touchpoint_mapping_file = os.path.join(base_path, "Touchpoint_reason.JSON")
        
        # Storage for mappings
        self.meeting_type_mapping = {}
        self.touchpoint_tags_mapping = {}
        self.icici_activities = []
        
        # Statistics
        self.stats = {
            'total_activities': 0,
            'activities_with_meeting_type': 0,
            'activities_with_touchpoint_tags': 0,
            'unmapped_meeting_types': set(),
            'meeting_type_usage': Counter(),
            'touchpoint_tags_usage': Counter()
        }

    def load_mappings(self) -> None:
        """Load ID mappings from JSON files."""
        print("📋 Loading mapping files...")
        
        # Load meeting type mappings
        try:
            with open(self.id_mapping_file, 'r', encoding='utf-8') as f:
                id_data = json.load(f)
            self.meeting_type_mapping = {item['id']: item['display_name'] for item in id_data}
            print(f"✅ Loaded {len(self.meeting_type_mapping)} meeting type mappings")
        except FileNotFoundError:
            print(f"❌ Error: {self.id_mapping_file} not found")
            raise
        except json.JSONDecodeError as e:
            print(f"❌ Error parsing {self.id_mapping_file}: {e}")
            raise

        # Load touchpoint tags mappings
        try:
            with open(self.touchpoint_mapping_file, 'r', encoding='utf-8') as f:
                touchpoint_data = json.load(f)
            self.touchpoint_tags_mapping = {item['id']: item['display_name'] for item in touchpoint_data}
            print(f"✅ Loaded {len(self.touchpoint_tags_mapping)} touchpoint tag mappings")
        except FileNotFoundError:
            print(f"❌ Error: {self.touchpoint_mapping_file} not found")
            raise
        except json.JSONDecodeError as e:
            print(f"❌ Error parsing {self.touchpoint_mapping_file}: {e}")
            raise

    def load_icici_activities(self) -> None:
        """Load ICICI activities from JSON file."""
        print("📊 Loading ICICI activities...")
        
        try:
            with open(self.icici_file, 'r', encoding='utf-8') as f:
                self.icici_activities = json.load(f)
            self.stats['total_activities'] = len(self.icici_activities)
            print(f"✅ Loaded {self.stats['total_activities']} ICICI activities")
        except FileNotFoundError:
            print(f"❌ Error: {self.icici_file} not found")
            raise
        except json.JSONDecodeError as e:
            print(f"❌ Error parsing {self.icici_file}: {e}")
            raise

    def map_meeting_type(self, meeting_type_id: str) -> Optional[str]:
        """Map meeting type ID to display name."""
        if meeting_type_id in self.meeting_type_mapping:
            mapped_name = self.meeting_type_mapping[meeting_type_id]
            self.stats['meeting_type_usage'][mapped_name] += 1
            return mapped_name
        else:
            self.stats['unmapped_meeting_types'].add(meeting_type_id)
            return None

    def map_touchpoint_tags(self, touchpoint_tag_ids: List[str]) -> List[str]:
        """Map touchpoint tag IDs to display names."""
        mapped_names = []
        
        for tag_id in touchpoint_tag_ids:
            if tag_id in self.touchpoint_tags_mapping:
                mapped_name = self.touchpoint_tags_mapping[tag_id]
                mapped_names.append(mapped_name)
                self.stats['touchpoint_tags_usage'][mapped_name] += 1
            else:
                mapped_names.append("Internal Note")
                self.stats['touchpoint_tags_usage']["Internal Note"] += 1
        
        return mapped_names

    def process_activities(self) -> List[Dict[str, Any]]:
        """Process all activities and apply mappings."""
        print("🔄 Processing activities and applying mappings...")
        
        processed_activities = []
        
        for activity in self.icici_activities:
            processed_activity = activity.copy()
            
            # Check if activity has properties
            if 'properties' in activity and activity['properties']:
                properties = activity['properties'].copy()
                
                # Process meeting_type
                if 'meeting_type' in properties:
                    self.stats['activities_with_meeting_type'] += 1
                    meeting_type_id = properties['meeting_type']
                    
                    # Add mapped meeting type name
                    mapped_name = self.map_meeting_type(meeting_type_id)
                    properties['meeting_type_id'] = meeting_type_id
                    properties['meeting_type_name'] = mapped_name if mapped_name else f"UNMAPPED_{meeting_type_id}"
                
                # Process touchpoint_tags
                if 'touchpoint_tags' in properties:
                    self.stats['activities_with_touchpoint_tags'] += 1
                    touchpoint_tags = properties['touchpoint_tags']
                    
                    # Handle both single ID and array of IDs
                    if isinstance(touchpoint_tags, str):
                        touchpoint_tags = [touchpoint_tags]
                    elif not isinstance(touchpoint_tags, list):
                        touchpoint_tags = []
                    
                    # Map touchpoint tags
                    mapped_names = self.map_touchpoint_tags(touchpoint_tags)
                    
                    # Store both original IDs and mapped names
                    properties['touchpoint_tags_ids'] = touchpoint_tags
                    properties['touchpoint_tags_names'] = mapped_names
                
                processed_activity['properties'] = properties
            
            processed_activities.append(processed_activity)
        
        return processed_activities

    def generate_summary_report(self) -> str:
        """Generate a comprehensive summary report."""
        report = []
        report.append("=" * 60)
        report.append("ICICI ACTIVITY PROCESSING SUMMARY")
        report.append("=" * 60)
        report.append(f"📊 Total Activities: {self.stats['total_activities']:,}")
        report.append(f"🎯 Activities with meeting_type: {self.stats['activities_with_meeting_type']:,}")
        report.append(f"🏷️  Activities with touchpoint_tags: {self.stats['activities_with_touchpoint_tags']:,}")
        report.append("")

        # Meeting Type Analysis
        report.append("📋 MEETING TYPE ANALYSIS")
        report.append("-" * 30)
        if self.stats['meeting_type_usage']:
            report.append("✅ Successfully Mapped Meeting Types:")
            for name, count in self.stats['meeting_type_usage'].most_common():
                report.append(f"   • {name}: {count:,} activities")

        if self.stats['unmapped_meeting_types']:
            report.append(f"\n❌ Unmapped Meeting Type IDs ({len(self.stats['unmapped_meeting_types'])}):") 
            for unmapped_id in sorted(self.stats['unmapped_meeting_types']):
                report.append(f"   • {unmapped_id}")
        report.append("")

        # Touchpoint Tags Analysis
        report.append("🏷️  TOUCHPOINT TAGS ANALYSIS")
        report.append("-" * 35)
        if self.stats['touchpoint_tags_usage']:
            report.append("✅ Mapped Touchpoint Tags:")
            for name, count in self.stats['touchpoint_tags_usage'].most_common():
                report.append(f"   • {name}: {count:,} activities")
        report.append("")

        # Solutions for Unmapped IDs
        if self.stats['unmapped_meeting_types']:
            report.append("💡 SOLUTIONS FOR UNMAPPED MEETING TYPE IDs")
            report.append("-" * 40)
            report.append("1. Check if these IDs exist in newer versions of mapping files")
            report.append("2. Contact data source administrator for missing mappings")
            report.append("3. Create manual mappings for critical unmapped IDs")
            report.append("4. Use 'UNMAPPED_{ID}' as fallback display names")

        report.append("=" * 60)
        return "\n".join(report)

    def save_processed_data(self, processed_activities: List[Dict[str, Any]], output_file: str = None) -> str:
        """Save processed activities to JSON file."""
        if output_file is None:
            output_file = os.path.join(self.base_path, "ICICI_processed.json")
        
        try:
            with open(output_file, 'w', encoding='utf-8') as f:
                json.dump(processed_activities, f, indent=2, ensure_ascii=False)
            print(f"💾 Processed data saved to: {output_file}")
            return output_file
        except Exception as e:
            print(f"❌ Error saving processed data: {e}")
            raise

    def run_conversion(self) -> None:
        """Run the complete conversion process."""
        print("🚀 Starting ICICI Activity Conversion Process")
        print("=" * 50)
        
        try:
            # Step 1: Load all data
            self.load_mappings()
            self.load_icici_activities()
            
            # Step 2: Process activities
            processed_activities = self.process_activities()
            
            # Step 3: Generate and display summary
            summary = self.generate_summary_report()
            print(summary)
            
            # Step 4: Save processed data
            output_file = self.save_processed_data(processed_activities)
            
            print(f"\n🎉 Conversion completed successfully!")
            print(f"📄 Summary report displayed above")
            print(f"💾 Processed data: {output_file}")
            
        except Exception as e:
            print(f"💥 Conversion failed: {e}")
            raise

def main():
    """Main function to run the converter."""
    converter = ICICIActivityConverter()
    converter.run_conversion()

if __name__ == "__main__":
    main()

#!/usr/bin/env python3
"""
Quick dependency fix script for Browser Automation Agent
"""
import subprocess
import sys
import os

def install_package(package):
    """Install a package using pip"""
    try:
        subprocess.check_call([sys.executable, "-m", "pip", "install", package])
        print(f"✅ Successfully installed {package}")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ Failed to install {package}: {e}")
        return False

def fix_dependencies():
    """Fix common dependency issues"""
    print("🔧 Fixing Browser Automation Agent Dependencies...")
    
    # Core dependencies that might be missing or need updates
    critical_packages = [
        "browser-use>=0.2.0",
        "langgraph>=0.4.0",
        "langgraph-checkpoint>=2.0.0",
        "letta>=0.6.0",
        "langchain>=0.3.0",
        "langchain-openai>=0.2.0",
        "playwright>=1.48.0",
        "openai>=1.50.0",
        "anthropic>=0.38.0",
        "python-dotenv>=1.0.0"
    ]
    
    print(f"Installing {len(critical_packages)} critical packages...")
    
    success_count = 0
    for package in critical_packages:
        if install_package(package):
            success_count += 1
    
    print(f"\n📊 Results: {success_count}/{len(critical_packages)} packages installed successfully")
    
    if success_count == len(critical_packages):
        print("\n✅ All critical dependencies installed successfully!")
        print("🚀 Your Browser Automation Agent should now work properly.")
    else:
        print(f"\n⚠️  {len(critical_packages) - success_count} packages failed to install.")
        print("You may need to install them manually or check for conflicts.")
    
    # Install playwright browsers
    print("\n🌐 Installing Playwright browsers...")
    try:
        subprocess.check_call([sys.executable, "-m", "playwright", "install"])
        print("✅ Playwright browsers installed successfully!")
    except subprocess.CalledProcessError as e:
        print(f"❌ Failed to install Playwright browsers: {e}")
        print("Try running: python -m playwright install")

if __name__ == "__main__":
    # Ensure we're in the right directory
    os.chdir(os.path.dirname(os.path.abspath(__file__)))
    
    fix_dependencies()
    
    print("\n🎯 Next steps:")
    print("1. Run: python3 main.py --integrations")
    print("2. Check integration status")
    print("3. Test with: python3 main.py -t 'Navigate to Google'")

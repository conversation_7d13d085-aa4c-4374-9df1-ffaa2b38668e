#!/usr/bin/env python3
"""
Check Meeting Types in ICICI Data
Analyze what meeting_type IDs exist and what they should map to
"""

import json
from collections import Counter

def analyze_meeting_types():
    """Analyze meeting types in ICICI data."""
    icici_file = "/Users/<USER>/Desktop/totango/ICICI_processed.json"
    
    with open(icici_file, 'r') as f:
        activities = json.load(f)
    
    print(f"🔍 ANALYZING MEETING TYPES IN {len(activities)} ACTIVITIES")
    print("=" * 60)
    
    meeting_type_ids = Counter()
    activities_with_meeting_type = 0
    
    for activity in activities:
        properties = activity.get('properties', {})
        
        if 'meeting_type' in properties:
            activities_with_meeting_type += 1
            meeting_type_id = properties['meeting_type']
            meeting_type_ids[meeting_type_id] += 1
    
    print(f"📊 Activities with meeting_type: {activities_with_meeting_type}")
    print(f"📊 Unique meeting type IDs: {len(meeting_type_ids)}")
    
    print(f"\n📋 Meeting Type IDs Found:")
    for meeting_id, count in meeting_type_ids.most_common():
        print(f"   • {meeting_id}: {count} activities")
    
    # Check if we have ID.json to see the actual names
    id_file = "/Users/<USER>/Desktop/totango/ID.json"
    try:
        with open(id_file, 'r') as f:
            id_mappings = json.load(f)
        
        print(f"\n📋 ID.json Mappings Found:")
        id_mapping_dict = {item['id']: item['display_name'] for item in id_mappings}
        
        for meeting_id, count in meeting_type_ids.most_common():
            mapped_name = id_mapping_dict.get(meeting_id, "UNKNOWN")
            print(f"   • {meeting_id} → '{mapped_name}' ({count} activities)")
            
    except Exception as e:
        print(f"\n⚠️  Could not load ID.json: {e}")
        print("This is why we're not getting proper Email/Call mappings")

if __name__ == "__main__":
    analyze_meeting_types()

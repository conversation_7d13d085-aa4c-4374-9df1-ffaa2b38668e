"""
Demo script showcasing the Browser Automation Agent capabilities
"""
import asyncio
import sys
import os
from pathlib import Path

# Add parent directory to path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

async def demo():
    """Run a comprehensive demo of the system"""
    print("🎬 Browser Automation Agent - Live Demo")
    print("=" * 50)
    
    # Import here to handle any import errors gracefully
    try:
        from orchestrator import orchestrator, execute_task, get_status
    except ImportError as e:
        print(f"❌ Import error: {e}")
        print("Please run: pip install -r requirements.txt")
        return
    
    # Initialize system
    print("🔧 Initializing system...")
    try:
        success = await orchestrator.initialize()
        if not success:
            print("❌ Failed to initialize system")
            return
        print("✅ System initialized successfully")
    except Exception as e:
        print(f"❌ Initialization error: {e}")
        print("Note: Some features may not work without proper setup")
        print("For full functionality, ensure Letta server is running: letta server")
    
    # Demo 1: Simple web interaction
    print("\n" + "="*50)
    print("📱 Demo 1: Simple Web Interaction")
    print("Task: Navigate to a website and explore")
    
    try:
        result = await execute_task(
            "Go to httpbin.org and explore the available endpoints",
            context={"demo": 1},
            learn_from_execution=True,
            record_session=True
        )
        
        if result["success"]:
            print(f"✅ Demo 1 completed in {result.get('execution_time', 0):.1f} seconds")
            if result.get("recording_path"):
                print(f"📹 Session recorded to: {result['recording_path']}")
        else:
            print(f"❌ Demo 1 failed: {result.get('error', 'Unknown error')}")
    
    except Exception as e:
        print(f"❌ Demo 1 error: {e}")
    
    # Demo 2: Memory demonstration
    print("\n" + "="*50)
    print("🧠 Demo 2: Memory & Learning")
    print("Task: Show how the agent learns and remembers")
    
    try:
        # First interaction
        print("\n🔸 First interaction - Teaching the agent:")
        result1 = await execute_task(
            "Remember that my favorite color is blue and I prefer simple interfaces",
            context={"demo": 2, "teaching": True}
        )
        
        # Second interaction - test memory
        print("\n🔸 Second interaction - Testing memory:")
        result2 = await execute_task(
            "What do you remember about my preferences?",
            context={"demo": 2, "testing": True}
        )
        
        if result1["success"] and result2["success"]:
            print("✅ Memory demonstration completed")
        else:
            print("⚠️  Memory demonstration partially completed")
    
    except Exception as e:
        print(f"❌ Demo 2 error: {e}")
    
    # Show system status
    print("\n" + "="*50)
    print("📊 System Status")
    
    try:
        status = await get_status()
        print(f"🔧 System Components:")
        print(f"   • Orchestrator: {'✅ Ready' if status['orchestrator']['initialized'] else '❌ Not Ready'}")
        print(f"   • Memory System: {status['memory_system']['memory_blocks_count']} blocks")
        print(f"   • Browser Agent: {status['browser_automation']['total_tasks']} tasks completed")
        print(f"   • Session History: {status['orchestrator']['session_history_count']} entries")
        
        print("\n🧠 Memory Blocks:")
        for block in status['memory_system']['memory_blocks']:
            print(f"   • {block}")
        
        print(f"\n⚙️  Configuration:")
        print(f"   • Max steps per task: {status['configuration']['max_steps_per_task']}")
        print(f"   • Browser headless: {status['configuration']['browser_headless']}")
        print(f"   • Learning enabled: {status['configuration']['learning_enabled']}")
    
    except Exception as e:
        print(f"❌ Status error: {e}")
    
    # Demo 3: Show recordings
    print("\n" + "="*50)
    print("📹 Demo 3: Recorded Sessions")
    
    recordings_dir = Path("data/recordings")
    if recordings_dir.exists():
        recordings = list(recordings_dir.glob("*.json"))
        if recordings:
            print(f"Found {len(recordings)} recorded sessions:")
            for recording in recordings[-3:]:  # Show last 3
                print(f"   • {recording.name}")
            print("\nYou can replay any session with:")
            print(f"   python main.py -r {recordings[-1]}")
        else:
            print("No recordings found yet. Run some tasks to create recordings!")
    else:
        print("Recordings directory not found")
    
    # Final summary
    print("\n" + "="*50)
    print("🎉 Demo Completed!")
    print("\nWhat you've seen:")
    print("✅ Browser automation with LLM intelligence")
    print("✅ Persistent memory that learns from interactions")
    print("✅ Task recording and replay capabilities")
    print("✅ System status monitoring")
    
    print("\nNext steps to explore:")
    print("• Try interactive mode: python main.py")
    print("• Run examples: python examples/basic_usage.py")
    print("• Create custom tasks: python main.py -t 'your task here'")
    print("• Replay sessions: python main.py -r path/to/recording.json")
    
    # Cleanup
    try:
        await orchestrator.cleanup()
        print("\n🧹 System cleanup completed")
    except:
        pass

if __name__ == "__main__":
    try:
        asyncio.run(demo())
    except KeyboardInterrupt:
        print("\n👋 Demo interrupted by user")
    except Exception as e:
        print(f"\n❌ Demo error: {e}")
        import traceback
        traceback.print_exc()

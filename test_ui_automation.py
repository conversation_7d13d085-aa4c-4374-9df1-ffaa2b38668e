#!/usr/bin/env python3
"""
🐺 Wild Weasel UI Automation - Test Script for First 3 Rows
=============================================================
Test the corrected UI automation with just the first 3 CSV rows
"""

import csv
import os
import sys
import time
import logging
from datetime import datetime
from playwright.sync_api import sync_playwright

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler("/Users/<USER>/Desktop/wild_weasel_gainsight_migration/test_ui_automation.log"),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger("WildWeasel-Test")

class TestUIAutomation:
    """Test UI automation with first 3 rows"""

    def __init__(self):
        self.config = {
            "csv_file": "./ICICI_processed_gainsight_mapped_enhanced_demo.csv",
            "target_url": "https://demo-emea1.gainsightcloud.com/v1/ui/customersuccess360?cid=1P02IPMAEL4M3CQGY4K5FHU22D2HHT11KCKU#/8fd79692-8e98-4543-b2ec-bc0dba3776ca"
        }
        self.credentials = {
            "username": "<EMAIL>",
            "password": "@Ramprasad826ie"
        }
        self.activities = []

    def load_test_activities(self):
        """Load first 3 activities from CSV"""
        try:
            if not os.path.exists(self.config['csv_file']):
                logger.error(f"❌ CSV file not found: {self.config['csv_file']}")
                return False

            self.activities = []
            with open(self.config['csv_file'], 'r', encoding='utf-8') as f:
                csv_reader = csv.DictReader(f)
                for i, row in enumerate(csv_reader):
                    if i >= 3:  # Only load first 3 rows
                        break
                    activity = {
                        "subject": row.get('Subject', '').strip(),
                        "activity_date": row.get('Activity Date', '').strip(),
                        "activity_type": row.get('Activity Type', '').strip(),
                        "plain_text": row.get('Plain Text', '').strip()
                    }
                    self.activities.append(activity)

            logger.info(f"📊 Loaded {len(self.activities)} test activities")
            return True

        except Exception as e:
            logger.error(f"❌ Failed to load test activities: {e}")
            return False

    def convert_date_format(self, date_string):
        """Convert date format"""
        try:
            if not date_string or date_string.strip() == '':
                return None, None

            dt = datetime.strptime(date_string.strip(), '%Y-%m-%d %H:%M:%S')
            gainsight_date = f"{dt.month}/{dt.day}/{dt.year}"
            gainsight_time = dt.strftime('%I:%M %p').lstrip('0')

            return gainsight_date, gainsight_time

        except Exception as e:
            logger.error(f"❌ Date conversion failed for '{date_string}': {e}")
            return None, None

    def create_test_activity(self, page, activity_data, row_index):
        """Create a single test activity"""
        try:
            subject = activity_data["subject"]
            logger.info(f"📝 Creating test activity {row_index}: {subject[:50]}...")

            # Step 1: Click "Add Activity" button
            page.wait_for_selector('button:has-text("Add Activity")', timeout=10000)
            page.click('button:has-text("Add Activity")')
            time.sleep(3)
            logger.info("✅ Add Activity button clicked")

            # Step 2: Fill Subject
            page.wait_for_selector('textbox[placeholder*="subject" i]', timeout=5000)
            page.fill('textbox[placeholder*="subject" i]', subject)
            logger.info(f"✅ Subject filled: {subject}")

            # Step 3: Fill Date
            gainsight_date, gainsight_time = self.convert_date_format(activity_data["activity_date"])
            if gainsight_date:
                page.wait_for_selector('textbox[placeholder*="Select date"]', timeout=5000)
                page.fill('textbox[placeholder*="Select date"]', gainsight_date)
                time.sleep(1)
                logger.info(f"✅ Date filled: {gainsight_date}")

            # Step 4: Fill Time
            if gainsight_time:
                page.wait_for_selector('textbox[placeholder*="hh:mm"]', timeout=5000)
                page.fill('textbox[placeholder*="hh:mm"]', gainsight_time)
                time.sleep(1)
                logger.info(f"✅ Time filled: {gainsight_time}")

            # Step 5: Select Activity Type
            activity_type = activity_data["activity_type"]
            if activity_type:
                page.wait_for_selector('label:has-text("Activity Type") + div img', timeout=5000)
                page.click('label:has-text("Activity Type") + div img')
                time.sleep(2)
                logger.info("✅ Activity Type dropdown opened")

                page.wait_for_selector(f'text="{activity_type}"', timeout=5000)
                page.click(f'text="{activity_type}"')
                time.sleep(1)
                logger.info(f"✅ Activity Type selected: {activity_type}")

                # Handle confirmation if needed
                try:
                    if page.locator('button:has-text("Yes")').is_visible(timeout=3000):
                        page.click('button:has-text("Yes")')
                        time.sleep(2)
                        logger.info("✅ Activity type change confirmed")
                except:
                    pass

            # Step 6: Fill Notes
            plain_text = activity_data["plain_text"]
            if plain_text:
                page.wait_for_selector('iframe', timeout=10000)
                iframe = page.frame_locator('iframe').first
                note_textbox = iframe.locator('textbox').first
                note_textbox.click()
                note_textbox.fill(plain_text)
                time.sleep(1)
                logger.info("✅ Notes filled")

            # Step 7: Save Activity
            page.wait_for_selector('button:has-text("Log Activity")', timeout=10000)
            page.click('button:has-text("Log Activity")')
            logger.info("✅ Log Activity clicked")

            # Wait for success
            try:
                page.wait_for_selector('text="Activity saved successfully"', timeout=15000)
                logger.info(f"✅ Test activity saved: {subject[:30]}...")
            except:
                time.sleep(3)
                logger.info(f"✅ Test activity completed: {subject[:30]}...")

            time.sleep(2)
            return True

        except Exception as e:
            logger.error(f"❌ Test activity failed: {e}")
            page.screenshot(path=f"/Users/<USER>/Desktop/wild_weasel_gainsight_migration/test_error_{row_index}.png")
            return False

    def run_test(self):
        """Run the test"""
        try:
            logger.info("🐺 Starting UI Automation Test...")

            if not self.load_test_activities():
                return False

            with sync_playwright() as playwright:
                browser = playwright.chromium.launch(headless=False)
                context = browser.new_context(viewport={'width': 1920, 'height': 1080})
                page = context.new_page()

                # Navigate directly to the timeline
                page.goto(self.config["target_url"])
                page.wait_for_load_state("networkidle", timeout=30000)
                time.sleep(5)

                # Test each activity
                successful = 0
                for i, activity in enumerate(self.activities):
                    if self.create_test_activity(page, activity, i+1):
                        successful += 1
                    time.sleep(3)  # Pause between activities

                logger.info(f"🎯 Test Results: {successful}/{len(self.activities)} successful")

                # Keep browser open for inspection
                input("Press Enter to close browser...")
                browser.close()

                return successful == len(self.activities)

        except Exception as e:
            logger.error(f"❌ Test failed: {e}")
            return False

def main():
    """Main test function"""
    test = TestUIAutomation()
    success = test.run_test()

    if success:
        print("🎉 Test PASSED - UI automation is working!")
    else:
        print("❌ Test FAILED - Check logs for issues")

if __name__ == "__main__":
    main()

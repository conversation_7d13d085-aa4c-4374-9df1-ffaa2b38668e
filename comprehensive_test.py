#!/usr/bin/env python3
"""
Comprehensive Test Suite for Enhanced Browser Automation
Tests multi-LLM functionality, ICICI migration, and system integration
"""

import asyncio
import json
import logging
import time
from pathlib import Path
from typing import Dict, List, Any, Optional
import requests

# Setup logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class ComprehensiveTestSuite:
    """
    Complete test suite for the enhanced browser automation system
    """
    
    def __init__(self):
        self.config = None
        self.test_results = {}
        self.start_time = time.time()
    
    async def run_all_tests(self):
        """Run all test categories"""
        print("🚀 COMPREHENSIVE SYSTEM TEST SUITE")
        print("=" * 60)
        
        try:
            # Import config after potential fixes
            from config import config
            self.config = config
            
            # Test 1: Configuration validation
            await self.test_configuration()
            
            # Test 2: Multi-LLM client functionality  
            await self.test_llm_client()
            
            # Test 3: ICICI data migration
            await self.test_icici_migration()
            
            # Test 4: Browser automation integration
            await self.test_browser_integration()
            
            # Test 5: API connectivity
            await self.test_api_connectivity()
            
            # Test 6: End-to-end workflow
            await self.test_end_to_end_workflow()
            
            # Generate final report
            self.generate_test_report()
            
        except Exception as e:
            logger.error(f"Test suite failed: {e}")
            print(f"❌ Test suite failed: {e}")
    
    async def test_configuration(self):
        """Test configuration validation and API key verification"""
        print("\n🔧 Testing Configuration...")
        test_name = "configuration"
        
        try:
            # Test config validation
            is_valid = self.config.validate()
            
            # Test API key validation
            api_status = self.config.get_api_config_for_model("meta-llama/llama-4-maverick:free")
            
            # Test model selection
            coding_model = self.config.get_model_for_task("coding")
            reasoning_model = self.config.get_model_for_task("reasoning")
            
            self.test_results[test_name] = {
                "status": "PASSED",
                "details": {
                    "config_valid": is_valid,
                    "api_config_available": bool(api_status),
                    "coding_model": coding_model,
                    "reasoning_model": reasoning_model
                }
            }
            
            print(f"  ✅ Configuration valid: {is_valid}")
            print(f"  ✅ Primary coding model: {coding_model}")
            print(f"  ✅ Primary reasoning model: {reasoning_model}")
            
        except Exception as e:
            self.test_results[test_name] = {
                "status": "FAILED",
                "error": str(e)
            }
            print(f"  ❌ Configuration test failed: {e}")
    
    async def test_llm_client(self):
        """Test the enhanced LLM client with multiple providers"""
        print("\n🤖 Testing Enhanced LLM Client...")
        test_name = "llm_client"
        
        try:
            # Import and test LLM client
            from enhanced_llm_client import EnhancedLLMClient
            
            async with EnhancedLLMClient() as client:
                # Test coding task
                coding_result = await client.complete(
                    "Write a simple Python function that adds two numbers",
                    task_type="coding",
                    max_tokens=200
                )
                
                # Test reasoning task
                reasoning_result = await client.complete(
                    "What are the key steps to migrate data between systems?",
                    task_type="reasoning", 
                    max_tokens=300
                )
                
                self.test_results[test_name] = {
                    "status": "PASSED",
                    "details": {
                        "coding_success": coding_result.success,
                        "coding_model": coding_result.model,
                        "coding_provider": coding_result.provider,
                        "coding_latency": coding_result.latency,
                        "reasoning_success": reasoning_result.success,
                        "reasoning_model": reasoning_result.model,
                        "reasoning_provider": reasoning_result.provider,
                        "reasoning_latency": reasoning_result.latency
                    }
                }
                
                print(f"  ✅ Coding task - Model: {coding_result.model}, Success: {coding_result.success}")
                print(f"  ✅ Reasoning task - Model: {reasoning_result.model}, Success: {reasoning_result.success}")
                
        except Exception as e:
            self.test_results[test_name] = {
                "status": "FAILED", 
                "error": str(e)
            }
            print(f"  ❌ LLM client test failed: {e}")
    
    async def test_icici_migration(self):
        """Test ICICI to Gainsight data migration"""
        print("\n📊 Testing ICICI Migration...")
        test_name = "icici_migration"
        
        try:
            # Check if data files exist
            icici_path = Path(self.config.integration.icici_data_path)
            id_path = Path(self.config.integration.id_mapping_path)
            gainsight_path = Path(self.config.integration.gainsight_payload_path)
            
            files_exist = {
                "icici_data": icici_path.exists(),
                "id_mapping": id_path.exists(),
                "gainsight_template": gainsight_path.exists()
            }
            
            # Load and analyze data if files exist
            data_analysis = {}
            if files_exist["icici_data"]:
                with open(icici_path, 'r') as f:
                    icici_data = json.load(f)
                    data_analysis["icici_records"] = len(icici_data) if isinstance(icici_data, list) else 1
                    data_analysis["icici_sample_keys"] = list(icici_data[0].keys()) if isinstance(icici_data, list) and icici_data else []
            
            if files_exist["id_mapping"]:
                with open(id_path, 'r') as f:
                    id_data = json.load(f)
                    data_analysis["id_mappings"] = len(id_data) if isinstance(id_data, list) else len(id_data) if isinstance(id_data, dict) else 0
            
            # Check if conversion has been done
            converted_file = Path("/Users/<USER>/Desktop/wildweasel/Browser/data/icici_gainsight_ready.json")
            conversion_done = converted_file.exists()
            
            self.test_results[test_name] = {
                "status": "PASSED" if all(files_exist.values()) else "PARTIAL",
                "details": {
                    "files_exist": files_exist,
                    "data_analysis": data_analysis,
                    "conversion_done": conversion_done
                }
            }
            
            print(f"  ✅ ICICI data file: {files_exist['icici_data']}")
            print(f"  ✅ ID mapping file: {files_exist['id_mapping']}")
            print(f"  ✅ Gainsight template: {files_exist['gainsight_template']}")
            if conversion_done:
                print(f"  ✅ Conversion already completed")
            
        except Exception as e:
            self.test_results[test_name] = {
                "status": "FAILED",
                "error": str(e)
            }
            print(f"  ❌ ICICI migration test failed: {e}")
    
    async def test_browser_integration(self):
        """Test browser automation capabilities"""
        print("\n🌐 Testing Browser Integration...")
        test_name = "browser_integration"
        
        try:
            # Check if browser automation files exist
            browser_files = [
                "browser_automation/enhanced_agent.py",
                "orchestrator.py",
                "main.py"
            ]
            
            files_status = {}
            for file_path in browser_files:
                full_path = Path(f"/Users/<USER>/Desktop/wildweasel/Browser/{file_path}")
                files_status[file_path] = full_path.exists()
            
            # Check if we can import key modules
            import_status = {}
            try:
                import orchestrator
                import_status["orchestrator"] = True
            except:
                import_status["orchestrator"] = False
            
            self.test_results[test_name] = {
                "status": "PASSED" if all(files_status.values()) else "PARTIAL",
                "details": {
                    "files_status": files_status,
                    "import_status": import_status
                }
            }
            
            print(f"  ✅ Browser automation files available: {sum(files_status.values())}/{len(files_status)}")
            print(f"  ✅ Core modules importable: {sum(import_status.values())}/{len(import_status)}")
            
        except Exception as e:
            self.test_results[test_name] = {
                "status": "FAILED",
                "error": str(e)
            }
            print(f"  ❌ Browser integration test failed: {e}")
    
    async def test_api_connectivity(self):
        """Test API connectivity to various services"""
        print("\n🔗 Testing API Connectivity...")
        test_name = "api_connectivity"
        
        connectivity_results = {}
        
        # Test OpenRouter API
        try:
            headers = {
                "Authorization": f"Bearer {self.config.llm.openrouter_api_key}",
                "Content-Type": "application/json"
            }
            response = requests.get(
                "https://openrouter.ai/api/v1/models",
                headers=headers,
                timeout=10
            )
            connectivity_results["openrouter"] = response.status_code == 200
        except:
            connectivity_results["openrouter"] = False
        
        # Test HuggingFace API
        try:
            headers = {
                "Authorization": f"Bearer {self.config.llm.huggingface_api_key}"
            }
            response = requests.get(
                "https://api-inference.huggingface.co/models/microsoft/DialoGPT-medium",
                headers=headers,
                timeout=10
            )
            connectivity_results["huggingface"] = response.status_code in [200, 503]  # 503 is model loading
        except:
            connectivity_results["huggingface"] = False
        
        # Test Gainsight API (if API key available)
        gainsight_key = getattr(self.config.integration, 'gainsight_api_key', None)
        if gainsight_key:
            try:
                headers = {
                    "Authorization": f"Bearer {gainsight_key}",
                    "Content-Type": "application/json"
                }
                response = requests.get(
                    self.config.integration.gainsight_base_url,
                    headers=headers,
                    timeout=10
                )
                connectivity_results["gainsight"] = response.status_code < 500
            except:
                connectivity_results["gainsight"] = False
        else:
            connectivity_results["gainsight"] = "No API key configured"
        
        self.test_results[test_name] = {
            "status": "PASSED" if connectivity_results.get("openrouter", False) else "PARTIAL",
            "details": connectivity_results
        }
        
        for service, status in connectivity_results.items():
            if isinstance(status, bool):
                print(f"  {'✅' if status else '❌'} {service.title()}: {'Connected' if status else 'Failed'}")
            else:
                print(f"  ⚠️  {service.title()}: {status}")
    
    async def test_end_to_end_workflow(self):
        """Test a complete end-to-end workflow"""
        print("\n🔄 Testing End-to-End Workflow...")
        test_name = "end_to_end"
        
        try:
            workflow_steps = []
            
            # Step 1: LLM task completion
            from enhanced_llm_client import EnhancedLLMClient
            async with EnhancedLLMClient() as client:
                result = await client.complete(
                    "Generate a JSON template for activity migration with fields: id, type, date, description",
                    task_type="coding",
                    max_tokens=300
                )
                workflow_steps.append({
                    "step": "LLM_Generation",
                    "success": result.success,
                    "model": result.model
                })
            
            # Step 2: Data processing simulation
            try:
                # Simulate data processing
                sample_data = {
                    "activities": [
                        {"id": 1, "type": "meeting", "date": "2025-01-01", "description": "Test meeting"}
                    ]
                }
                workflow_steps.append({
                    "step": "Data_Processing", 
                    "success": True,
                    "data_count": len(sample_data["activities"])
                })
            except Exception as e:
                workflow_steps.append({
                    "step": "Data_Processing",
                    "success": False,
                    "error": str(e)
                })
            
            # Step 3: Configuration access
            try:
                model = self.config.get_model_for_task("data_migration")
                workflow_steps.append({
                    "step": "Config_Access",
                    "success": True,
                    "selected_model": model
                })
            except Exception as e:
                workflow_steps.append({
                    "step": "Config_Access",
                    "success": False,
                    "error": str(e)
                })
            
            total_success = all(step.get("success", False) for step in workflow_steps)
            
            self.test_results[test_name] = {
                "status": "PASSED" if total_success else "PARTIAL",
                "details": {
                    "workflow_steps": workflow_steps,
                    "total_steps": len(workflow_steps),
                    "successful_steps": sum(1 for step in workflow_steps if step.get("success", False))
                }
            }
            
            print(f"  ✅ Workflow steps completed: {sum(1 for step in workflow_steps if step.get('success', False))}/{len(workflow_steps)}")
            for step in workflow_steps:
                status = "✅" if step.get("success", False) else "❌"
                print(f"    {status} {step['step']}")
                
        except Exception as e:
            self.test_results[test_name] = {
                "status": "FAILED",
                "error": str(e)
            }
            print(f"  ❌ End-to-end workflow test failed: {e}")
    
    def generate_test_report(self):
        """Generate comprehensive test report"""
        print("\n📋 TEST REPORT")
        print("=" * 60)
        
        total_tests = len(self.test_results)
        passed_tests = sum(1 for result in self.test_results.values() if result["status"] == "PASSED")
        partial_tests = sum(1 for result in self.test_results.values() if result["status"] == "PARTIAL") 
        failed_tests = sum(1 for result in self.test_results.values() if result["status"] == "FAILED")
        
        print(f"📊 Test Summary:")
        print(f"   • Total Tests: {total_tests}")
        print(f"   • ✅ Passed: {passed_tests}")
        print(f"   • ⚠️  Partial: {partial_tests}")
        print(f"   • ❌ Failed: {failed_tests}")
        print(f"   • Success Rate: {(passed_tests/total_tests)*100:.1f}%")
        
        print(f"\n🔍 Detailed Results:")
        for test_name, result in self.test_results.items():
            status_emoji = {"PASSED": "✅", "PARTIAL": "⚠️", "FAILED": "❌"}[result["status"]]
            print(f"   {status_emoji} {test_name.upper()}: {result['status']}")
            
            if "details" in result:
                for key, value in result["details"].items():
                    print(f"      • {key}: {value}")
            
            if "error" in result:
                print(f"      • Error: {result['error']}")
        
        # System readiness assessment
        print(f"\n🎯 System Readiness Assessment:")
        if passed_tests >= 4:
            print("   ✅ System is ready for production use")
            print("   ✅ Multi-LLM integration functional")
            print("   ✅ ICICI migration capabilities confirmed")
        elif passed_tests >= 2:
            print("   ⚠️  System partially ready - some features may need attention")
            print("   ✅ Core functionality available")
        else:
            print("   ❌ System needs significant fixes before use")
        
        # Recommendations
        print(f"\n💡 Recommendations:")
        if failed_tests > 0:
            print("   1. Address failed test cases before production deployment")
        if partial_tests > 0:
            print("   2. Complete partial implementations for full functionality")
        print("   3. Run ICICI migration test with actual data")
        print("   4. Configure Gainsight API key for complete testing")
        
        # Performance metrics
        total_time = time.time() - self.start_time
        print(f"\n⏱️  Test Execution Time: {total_time:.2f} seconds")
        
        # Save report to file
        report_path = Path("/Users/<USER>/Desktop/wildweasel/Browser/data/test_report.json")
        report_path.parent.mkdir(exist_ok=True)
        
        with open(report_path, 'w') as f:
            json.dump({
                "summary": {
                    "total_tests": total_tests,
                    "passed": passed_tests,
                    "partial": partial_tests,
                    "failed": failed_tests,
                    "success_rate": (passed_tests/total_tests)*100,
                    "execution_time": total_time
                },
                "results": self.test_results,
                "timestamp": time.time()
            }, f, indent=2)
        
        print(f"   📄 Full report saved to: {report_path}")

async def main():
    """Run the comprehensive test suite"""
    test_suite = ComprehensiveTestSuite()
    await test_suite.run_all_tests()

if __name__ == "__main__":
    asyncio.run(main())
